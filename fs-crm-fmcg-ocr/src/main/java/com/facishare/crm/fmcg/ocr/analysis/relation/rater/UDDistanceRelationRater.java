package com.facishare.crm.fmcg.ocr.analysis.relation.rater;

import com.facishare.crm.fmcg.ocr.analysis.Location;
import com.facishare.crm.fmcg.ocr.analysis.relation.RatedBlockRelation;
import com.facishare.crm.fmcg.ocr.analysis.relation.RelationRater;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2023/2/23 10:20
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class UDDistanceRelationRater extends RelationRater {

    public static final int BASE_SCORE = 100;

    private double distance(Location a, Location b) {
        // a center
        double x1 = a.getLeft() + (a.getWidth() / 2);
        double y1 = a.getTop() + (a.getHeight() / 2);

        // b center
        double x2 = b.getLeft() + (b.getWidth() / 2);
        double y2 = b.getTop() + (b.getHeight() / 2);

        // calculate distance
        return Math.sqrt(Math.abs((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2)));
    }

    @Override
    public void rate(int width, int height, List<RatedBlockRelation> relations) {
        List<RelationWithDistance> data = Lists.newArrayListWithCapacity(relations.size());

        for (RatedBlockRelation relation : relations) {
            double distance = distance(relation.getSource().getLocation(), relation.getTarget().getLocation());
            data.add(RelationWithDistance.builder().relation(relation).distance(distance).build());
        }

        data = data.stream().sorted(Comparator.comparing(RelationWithDistance::getDistance)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        double base = data.get(0).getDistance();
        for (RelationWithDistance datum : data) {
            int maxHeight = Math.max((int) datum.getRelation().getSource().getLocation().getHeight(), (int) datum.getRelation().getTarget().getLocation().getHeight());
            if (datum.getDistance() < maxHeight * 4) {
                datum.getRelation().increase((int) (BASE_SCORE * base / datum.getDistance()));
            } else {
                datum.getRelation().increase(-BASE_SCORE * 4);
            }
        }
    }

    @Getter
    @Builder
    static class RelationWithDistance {

        private final RatedBlockRelation relation;

        private final double distance;
    }
}