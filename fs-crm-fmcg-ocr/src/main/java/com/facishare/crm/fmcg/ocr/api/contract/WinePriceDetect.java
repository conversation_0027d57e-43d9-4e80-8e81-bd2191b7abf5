package com.facishare.crm.fmcg.ocr.api.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.ocr.analysis.relation.RatedBlockRelation;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface WinePriceDetect {

    @Data
    @ToString
    class Arg implements Serializable {

        private String path;
    }

    @Data
    @ToString
    class Result implements Serializable {

        private List<WinePriceDatum> data;

        public static Result of(List<RatedBlockRelation> relations) {
            Result result = new Result();
            result.setData(Lists.newArrayList());

            for (RatedBlockRelation relation : relations) {
                WinePriceDatum datum = new WinePriceDatum();
                datum.setName(relation.getSource().getWords());
                datum.setPrice(relation.getTarget().getWords());
                result.getData().add(datum);
            }

            return result;
        }
    }

    @Data
    @ToString
    class WinePriceDatum implements Serializable {

        private String name;

        private String price;

        @SerializedName("numeric_price")
        @JSONField(name = "numeric_price")
        @JsonProperty("numeric_price")
        private String numericPrice;
    }
}
