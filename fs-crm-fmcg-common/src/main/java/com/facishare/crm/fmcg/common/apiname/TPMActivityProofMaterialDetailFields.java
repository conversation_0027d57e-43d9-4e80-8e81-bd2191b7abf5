package com.facishare.crm.fmcg.common.apiname;

import java.util.List;

import com.google.common.collect.Lists;

public abstract class TPMActivityProofMaterialDetailFields {

    private TPMActivityProofMaterialDetailFields() {
    }

    public static final String MATERIAL_CATEGORY = "material_category";

    public static final String AI_NUMBER = "ai_number";

    public static final String NUMBER_STANDARD = "number_standard";

    public static final String MATERIAL_ID = "material_id";

    public static final String ACTIVITY_PROOF_DISPLAY_IMG_ID = "activity_proof_display_img_id";

    public static final String ACTIVITY_PROOF_ID = "activity_proof_id";

    public static final String DISPLAY_FORM_ID = "display_form_id";

    public static final List<String> ALL = Lists.newArrayList(
            MATERIAL_CATEGORY,
            AI_NUMBER,
            NUMBER_STANDARD,
            MATERIAL_ID,
            ACTIVITY_PROOF_DISPLAY_IMG_ID,
            ACTIVITY_PROOF_ID,
            DISPLAY_FORM_ID
    );
}