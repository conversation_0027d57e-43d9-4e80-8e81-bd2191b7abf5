package com.facishare.crm.fmcg.common.apiname;

public abstract class EnterpriseFundAccountAdjustmentFields {

    private EnterpriseFundAccountAdjustmentFields() {
    }

    public static final String EXPENDITURE_ACCOUNT = "expenditure_account";

    public static final String EXPENSE_TYPE = "expense_type";

    public static final String SERVICE_CHARGE = "service_charge";

    public static final String REVENUE_AMOUNT = "revenue_amount";

    public static final String REVENUE_ACCOUNT = "revenue_account";

    public static final String EXPENSE_AMOUNT = "expense_amount";

    public static final String TRANSFER_AMOUNT = "transfer_amount";

    public static final String REVENUE_TYPE = "revenue_type";

    public static final String REMARKS = "remarks";

    public static final String ADJUSTMENT_DATE = "adjustment_date";

    public static class LayoutApiName {
        public static final String REVENUE = "layout_default_EnterpriseFundAccountAdjustmentObj__c";
        public static final String EXPENDITURE = "layout_other_expenditure_document__c";
        public static final String TRANSFER = "layout_bank_transfer_document__c";
    }

    public static class RecordTypeApiName {
        public static final String REVENUE = "default__c";
        public static final String REVENUE_NAME = "其他收入单";//ignorei18n
        public static final String EXPENDITURE = "other_expenditure_document__c";
        public static final String EXPENDITURE_NAME = "其他支出单";//ignorei18n
        public static final String TRANSFER = "bank_transfer_slip__c";
        public static final String TRANSFER_NAME = "银行转款单";//ignorei18n
    }
}