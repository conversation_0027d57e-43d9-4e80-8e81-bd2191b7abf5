package com.facishare.crm.fmcg.common.apiname;

/**
 * @description:
 * @author: yangyx
 * @date: 2022-06-16 17:34
 **/

public abstract class SalesOrderProductFields {
    private SalesOrderProductFields() {
    }

    public static final String BATCH_ID = "batch_id__c";
    public static final String BATCH_STOCK_ID = "batch_stock_id__c";
    public static final String PRODUCT_FRESHNESS = "product_freshness__c";
    public static final String SALES_TYPE = "sales_type";
    public static final String BILLING_LABEL = "billing_label";
    public static final String PRODUCT_ID = "product_id";
    public static final String SALES_PRICE = "sales_price";
    public static final String ORDER_ID = "order_id";
    public static final String AMORTIZE_SUBTOTAL = "amortize_subtotal";
    public static final String QUANTITY = "quantity";
    public static final String PRICE_BOOK_SUBTOTAL = "price_book_subtotal";
    public static final String FIELD_P9J9L = "field_P9J9l__c";
    public static final String SUBTOTAL = "subtotal";
    public static final String CONVERSION_RATIO = "conversion_ratio";
}
