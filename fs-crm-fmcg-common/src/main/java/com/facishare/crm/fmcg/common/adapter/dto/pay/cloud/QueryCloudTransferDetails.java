package com.facishare.crm.fmcg.common.adapter.dto.pay.cloud;

import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/8/1 11:50
 */
public interface QueryCloudTransferDetails {

    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * 优先级最高 批量转账id
         */
        private String batchTransferId;

        /**
         * 单条转账Id
         */
        private String transferId;

        /**
         * 业务自定义id
         */
        private String businessId;




    }


    @Data
    @ToString
    class Result implements Serializable {

        private String batchTransferId;

        private String transferId;

        private String businessId;

        private List<TransferDetail> transferDetails;
    }
}
