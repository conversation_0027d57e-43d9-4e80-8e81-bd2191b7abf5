package com.facishare.crm.fmcg.common.adapter.dto.pay.cloud;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Author: linmj
 * Date: 2023/8/1 10:33
 */
@Data
@ToString
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class WXCloudPayReceiverAccount extends BaseCloudPayReceiverAccount {

    /**
     * 必填 微信openId
     */
    private String openId;

    /**
     * 选填 微信应用id
     */
    private String appId;

    /**
     * 选填 微信支付模式
     */
    private String payMode;

    public WXCloudPayReceiverAccount(String realName, String idCard, String phoneNumber, String openId) {
        super(realName, idCard, phoneNumber);
        this.openId = openId;
    }
}
