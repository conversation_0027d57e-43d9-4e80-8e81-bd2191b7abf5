package com.facishare.crm.fmcg.common.apiname;

public abstract class TPMActivityProductRangeFields {

	public static final String UNIT = "unit";

	public static final String ACTIVITY_TOTAL_PRICE = "activity_total_price";

	public static final String PRICE = "price";

	public static final String PRODUCT_ID = "product_id";

	public static final String ACTIVITY_ID = "activity_id";

	public static final String ACTIVITY_QUANTITY = "activity_quantity";

	public static final String CONSUMER_RETAIL_PRICE = "consumer_retail_price";

	public static final String ACTIVITY_DEDUCT_AMOUNT = "activity_deduct_amount";

	public static final String TO_BE_EXPIRED_DAYS = "to_be_expired_days";

	/**
	 * 匹配模式
	 * 0 : 临期匹配 - 设置产品剩余有效期20天，那么“临期匹配”模式会匹配那些剩余有效期小于20天的码参与活动
	 * 1 : 新货匹配 - 设置产品剩余有效期20天，那么“新货匹配”模式会匹配那些剩余有效期大于20天的码参与活动
	 */
	public static final String MATCH_METHOD = "match_method";
	public static final String MATCH_METHOD__BIG_DATE = "0";
	public static final String MATCH_METHOD__NEW_GOODS = "1";

	public static final String MANUFACTURE_DATE_START = "manufacture_date_start";

	public static final String MANUFACTURE_DATE_END = "manufacture_date_end";

}