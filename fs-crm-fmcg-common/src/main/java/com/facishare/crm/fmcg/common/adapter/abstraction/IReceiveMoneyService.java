package com.facishare.crm.fmcg.common.adapter.abstraction;

import com.facishare.crm.fmcg.common.adapter.dto.receipts.CloseWXOrder;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.FormWxPayMini;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.QueryEnterpriseUnionISV;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.QueryTransferDetailForReceipts;

/**
 * Author: linmj
 * Date: 2023/9/12 18:36
 */
public interface IReceiveMoneyService {

    FormWxPayMini.Result formWxPayMini( FormWxPayMini.Arg arg);

    QueryTransferDetailForReceipts.Result queryTransferDetailForReceipts( QueryTransferDetailForReceipts.Arg arg);

    CloseWXOrder.Result closeWXOrder(CloseWXOrder.Arg arg);

    QueryEnterpriseUnionISV.Result queryEnterpriseUnionISV(QueryEnterpriseUnionISV.Arg arg);
}
