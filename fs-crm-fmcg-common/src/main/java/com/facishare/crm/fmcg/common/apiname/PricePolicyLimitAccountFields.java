package com.facishare.crm.fmcg.common.apiname;

public abstract class PricePolicyLimitAccountFields {
    private PricePolicyLimitAccountFields(){}
    public static final String LIMIT_TYPE = "limit_type";
    public static final String LIMIT_OBJ_TYPE = "limit_obj_type";
    public static final String PRICE_POLICY_RULE_ID = "price_policy_rule_id";
    public static final String PRICE_POLICY_ID = "price_policy_id";
    public static final String RANGE = "range";
    public static final String REMARK = "remark";
    public static final String GIFT_TYPE = "gift_type";
    public static final String LIMIT_NUMBER = "limit_number";
    public static final String ACCOUNT_ID = "account_id";
    public static final String DIMENSION = "dimension";
    public static final String ACCOUNT_MODE = "account_mode";
    public static final String ACTIVITY_ID = "activity_id";

    public static final String DIMENSION_GIFT = "gift";
    public static final String DIMENSION_SELF = "self";

    public static final String GIFT_TYPE_FIXED = "FIXED";
    public static final String GIFT_TYPE_ALL = "ALL";

    public static final String LIMIT_OBJ_TYPE_ALL = "all";
    public static final String LIMIT_OBJ_TYPE_ACCOUNT = "account";

    public static final String LIMIT_TYPE_GIFT_AMOUNT = "GIFT_AMOUNT";
    public static final String LIMIT_TYPE_ORIGINAL_AMOUNT = "ORIGINAL_AMOUNT";

    public static final String RANGE_POLICY = "POLICY";
    public static final String RANGE_RULE = "RULE";
    public static final String RANGE_MULTI_POLICY = "MULTI_POLICY";

    public static final String ACCOUNT_MODE_EQUAL = "EQUAL";
    public static final String ACCOUNT_MODE_DIFFERENCE = "DIFFERENCE";
    public static final String PRICE_POLICY_IDS = "price_policy_ids";
    /*
     * RIO 自定义活动方案ID
     * */
    public static final String RIO_ACTIVITY_ID = "field_pxe07__c";
}
