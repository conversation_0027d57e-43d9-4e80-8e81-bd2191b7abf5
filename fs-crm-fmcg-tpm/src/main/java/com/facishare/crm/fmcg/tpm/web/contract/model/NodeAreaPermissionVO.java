package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @author: wuyx
 * @description: TODO
 * @createTime: 2021/12/24 19:20
 */
@Data
@ToString
@Builder
public class NodeAreaPermissionVO implements Serializable {
    @JSONField(name = "enable_edit_areas")
    @JsonProperty(value = "enable_edit_areas")
    @SerializedName("enable_edit_areas")
    private List<String> enableEditAreas;

    @JSONField(name = "un_enable_edit_areas")
    @JsonProperty(value = "un_enable_edit_areas")
    @SerializedName("un_enable_edit_areas")
    private List<String> unEnableEditAreas;
}
