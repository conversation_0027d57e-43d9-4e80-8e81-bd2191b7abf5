package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityStoreWriteOffConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class ActivityStoreWriteOffConfigVO implements Serializable {

    @JSONField(name = "store_write_off_source_config")
    @JsonProperty(value = "store_write_off_source_config")
    @SerializedName("store_write_off_source_config")
    private ActivityStoreWriteOffSourceConfigVO storeWriteOffSourceConfig;

    public static ActivityStoreWriteOffConfigVO fromPO(ActivityStoreWriteOffConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityStoreWriteOffConfigVO vo = new ActivityStoreWriteOffConfigVO();
        vo.setStoreWriteOffSourceConfig(ActivityStoreWriteOffSourceConfigVO.fromPO(po.getStoreWriteOffSourceConfig()));
        return vo;
    }
}
