<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.crm.fmcg.tpm.dao.paas.BudgetAccountDetailMapper">

    <select id="statisticMoney" resultType="java.util.Map">
        select
            *,
            income + expenditure - frozen_amount available_amount,
            -expenditure used_amount,
            transfer_in_amount - transfer_out_amount + calculate_apply_amount + base_amount total_amount
        from
            (
            select
                sum(case main_type when 'income' then coalesce(amount, 0) else 0 end ) income,
                sum(case main_type when 'expenditure' then coalesce(-amount, 0) else 0 end ) expenditure,
                sum(case main_type when 'freeze' then coalesce(amount, 0) when 'unfreeze' then coalesce(-amount, 0) else 0 end ) frozen_amount,
                sum(case business_type when 'accrual' then coalesce(amount, 0) else 0 end ) calculate_apply_amount,
                sum(case business_type when 'transfer_append' then coalesce(amount, 0) when 'transfer_in' then coalesce(amount, 0)  when 'take_apart_in' then coalesce(amount, 0)  when 'carry_over_in' then coalesce(amount, 0) else 0 end ) transfer_in_amount,
                sum(case business_type when 'transfer_deduction' then coalesce(amount, 0) when 'transfer_out' then coalesce(amount, 0)  when 'take_apart_out' then coalesce(amount, 0)  when 'carry_over_out' then coalesce(amount, 0) else 0 end ) transfer_out_amount,
                sum(case  when main_type = 'income' and business_type = 'init_money' then coalesce(amount, 0) else 0 end ) base_amount
            from
                fmcg_tpm_budget_account_detail
            where
                tenant_id = #{tenant_id} and budget_account_id = #{budget_account_id}
                and is_deleted = 0 and (detail_status is null or detail_status = 'include')
            ) amount_table

    </select>


</mapper>