package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityAgreementConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 18:08
 */
@Data
@ToString
public class ActivityAgreementConfigEntity implements Serializable {

    @Embedded("signing_mode")
    private String signingMode;

    @Embedded("begin_date_setting")
    private ActivityAgreementTimeSettingEntity beginTimeSetting;

    @Embedded("end_date_setting")
    private ActivityAgreementTimeSettingEntity endTimeSetting;

    public static ActivityAgreementConfigEntity fromVO(ActivityAgreementConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityAgreementConfigEntity po = new ActivityAgreementConfigEntity();
        po.setSigningMode(vo.getSigningMode());
        po.setBeginTimeSetting(ActivityAgreementTimeSettingEntity.fromVO(vo.getBeginTimeSetting()));
        po.setEndTimeSetting(ActivityAgreementTimeSettingEntity.fromVO(vo.getEndTimeSetting()));
        return po;
    }
}
