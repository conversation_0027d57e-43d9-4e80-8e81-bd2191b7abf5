package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.metadata.importobject.ImportType;

/**
 * author: wuyx
 * description:
 * createTime: 2022/7/13 11:30
 */
public class TPMBudgetStatisticTableObjImportObjectController extends StandardImportObjectController {

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("init TPMBudgetStatisticTableObjImportObjectController after");
        Result after = super.after(arg, result);
        after.getImportObject().setSupportType(ImportType.UNSUPPORT_INSERT_IMPORT);
        return after;
    }
}
