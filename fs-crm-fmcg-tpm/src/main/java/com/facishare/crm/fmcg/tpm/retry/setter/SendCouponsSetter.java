package com.facishare.crm.fmcg.tpm.retry.setter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandlerEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2024/1/30 19:58
 */
//IgnoreI18nFile
@Component
public class SendCouponsSetter extends BaseSetter {


    public void setUpdateStatusTask(String tenantId, String downstreamTenantId, String objectId, String dealerId, long month, List<String> feeTypes, JSONObject extraData, long nextExecuteTime) {
        String name = String.format("发放优惠券任务_%s_%s_%s", objectId, downstreamTenantId, month);
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", tenantId);
        params.put("objectId", objectId);
        params.put("downstreamTenantId", downstreamTenantId);
        params.put("dealerId", dealerId);
        params.put("month", month);
        params.put("feeTypes", feeTypes);
        params.put("extraData", extraData);
        String taskTenantId = getTenantId(tenantId);

        setInit(taskTenantId, name, RetryHandlerEnum.SEND_COUPONS_HANDLER.code(), JSON.toJSONString(params), 3, nextExecuteTime);
    }
}
