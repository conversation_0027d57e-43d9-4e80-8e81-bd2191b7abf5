package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityWriteOffSourceConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 19:42
 */
@Data
@ToString
public class ActivityWriteOffSourceConfigEntity implements Serializable {

    @Property("template_id")
    private String templateId;

    /**
     * 核销计算策略
     */
    @Property("calculate_type")
    private String calculateType;

    /**
     * 核销来源对象
     */
    @Property("api_name")
    private String apiName;

    @Property("record_type")
    private String recordType;

    @Property("reference_write_off_field_api_name")
    private String referenceWriteOffFieldApiName;

    /**
     * 经销商字段
     */
    @Property("dealer_field_api_name")
    private String dealerFieldApiName;

    @Property("reference_activity_field_api_name")
    private String referenceActivityFieldApiName;

    /**
     * 客户字段
     */
    @Property("account_field_api_name")
    private String accountFieldApiName;

    /**
     * 费用字段
     */
    @Property("cost_field_api_name")
    private String costFieldApiName;

    /**
     * 展示的字段列表
     */
    @Embedded("display_field_api_names")
    private List<String> displayFieldApiNames;

    public static ActivityWriteOffSourceConfigEntity fromVO(ActivityWriteOffSourceConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityWriteOffSourceConfigEntity po = new ActivityWriteOffSourceConfigEntity();
        po.setTemplateId(vo.getTemplateId());
        po.setApiName(vo.getApiName());
        po.setRecordType(vo.getRecordType());
        po.setCalculateType(vo.getCalculateType());
        po.setReferenceActivityFieldApiName(vo.getReferenceActivityFieldApiName());
        po.setReferenceWriteOffFieldApiName(vo.getReferenceWriteOffFieldApiName());
        po.setDealerFieldApiName(vo.getDealerFieldApiName());
        po.setAccountFieldApiName(vo.getAccountFieldApiName());
        po.setCostFieldApiName(vo.getCostFieldApiName());
        po.setDisplayFieldApiNames(vo.getDisplayFieldApiNames());
        return po;
    }

}
