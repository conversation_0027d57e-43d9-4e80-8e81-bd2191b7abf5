package com.facishare.crm.fmcg.tpm.utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Date;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/24/20 7:15 PM
 */
public class TimeUtils {

    public static final long MIN_DATE = 0L;
    public static final long MAX_DATE = 7983763200000L;

    private TimeUtils() {
    }

    public static long convertToDayEnd(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTimeInMillis();
    }

    public static long convertToDayEndIfTimeWasDayBegin(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        if (calendar.get(Calendar.HOUR_OF_DAY) == 0 &&
                calendar.get(Calendar.MINUTE) == 0 &&
                calendar.get(Calendar.SECOND) == 0
        ) {
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            return calendar.getTimeInMillis();
        }
        return time;
    }

    public static long convertToDayStart(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);

        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    public static long covertToCurrentMonth(long time) {

        //根据创建时间获取 年月
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(time));
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    public static boolean isIntervalOverlap(long range1Left, long range1Right, long range2Left, long range2Right) {
        return (range1Left <= range2Left && range1Right >= range2Left) || (range1Left <= range2Right && range1Right >= range2Right) || (range2Left <= range1Left && range1Right <= range2Right);
    }

    public static long calculateDurationDays(long sourceDate, long targetDate) {
        LocalDate source = Instant.ofEpochMilli(sourceDate).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate target = Instant.ofEpochMilli(targetDate).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        long days = target.toEpochDay() - source.toEpochDay();
        return days < 0 ? 0 : days;
    }

    public static long getMonthStart(long time) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneOffset.ofHours(8));
        return localDateTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }

    public static long getMonthEnd(long time) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneOffset.ofHours(8));
        return localDateTime.plusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli() - 1;
    }
}
