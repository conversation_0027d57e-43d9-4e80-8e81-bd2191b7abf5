package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.appframework.common.service.EmployeeService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import de.lab4inf.math.util.Strings;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/8 上午10:59
 */
public class TPMActivityProofAuditObjTPMProofRandomAuditAction extends AbstractStandardAction<TPMActivityProofAuditObjTPMProofRandomAuditAction.Arg, TPMActivityProofAuditObjTPMProofRandomAuditAction.Result> {

    private IObjectData objectData;

    private EmployeeService employeeService = SpringUtil.getContext().getBean(EmployeeService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.TPM_PROOF_RANDOM_AUDIT.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getProofAuditId());
    }

    @Override
    protected Result doAct(Arg arg) {
        if (Strings.isNullOrEmpty(arg.getAction()) || Strings.isNullOrEmpty(arg.getProofId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_OBJ_PROOF_RANDOM_AUDIT_ACTION_0));
        }

        long auditTime = System.currentTimeMillis();
        objectData.set("audit_status", arg.getAction());
        objectData.set("opinion", arg.getOpinion());
        objectData.set("audit_total", arg.getTotalCost());
        objectData.set("random_audit_status", "checked");
        objectData.set("audit_time", auditTime);
        objectData.set("inspector", Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
        List<IObjectData> details = new ArrayList<>();
        Map<String, List<IObjectData>> detailMap = new HashMap<>();
        detailMap.put("TPMActivityProofAuditDetailObj", details);
        arg.getDetails().values().forEach(detail -> {
            IObjectData tmpDetail = new ObjectData();
            tmpDetail.set("_id", detail.get("detailId"));
            tmpDetail.set("audit_amount", detail.get("amount"));
            tmpDetail.set("audit_subtotal", detail.get("cost"));
            details.add(tmpDetail);
        });
        BaseObjectSaveAction.Result editRst = edit(objectData, detailMap);

        return Result.of(objectData, auditTime, arg.getAction(), "checked", getEmployeeVo(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId()));
    }

    protected IObjectData getPreObjectData() {
        return objectData;
    }

    protected IObjectData getPostObjectData() {
        return objectData;
    }

    protected String getButtonApiName() {
        return ObjectAction.TPM_PROOF_RANDOM_AUDIT.getButtonApiName();
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String proofAuditId;

        private String proofId;

        private String action;

        private String opinion;

        private double totalCost;

        private Map<String, ObjectDataDocument> details;
    }


    @Override
    protected void before(Arg arg) {
        String auditStatus = objectData.get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS, String.class);
        if (!TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS__UNCHECKED.equals(auditStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_OBJ_PROOF_RANDOM_AUDIT_ACTION_1));
        }
        super.before(arg);
    }


    private BaseObjectSaveAction.Result edit(IObjectData objectData, Map<String, List<IObjectData>> details) {
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(objectData));
        arg.setDetails(ObjectDataDocument.ofMap(details));
        ActionContext context = new ActionContext(actionContext.getRequestContext(), objectData.getDescribeApiName(), "Edit");
        return serviceFacade.triggerAction(context, arg, BaseObjectSaveAction.Result.class);
    }

    private EmployeeVO getEmployeeVo(String tenantId, String userId) {

        EmployeeDto employeeDto = employeeService.getUserInfo(tenantId, userId);
        EmployeeVO employeeVO = new EmployeeVO();
        employeeVO.setDataId(employeeDto.getDataId());
        employeeVO.setEmployeeId(employeeDto.getEmployeeId());
        employeeVO.setName(employeeDto.getName());
        employeeVO.setPost(employeeDto.getPost());
        return employeeVO;
    }

    @Data
    public static class Result {

        private ObjectDataDocument objectData;
        private long auditTime;
        private String auditStatus;
        private String randomAuditStatus;
        private EmployeeVO inspector;

        public static Result of(IObjectData objectData, long auditTime, String auditStatus, String randomAuditStatus, EmployeeVO inspector) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            result.setAuditStatus(auditStatus);
            result.setAuditTime(auditTime);
            result.setRandomAuditStatus(randomAuditStatus);
            result.setInspector(inspector);
            return result;
        }
    }

    @Data
    @ToString
    static class EmployeeVO implements Serializable {

        private int employeeId;

        private String name;

        private String post;

        private String dataId;
    }
}
