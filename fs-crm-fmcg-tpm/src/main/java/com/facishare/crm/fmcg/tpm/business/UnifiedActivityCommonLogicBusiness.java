package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.business.dto.GetValidActivitiesByDealerIdResult;
import com.facishare.crm.fmcg.tpm.dao.paas.ActivityCalculateMapper;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/16 下午4:51
 */
@Slf4j
@Component
public class UnifiedActivityCommonLogicBusiness implements IUnifiedActivityCommonLogicBusiness {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private StoreBusiness storeBusiness;

    @Autowired
    private OrganizationService organizationService;

    @Resource
    private ActivityCalculateMapper activityCalculateMapper;

    @Resource
    private IRangeFieldBusiness rangeFieldBusiness;

    @Override
    public void checkIfUnifiedActivityContainsActivityType(IObjectData unifiedActivity, String activityType) {
        if (unifiedActivity == null) {
            return;
        }
        String unifiedActivityType = unifiedActivity.get(TPMActivityUnifiedCaseFields.ACTIVITY_TYPE, String.class);
        if (Strings.isNullOrEmpty(activityType) || !activityType.equals(unifiedActivityType)) {
            throw new ValidateException(I18N.text(I18NKeys.UNIFIED_ACTIVITY_COMMON_LOGIC_BUSINESS_0));
        }
    }

    @Override
    public void checkDealerIsInTheRangeOfUnifiedActivityLimits(String tenantId, IObjectData unifiedActivity, IObjectData dealer) {
        String jsonString = unifiedActivity.get(TPMActivityUnifiedCaseFields.STORE_RANGE, String.class);
        if (Strings.isNullOrEmpty(jsonString)) {
            return;
        }
        JSONObject range = JSON.parseObject(jsonString);
        String dealerId = dealer == null ? null : dealer.getId();
        switch (range.getString("type").toUpperCase()) {
            case "FIXED":
                if (queryDealer(tenantId, unifiedActivity.getId(), dealerId) == null) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.UNIFIED_ACTIVITY_COMMON_LOGIC_BUSINESS_1), dealer != null ? dealer.getName() : "", unifiedActivity.getName()));
                }
                break;
            case "CONDITION":
                List<IFilter> array = JSON.parseArray(range.getString("value"), IFilter.class);
                if (queryDealerByCondition(tenantId, array, dealerId) == null) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.UNIFIED_ACTIVITY_COMMON_LOGIC_BUSINESS_2), dealer != null ? dealer.getName() : "", unifiedActivity.getName()));
                }
                break;
            case "ALL":
            default:
        }
    }

    @Override
    public void checkDepartmentIsUnderTheRangeOfUnifiedActivityLimits(String tenantId, IObjectData unifiedActivity, List<String> departments) {
        List<String> departmentOfUnifiedActivity = CommonUtils.cast(unifiedActivity.get(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE),String.class);
        if (CollectionUtils.isEmpty(departmentOfUnifiedActivity)) {
            throw new ValidateException(I18N.text(I18NKeys.UNIFIED_ACTIVITY_COMMON_LOGIC_BUSINESS_3));
        }
        List<Integer> allDepartments = organizationService.batchQueryLowerDepartmentIds(Integer.parseInt(tenantId), departmentOfUnifiedActivity.stream().map(Integer::valueOf).collect(Collectors.toList()));
        Set<Integer> mainDepartmentSet = new HashSet<>(allDepartments);
        departmentOfUnifiedActivity.forEach(v -> mainDepartmentSet.add(Integer.valueOf(v)));
        log.info("mainDepartmentSet is {}", mainDepartmentSet);
        log.info("departments is {}", departments);
        if (!mainDepartmentSet.containsAll(departments.stream().map(Integer::valueOf).collect(Collectors.toList()))) {
            throw new ValidateException(I18N.text(I18NKeys.UNIFIED_ACTIVITY_COMMON_LOGIC_BUSINESS_4));
        }
    }

    @Override
    public void checkTimeRangeIsInTheRangeOfUnifiedActivityLimits(IObjectData unifiedActivity, long startDate, long endDate) {
        if (unifiedActivity == null) {
            return;
        }
        Long mainStartDate = unifiedActivity.get(TPMActivityUnifiedCaseFields.START_DATE, Long.class);
        Long mainEndDate = unifiedActivity.get(TPMActivityUnifiedCaseFields.END_DATE, Long.class);

        if (mainEndDate == null || mainStartDate == null) {
            throw new ValidateException(I18N.text(I18NKeys.UNIFIED_ACTIVITY_COMMON_LOGIC_BUSINESS_5));
        }
        mainEndDate = CommonUtils.getEndDateTimeStamp(mainEndDate);
        if (startDate < mainStartDate || endDate > mainEndDate) {
            throw new ValidateException(I18N.text(I18NKeys.UNIFIED_ACTIVITY_COMMON_LOGIC_BUSINESS_6));
        }
    }

    @Override
    public Set<String> getDealerIdsOfUnifiedActivity(String tenantId, IObjectData unifiedActivity) {
        Set<String> dealerIds = new HashSet<>();
        if (unifiedActivity == null) {
            dealerIds.add("-1");
            return dealerIds;
        }
        String jsonString = unifiedActivity.get(TPMActivityUnifiedCaseFields.STORE_RANGE, String.class);
        if (Strings.isNullOrEmpty(jsonString)) {
            return dealerIds;
        }
        JSONObject range = JSON.parseObject(jsonString);
        switch (range.getString("type").toUpperCase()) {
            case "FIXED":
                return new HashSet<>(queryDealers(tenantId, unifiedActivity.getId()));
            case "CONDITION":
                List<IFilter> array = JSON.parseArray(range.getString("value"), IFilter.class);
                return new HashSet<>(queryDealersByCondition(tenantId, array));
            case "ALL":
            default:
                dealerIds.add("-1");
        }
        return dealerIds;
    }


    @Override
    public boolean isNotFitUnifiedActivity(String tenantId, Map<String, Set<String>> unifiedActivity2DealerIds, IObjectData activity, String storeId, String dealerId) {
        User user = User.systemUser(tenantId);
        String unifiedActivityId = activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
        if (!Strings.isNullOrEmpty(unifiedActivityId)) {
            Set<String> dealerIds = unifiedActivity2DealerIds.get(unifiedActivityId);
            if (Objects.isNull(dealerIds)) {
                IObjectData unifiedActivity = serviceFacade.findObjectDataIncludeDeleted(user, unifiedActivityId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
                dealerIds = getDealerIdsOfUnifiedActivity(tenantId, unifiedActivity);
                unifiedActivity2DealerIds.put(unifiedActivityId, dealerIds);
            }
            return Strings.isNullOrEmpty(dealerId) ? !dealerIds.contains(storeId) && !dealerIds.contains("-1") : !dealerIds.contains(dealerId) && !dealerIds.contains("-1");
        }
        return false;
    }

    @Override
    public void recalculateUnifiedAmountField(String tenantId, IObjectData unifiedActivity) {
        if (unifiedActivity == null) {
            return;
        }
        log.info("recalculate unifiedActivity:{}.", unifiedActivity.getId());
        BigDecimal totalAmount = unifiedActivity.get(TPMActivityUnifiedCaseFields.ACTIVITY_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        Map<String, BigDecimal> amountMap = activityCalculateMapper.statisticMoneyByUnifiedActivityId(tenantId, unifiedActivity.getId());
        log.info("recalculate statistic money:{}.", amountMap);
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMActivityUnifiedCaseFields.ACTIVITY_ACTUAL_AMOUNT, amountMap.getOrDefault("write_off_money", BigDecimal.ZERO));
        updateMap.put(TPMActivityUnifiedCaseFields.AVAILABLE_AMOUNT, totalAmount.subtract(amountMap.getOrDefault("used_money", BigDecimal.ZERO)));
        updateMap.put(TPMActivityUnifiedCaseFields.OCCUPY_AMOUNT, amountMap.getOrDefault("occupy_money", BigDecimal.ZERO));
        log.info("recalculate updateMap :{}.", updateMap);
        serviceFacade.updateWithMap(User.systemUser(tenantId), unifiedActivity, updateMap);
    }

    @Override
    public void recalculateUnifiedAmountField(String tenantId, String unifiedActivityId) {
        if (Strings.isNullOrEmpty(unifiedActivityId)) {
            return;
        }
        IObjectData unifiedActivity = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(tenantId), unifiedActivityId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        recalculateUnifiedAmountField(tenantId, unifiedActivity);
    }

    @Override
    public GetValidActivitiesByDealerIdResult getValidActivitiesByDealerId(String tenantId, List<IObjectData> activities, String dealerId) {
        List<String> unifiedIds = activities.stream().map(v -> v.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class)).filter(v -> !Strings.isNullOrEmpty(v)).collect(Collectors.toList());
        List<IObjectData> unifiedActivities = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, unifiedIds, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        Map<String, Boolean> fitResult = rangeFieldBusiness.judgeDealerInActivitiesDealerRange(tenantId, dealerId, unifiedActivities);
        GetValidActivitiesByDealerIdResult result = new GetValidActivitiesByDealerIdResult();
        result.setActivities(Lists.newArrayList());
        result.setUnifiedId2StoreRange(new HashMap<>());
        for (IObjectData activity : activities) {
            String unifiedId = activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
            if (Strings.isNullOrEmpty(unifiedId) || fitResult.getOrDefault(unifiedId, false)) {
                result.getActivities().add(activity);
            }
        }
        unifiedActivities.forEach(unified -> result.getUnifiedId2StoreRange().put(unified.getId(), unified.get(TPMActivityUnifiedCaseFields.STORE_RANGE, String.class)));
        return result;
    }

    private List<String> queryDealersByCondition(String tenantId, List<IFilter> filters) {

        SearchTemplateQuery query = new SearchTemplateQuery();


        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);

        filters.forEach(filter -> {
            Wheres where = new Wheres();
            where.setFilters(filter.getFilters());

            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setOperator(Operator.IN);
            recordTypeFilter.setFieldValues(storeBusiness.findDealerRecordType(tenantId));
            where.getFilters().add(recordTypeFilter);

            query.getWheres().add(where);
        });

        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.ACCOUNT_OBJ, query, Lists.newArrayList(CommonFields.ID))
                .stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private List<String> queryDealers(String tenantId, String unifiedActivityId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        Filter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(TPMActivityDealerScopeFields.ACTIVITY_UNIFIED_CASE_ID);
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(Lists.newArrayList(unifiedActivityId));
        query.getFilters().add(masterIdFilter);

        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);


        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_DEALER_SCOPE_OBJ, query, Lists.newArrayList(CommonFields.ID, TPMActivityDealerScopeFields.DEALER_ID))
                .stream().map(v -> v.get(TPMActivityDealerScopeFields.DEALER_ID, String.class)).collect(Collectors.toList());
    }

    private IObjectData queryDealer(String tenantId, String unifiedActivityId, String dealerId) {
        if (dealerId == null) {
            return null;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        Filter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(TPMActivityDealerScopeFields.ACTIVITY_UNIFIED_CASE_ID);
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(Lists.newArrayList(unifiedActivityId));
        query.getFilters().add(masterIdFilter);

        Filter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName(TPMActivityDealerScopeFields.DEALER_ID);
        dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
        dealerIdFilter.setOperator(Operator.EQ);
        query.getFilters().add(dealerIdFilter);

        query.setOffset(0);
        query.setLimit(1);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);

        List<IObjectData> list = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_DEALER_SCOPE_OBJ, query).getData();

        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    private IObjectData queryDealerByCondition(String tenantId, List<IFilter> filters, String dealerId) {
        if (dealerId == null) {
            return null;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);

        filters.forEach(filter -> {

            Wheres wheres = new Wheres();
            wheres.setFilters(filter.getFilters());
            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setOperator(Operator.IN);
            recordTypeFilter.setFieldValues(storeBusiness.findDealerRecordType(tenantId));
            wheres.getFilters().add(recordTypeFilter);

            Filter idFilter = new Filter();
            idFilter.setFieldName(CommonFields.ID);
            idFilter.setOperator(Operator.EQ);
            idFilter.setFieldValues(Lists.newArrayList(dealerId));
            wheres.getFilters().add(idFilter);

            query.getWheres().add(wheres);
        });


        List<IObjectData> list = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.ACCOUNT_OBJ, query).getData();
        return CollectionUtils.isEmpty(list) ? null : list.get(0);

    }
}
