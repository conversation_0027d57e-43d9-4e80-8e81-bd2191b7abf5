package com.facishare.crm.fmcg.tpm.dao.paas;

import com.facishare.paas.metadata.ratelimit.DBLimit;
import com.facishare.paas.metadata.ratelimit.MethodType;
import com.github.mybatis.mapper.ITenant;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/22 上午11:28
 */
public interface ActivityCalculateMapper extends ITenant<ActivityCalculateMapper> {

    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    Map<String, BigDecimal> statisticMoneyByUnifiedActivityId(String tenantId, String unifiedActivityId);
}
