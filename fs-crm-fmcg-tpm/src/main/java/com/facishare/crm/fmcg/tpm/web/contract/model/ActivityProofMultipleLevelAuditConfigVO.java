package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofMultipleLevelAuditConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 8.9.0 RIO
 * <p>
 * create by @yangqf
 * create time 2021/12/6 19:42
 */
@Data
@ToString
@SuppressWarnings("Duplicates")
public class ActivityProofMultipleLevelAuditConfigVO implements Serializable {

    @JSONField(name = "audit_action_name")
    @JsonProperty(value = "audit_action_name")
    @SerializedName("audit_action_name")
    private String auditActionName;

    @JSONField(name = "object_record_type")
    @JsonProperty(value = "object_record_type")
    @SerializedName("object_record_type")
    private String objectRecordType;

    @JSONField(name = "display_field_api_names_of_master")
    @JsonProperty(value = "display_field_api_names_of_master")
    @SerializedName("display_field_api_names_of_master")
    private List<String> displayFieldApiNamesOfMaster;

    @JSONField(name = "display_field_api_names_of_detail")
    @JsonProperty(value = "display_field_api_names_of_detail")
    @SerializedName("display_field_api_names_of_detail")
    private List<String> displayFieldApiNamesOfDetail;

    @JSONField(name = "is_detail_show_big_image")
    @JsonProperty(value = "is_detail_show_big_image")
    @SerializedName("is_detail_show_big_image")
    private Boolean isDetailShowBigImage;

    @JSONField(name = "display_field_api_names_of_audit_master")
    @JsonProperty(value = "display_field_api_names_of_audit_master")
    @SerializedName("display_field_api_names_of_audit_master")
    private List<String> displayFieldApiNamesOfAuditMaster;

    @JSONField(name = "display_field_api_names_of_audit_detail")
    @JsonProperty(value = "display_field_api_names_of_audit_detail")
    @SerializedName("display_field_api_names_of_audit_detail")
    private List<String> displayFieldApiNamesOfAuditDetail;

    @JSONField(name = "is_audit_detail_show_big_image")
    @JsonProperty(value = "is_audit_detail_show_big_image")
    @SerializedName("is_audit_detail_show_big_image")
    private Boolean isAuditDetailShowBigImage;

    public static ActivityProofMultipleLevelAuditConfigVO fromPO(ActivityProofMultipleLevelAuditConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityProofMultipleLevelAuditConfigVO vo = new ActivityProofMultipleLevelAuditConfigVO();
        vo.setAuditActionName(po.getAuditActionName());
        vo.setObjectRecordType(po.getObjectRecordType());
        vo.setDisplayFieldApiNamesOfMaster(po.getDisplayFieldApiNamesOfMaster());
        vo.setDisplayFieldApiNamesOfDetail(po.getDisplayFieldApiNamesOfDetail());
        vo.setIsDetailShowBigImage(po.getIsDetailShowBigImage());
        vo.setDisplayFieldApiNamesOfAuditMaster(po.getDisplayFieldApiNamesOfAuditMaster());
        vo.setDisplayFieldApiNamesOfAuditDetail(po.getDisplayFieldApiNamesOfAuditDetail());
        vo.setIsAuditDetailShowBigImage(po.getIsAuditDetailShowBigImage());
        return vo;
    }
}