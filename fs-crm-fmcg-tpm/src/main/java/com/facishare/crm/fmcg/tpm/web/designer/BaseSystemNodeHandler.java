package com.facishare.crm.fmcg.tpm.web.designer;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.PackageType;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityType;
import com.facishare.paas.appframework.core.exception.ValidateException;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/30 15:19
 */
public abstract class BaseSystemNodeHandler extends BaseNodeHandler {

    @Override
    public void validation(String tenantId, int index, List<ActivityNodeVO> nodes, IActivityType activityType) {
        super.validation(tenantId, index, nodes, activityType);

        List<String> postSystemNodeTypes = queryPostSystemNodeTypes();
        if (!postSystemNodeTypes.isEmpty()) {
            for (int cur = 0; cur < index; cur++) {
                IActivityNode curNode = nodes.get(cur);
                if (curNode.getPackageType().equals(PackageType.SYSTEM.value()) && postSystemNodeTypes.contains(curNode.getType())) {
                    throw new ValidateException("activity node order error.");
                }
            }
        }
    }

    protected abstract List<String> queryPostSystemNodeTypes();
}
