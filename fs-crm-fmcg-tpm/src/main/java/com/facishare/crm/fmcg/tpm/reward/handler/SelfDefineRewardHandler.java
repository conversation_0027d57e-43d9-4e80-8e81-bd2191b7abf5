package com.facishare.crm.fmcg.tpm.reward.handler;

import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.ActivityService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.dto.GetRewardDetailDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityRewardRulePO;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardBase;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardHandler;
import com.facishare.crm.fmcg.tpm.reward.dto.SelfDefineReward;
import com.facishare.crm.fmcg.tpm.reward.service.AdvancedRewardLimitService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.lock.DistributedLock;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IRewardRuleManager;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IDistributedLockService;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2024/5/15 16:24
 */
//IgnoreI18nFile
@Slf4j
@Component
public class SelfDefineRewardHandler extends ActivityRewardBase implements ActivityRewardHandler<SelfDefineReward.Arg, SelfDefineReward.Result> {

    @Resource
    private ActivityService activityService;

    @Resource
    private IRangeFieldBusiness rangeFieldBusiness;

    @Resource
    private DistributedLock distributedLock;

    @Resource
    private IRewardRuleManager rewardRuleManager;

    @Resource
    private AdvancedRewardLimitService advancedRewardLimitService;


    private static final String BUSINESS_PREFIX = "self_define:%s";

    private static final String SELF_DEFINE_LOCK = "self_define:%s";

    private static final String ACTIVITY_LOCK = "self_define:activity:%s";

    private static final List<String> IGNORE_ACTION_LIST = Lists.newArrayList(ScanCodeActionConstants.CONSUMER_SCAN, ScanCodeActionConstants.CONSUMER_GET_RED_PACKAGE);

    //todo:lock
    @Override
    public SelfDefineReward.Result handle(SelfDefineReward.Arg arg) {
        log.info("arg:{}", arg);
        arg.setBusinessId(String.format(BUSINESS_PREFIX, arg.getBusinessId()));
        SelfDefineReward.Result result = new SelfDefineReward.Result();
        User systemUser = User.systemUser(arg.getTenantId());
        IObjectData triggerStatusObj = serviceFacade.findObjectData(systemUser, arg.getSerialNumberStatusId(), ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ);
        String accountId = triggerStatusObj.get(FMCGSerialNumberStatusFields.ACCOUNT_ID, String.class);
        String actionUniqueId = fmcgSerialNumberService.getActionUniqueIdByActionId(arg.getTenantId(), triggerStatusObj.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class));
        if (IGNORE_ACTION_LIST.contains(actionUniqueId)) {
            log.info("actionId:{},暂不支持在此处执行。", actionUniqueId);
            result.setTips("忽略动作类型。");
            return result;
        }
        Set<String> triggerActions = rewardRuleManager.getAllowTriggerActions(arg.getTenantId());
        if (!triggerActions.contains(actionUniqueId)) {
            log.info("actionId:{},非触发类型动作。", actionUniqueId);
            result.setTips("非触发类型动作。");
            return result;
        }
        IObjectData serialNumber = serviceFacade.findObjectData(systemUser, triggerStatusObj.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class), ApiNames.FMCG_SERIAL_NUMBER_OBJ);
        result.setSerialNumberId(serialNumber.getId());
        distributedLock.executeByLock(String.format(SELF_DEFINE_LOCK, serialNumber.getId()), () -> {
            if (hasHandled(arg.getTenantId(), arg.getBusinessId())) {
                log.info("has rewarded.");
                result.setTips("已经激励过。");
                return;
            }
            if (validateSerialNumberHasDeal(arg.getTenantId(), serialNumber.getId(), String.format(BUSINESS_PREFIX, ""))) {
                log.info("码:{},已经执行过其他活动奖励。", serialNumber);
                result.setTips("已经执行其他活动。");
                return;
            }
            IObjectData activity = findActivity(arg.getTenantId(), serialNumber, triggerStatusObj, actionUniqueId);
            IObjectData store = serviceFacade.findObjectData(systemUser, accountId, ApiNames.ACCOUNT_OBJ);
            if (activity != null) {
                result.setActivityId(activity.getId());
                List<GetRewardDetailDTO> detailDTOS;
                try {
                    detailDTOS = getRewardDetails(arg.getTenantId(), activity.getId(), serialNumber.getId(), arg.getBusinessId(), accountId);
                } catch (RewardFmcgException e) {
                    log.info("由于配置问题导致无法获取奖励,", e);
                    result.setTips(e.getMessage());
                    return;
                }
                if (CollectionUtils.isNotEmpty(detailDTOS)) {
                    List<IObjectData> rewardDetails = new ArrayList<>();
                    Map<IObjectData, List<IObjectData>> redPacketDetailMap = new HashMap<>();
                    detailDTOS.forEach(detail -> {
                        if (CollectionUtils.isNotEmpty(detail.getActivityRewardDetails())) {
                            rewardDetails.addAll(detail.getActivityRewardDetails());
                        }
                        if (Objects.nonNull(detail.getRedPacket())) {
                            redPacketDetailMap.put(detail.getRedPacket(), detail.getRedPacketDetails());
                        }
                    });
                    //活动金额校验
                    distributedLock.executeByLock(String.format(ACTIVITY_LOCK, activity.getId()), () -> {
                        if (advancedRewardLimitService.overlimit(arg.getTenantId(), activity.getId(), store.get(AccountFields.CHANNEL, String.class), accountId, serialNumber.get(FMCGSerialNumberFields.PRODUCT_ID, String.class))) {
                            log.info("advanced reward limit over.");
                            result.setTips("advanced reward limit over.");
                            return;
                        }
                        try {
                            validateActivityAmount(arg.getTenantId(), activity.getId(), redPacketDetailMap.keySet());
                        } catch (RewardFmcgException e) {
                            if (e.getErrorCode() == 90004) {
                                log.info("活动金额不足跳过", e);
                                result.setTips(e.getMessage());
                                return;
                            }
                            throw e;
                        }
                        saveRewardData(arg.getTenantId(), arg.getBusinessId(), rewardDetails, redPacketDetailMap, null, null, null);
                    });
                }
                if (ScanCodeActionConstants.STORE_CHECK_WRITE_OFFS.equals(actionUniqueId)) {
                    updateActivatedRecord(arg.getTenantId(), serialNumber.getId(), activity.getId());
                }
            } else {
                log.info("no activity found.");
                result.setTips("no activity found.");
            }
        });
        return result;
    }


    private boolean hasHandled(String tenantId, String businessId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.BUSINESS_ID, Operator.EQ, Lists.newArrayList(businessId)),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList("0"))
        ));

        int count = serviceFacade.countObjectDataFromDB(tenantId, ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query);
        return count > 0;
    }

    private IObjectData findActivity(String tenantId, IObjectData serialNumber, IObjectData triggerStatusObj, String actionUniqueId) {
        String action = triggerStatusObj.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class);
        User systemUser = User.systemUser(tenantId);
        List<IObjectData> serialNumberStatuses = fmcgSerialNumberService.queryAllSerialNumberStatusBySerialNumberId(tenantId, serialNumber.getId());
        Set<String> relatedTenantIds = serialNumberStatuses.stream().map(v -> v.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class)).collect(Collectors.toSet());
        String dimension = getDimension(tenantId, triggerStatusObj, relatedTenantIds);
        log.info("dimension:{}", dimension);
        Map<String, ActivityRewardRulePO> activityId2RuleMap = activityRewardRuleDAO.queryByTriggerTypeAndDimension(tenantId, ScanCodeActionConstants.SELF_DEFINE_REWARD_TEMPLATE_ID, action, dimension).stream().collect(Collectors.toMap(ActivityRewardRulePO::getRelatedObjectId, v -> v, (a, b) -> b));
        if (MapUtils.isEmpty(activityId2RuleMap)) {
            log.warn("没有相关活动");
            return null;
        }
        String productId = serialNumber.get(FMCGSerialNumberFields.PRODUCT_ID, String.class);
        String storeId = triggerStatusObj.get(FMCGSerialNumberStatusFields.ACCOUNT_ID, String.class);
        if (Strings.isNullOrEmpty(storeId)) {
            log.info("找不到门店。");
            return null;
        }
        IObjectData store = serviceFacade.findObjectData(systemUser, storeId, ApiNames.ACCOUNT_OBJ);
        List<String> departmentIds = activityService.getDepartmentByStore(tenantId, store);
        List<IObjectData> activities = activityService.findActivityByStore(tenantId, departmentIds, store, new ArrayList<>(activityId2RuleMap.keySet()), Lists.newArrayList("all"));
        log.info("activity before product filter, size:{}", activities.size());
        activities = filterByProductRange(tenantId, productId, activities, serialNumber);
        activities = filterByActivatedTime(tenantId, activities, triggerStatusObj, actionUniqueId);
        log.info("activity size:{}", activities.size());
        if (CollectionUtils.isNotEmpty(activities)) {
            activities.sort(Comparator.comparing(IObjectData::getCreateTime));
            for (IObjectData activity : activities) {
                if (!lackOfRequiredNode(tenantId, serialNumberStatuses, activityId2RuleMap.get(activity.getId()).getRewardDetails())) {
                    return activity;
                }
            }
        }
        return null;
    }

    private List<IObjectData> filterByActivatedTime(String tenantId, List<IObjectData> activities, IObjectData triggerStatusObj, String actionUniqueId) {
        if (CollectionUtils.isNotEmpty(activities) && ScanCodeActionConstants.STORE_CHECK_WRITE_OFFS.equals(actionUniqueId)) {
            IObjectData writeOffData = serviceFacade.findObjectData(User.systemUser(tenantId), triggerStatusObj.get(FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID, String.class), triggerStatusObj.get(FMCGSerialNumberStatusFields.BUSINESS_OBJECT, String.class));
            Long activatedTime = writeOffData.get(ActivityBarcodeActivationRecordFields.ACTIVATION_TIME, Long.class);
            activities = activities.stream().filter(activity -> {
                Long activatedStartTime = activity.get(TPMActivityFields.ACTIVATION_START_TIME, Long.class, 0L);
                Long activatedEndTime = activity.get(TPMActivityFields.ACTIVATION_END_TIME, Long.class, 0L);
                return (activatedStartTime == 0 && activatedEndTime == 0) || (activatedTime >= activatedStartTime && activatedTime <= activatedEndTime);
            }).collect(Collectors.toList());
        }
        return activities;
    }

    private List<IObjectData> filterByProductRange(String tenantId, String id, List<IObjectData> baseActivities, IObjectData serialNumber) {
        List<IObjectData> result = new ArrayList<>();
        Map<String, Boolean> fitMap = rangeFieldBusiness.judgeProductInActivitiesProductRange(tenantId, id, baseActivities, serialNumber);
        baseActivities.forEach(activity -> {
            if (Boolean.TRUE.equals(fitMap.get(activity.getId()))) {
                result.add(activity);
            }
        });
        return result;
    }

    private IObjectData getActivatedRecord(String tenantId, String serialNumberId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(ActivityBarcodeActivationRecordFields.SERIAL_NUMBER_ID, Operator.EQ, Lists.newArrayList(serialNumberId))));
        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.ACTIVITY_BARCODE_ACTIVATION_RECORD_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.TENANT_ID));
        return data.isEmpty() ? null : data.get(0);
    }

    private void updateActivatedRecord(String tenantId, String snId, String activityId) {
        IObjectData record = getActivatedRecord(tenantId, snId);
        if (record != null && !Boolean.TRUE.equals(record.get(ActivityBarcodeActivationRecordFields.IS_MOTIVATING, Boolean.class))) {
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(ActivityBarcodeActivationRecordFields.IS_MOTIVATING, true);
            updateMap.put(ActivityBarcodeActivationRecordFields.PARTICIPATE_IN_ACTIVITY, activityId);
            serviceFacade.updateWithMap(User.systemUser(tenantId), record, updateMap);
        }
    }
}
