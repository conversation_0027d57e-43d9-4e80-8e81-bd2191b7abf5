package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2023/9/14 16:35
 */
@Data
@ToString
public class RewardAccountEntity implements Serializable {

    public static final String F_ACCOUNT_DIMENSION = "account_dimension";

    public static final String F_ACCOUNT_TYPE = "account_type";

    public static final String F_DEPARTMENT_LEVEL = "department_level";

    public static final String F_FIXED_ACCOUNT_ID = "fixed_account_id";

    public static final String F_ACCOUNT_PLATFORM = "account_platform";

    @Property(F_ACCOUNT_DIMENSION)
    private String accountDimension;

    @Property(F_ACCOUNT_TYPE)
    private String accountType;

    @Property(F_DEPARTMENT_LEVEL)
    private String departmentLevel;

    @Property(F_ACCOUNT_PLATFORM)
    private String accountPlatform;

    @Property(F_FIXED_ACCOUNT_ID)
    private String fixedAccountId;

}
