package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/26 03:35
 */
public interface AuditList {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "activity_type_id")
        @JsonProperty(value = "activity_type_id")
        @SerializedName("activity_type_id")
        private String activityTypeId;

        @JSONField(name = "dealer_id")
        @JsonProperty(value = "dealer_id")
        @SerializedName("dealer_id")
        private String dealerId;

        @JSONField(name = "activity_id")
        @JsonProperty(value = "activity_id")
        @SerializedName("activity_id")
        private String activityId;

        @JSONField(name = "begin_date")
        @JsonProperty(value = "begin_date")
        @SerializedName("begin_date")
        private long beginDate;

        @JSONField(name = "end_date")
        @JsonProperty(value = "end_date")
        @SerializedName("end_date")
        private long endDate;

        @JSONField(name = "activity_owner")
        @JsonProperty(value = "activity_owner")
        @SerializedName("activity_owner")
        private Integer activityOwner;

        @JSONField(name = "activity_begin_date")
        @JsonProperty(value = "activity_begin_date")
        @SerializedName("activity_begin_date")
        private long activityBeginDate;

        @JSONField(name = "activity_end_date")
        @JsonProperty(value = "activity_end_date")
        @SerializedName("activity_end_date")
        private long activityEndDate;

        @JSONField(name = "activity_closed_status")
        @JsonProperty(value = "activity_closed_status")
        @SerializedName("activity_closed_status")
        private String activityClosedStatus;

        @JSONField(name = "activity_create_time_begin_date")
        @JsonProperty(value = "activity_create_time_begin_date")
        @SerializedName("activity_create_time_begin_date")
        private long activityCreateTimeBeginDate;

        @JSONField(name = "activity_create_time_end_date")
        @JsonProperty(value = "activity_create_time_end_date")
        @SerializedName("activity_create_time_end_date")
        private long activityCreateTimeEndDate;

        @JSONField(name = "number_filter_list")
        @JsonProperty(value = "number_filter_list")
        @SerializedName("number_filter_list")
        private List<NumberFilterVO> numberFilterList;

        @JSONField(name = "order_by_list")
        @JsonProperty(value = "order_by_list")
        @SerializedName("order_by_list")
        private List<OrderBy> orderByList;

        @JSONField(name = "activity_status")
        @JsonProperty(value = "activity_status")
        @SerializedName("activity_status")
        private String activityStatus;

        private Long offset;

        private Long limit;
    }

    @Data
    @ToString
    class OrderBy implements Serializable {
        private String field;
        private String type;
    }

    @Data
    @ToString
    class NumberFilterVO implements Serializable {

        @JSONField(name = "field_key")
        @JsonProperty(value = "field_key")
        @SerializedName("field_key")
        private String fieldKey;

        private String operator;

        @JSONField(name = "field_values")
        @JsonProperty(value = "field_values")
        @SerializedName("field_values")
        private List<Object> fieldValues;

        @JSONField(name = "is_asc")
        @JsonProperty(value = "is_asc")
        @SerializedName("is_asc")
        private Boolean isAsc;

        public String toSql(String table) {
            switch (operator) {
                default:
                case "EQ":
                    validateIsOneNumber();
                    return table + "." + fieldKey + " = " + fieldValues.get(0).toString();
                case "N":
                    validateIsOneNumber();
                    return table + "." + fieldKey + " != " + fieldValues.get(0).toString();
                case "LT":
                    validateIsOneNumber();
                    return table + "." + fieldKey + " < " + fieldValues.get(0).toString();
                case "LTE":
                    validateIsOneNumber();
                    return table + "." + fieldKey + " <= " + fieldValues.get(0).toString();
                case "GT":
                    validateIsOneNumber();
                    return table + "." + fieldKey + " > " + fieldValues.get(0).toString();
                case "GTE":
                    validateIsOneNumber();
                    return table + "." + fieldKey + " >= " + fieldValues.get(0).toString();
                case "BETWEEN":
                    validateIsTwoNumber();
                    return table + "." + fieldKey + " between " + fieldValues.get(0).toString() + " and " + fieldValues.get(1).toString();
            }
        }

        private void validateIsOneNumber() {
            if (CollectionUtils.isEmpty(fieldValues)) {
                throw new IllegalArgumentException("Number filter to sql error, field value empty.");
            }
            isNumber(fieldValues.get(0));
        }

        private void validateIsTwoNumber() {
            if (fieldValues.size() < 2) {
                throw new IllegalArgumentException("Number filter to sql error, field value size must have at least two elements.");
            }
            isNumber(fieldValues.get(0));
            isNumber(fieldValues.get(1));
        }

        private void isNumber(Object obj) {
            if (!(obj instanceof Number)) {
                String str = obj.toString();
                if (!NumberUtils.isCreatable(str)) {
                    throw new IllegalArgumentException("Number filter to sql error, field value is not a number.");
                }
            }
        }
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "data")
        @JsonProperty(value = "data")
        @SerializedName("data")
        private List<AuditListDatumVO> data;

        private long total;

        private Boolean needWriteOff;
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class AuditListDatumVO extends HashMap<String, Object> implements Serializable {

        public static AuditListDatumVO fromPO(Map<String, Object> po) {
            AuditListDatumVO datum = new AuditListDatumVO();

            String activityId = (String) po.get("activity_id");
            String dealerId = (String) po.get("dealer_id");
            String identity = String.format("audit_%s.%s", activityId, dealerId);
            datum.put("_id", identity);

            fillActivityDate(po);
            for (Entry<String, Object> entry : po.entrySet()) {
                datum.put(entry.getKey(), entry.getValue());
            }
            return datum;
        }

        private static void fillActivityDate(Map<String, Object> poData) {
            if (convertLong(poData.getOrDefault(TPMActivityFields.BEGIN_DATE, TimeUtils.MIN_DATE)) <= TimeUtils.MIN_DATE) {
                poData.put(TPMActivityFields.BEGIN_DATE, null);
            }
            if (convertLong(poData.getOrDefault(TPMActivityFields.END_DATE, TimeUtils.MAX_DATE)) >= TimeUtils.MAX_DATE) {
                poData.put(TPMActivityFields.END_DATE, null);
            }
        }

        private static Long convertLong(Object date) {
            if (date instanceof BigDecimal) {
                return ((BigDecimal) date).longValue();
            } else if (date instanceof Long) {
                return (Long) date;
            }
            return 0L;
        }

    }

    @Data
    @ToString
    @Builder
    class ObjectReferenceValueVO implements Serializable {

        private String id;

        private String name;

        @JSONField(name = "object_api_name")
        @JsonProperty(value = "object_api_name")
        @SerializedName("object_api_name")
        private String objectApiName;
    }
}