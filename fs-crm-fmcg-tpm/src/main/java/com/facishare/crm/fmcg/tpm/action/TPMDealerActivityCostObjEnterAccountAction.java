package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMDealerActivityCostFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.CustomButtonAction;
import com.facishare.paas.appframework.core.predef.action.StandardEnterAccountAction;
import com.facishare.paas.metadata.api.IObjectData;
import de.lab4inf.math.util.Strings;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/1/4 下午4:02
 */
public class TPMDealerActivityCostObjEnterAccountAction extends StandardEnterAccountAction {

    @Override
    protected void before(CustomButtonAction.Arg arg) {
        if (Objects.equals("fs-crm-fmcg-service", actionContext.getPeerName())) {
            actionContext.setAttribute(RequestContext.Attributes.SKIP_BUTTON_CONDITIONS, true);
        }
        IObjectData cost = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
        String dealerId = cost.get(TPMDealerActivityCostFields.DEALER_ID, String.class);
        if (Strings.isNullOrEmpty(dealerId)) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_ENTER_ACCOUNT_ACTION_0));
        }
        super.before(arg);
    }
}
