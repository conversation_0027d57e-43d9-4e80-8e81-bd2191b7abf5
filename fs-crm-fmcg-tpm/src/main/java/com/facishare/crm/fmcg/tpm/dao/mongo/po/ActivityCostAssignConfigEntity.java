package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityCostAssignConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2022/5/30 17:59
 */
@Data
@ToString
public class ActivityCostAssignConfigEntity implements Serializable {

    /***
     * 门店签收确认
     */
    @Embedded("activity_cost_assign_accept_config")
    private ActivityCostAssignAcceptConfigEntity activityCostAssignAcceptConfig;

    public static ActivityCostAssignConfigEntity fromVO(ActivityCostAssignConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityCostAssignConfigEntity po = new ActivityCostAssignConfigEntity();
        po.setActivityCostAssignAcceptConfig(ActivityCostAssignAcceptConfigEntity.fromVO(vo.getActivityCostAssignAcceptConfigVO()));
        return po;
    }
}
