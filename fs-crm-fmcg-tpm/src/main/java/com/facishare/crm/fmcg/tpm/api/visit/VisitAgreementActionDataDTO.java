package com.facishare.crm.fmcg.tpm.api.visit;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/27 16:37
 */
@Data
@ToString
public class VisitAgreementActionDataDTO implements Serializable {

    @JSONField(name = "activity_agreement_list")
    private List<ActivityAgreementDTO> activityAgreementDTOList;

    @JSONField(name = "activity_agreement_list_size")
    private long activityAgreementListSize;
}
