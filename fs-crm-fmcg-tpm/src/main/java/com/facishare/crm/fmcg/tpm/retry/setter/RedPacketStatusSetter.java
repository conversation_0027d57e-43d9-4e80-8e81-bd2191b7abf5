package com.facishare.crm.fmcg.tpm.retry.setter;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandlerEnum;
import com.facishare.paas.appframework.core.exception.ValidateException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2023/8/9 17:52
 */
//IgnoreI18nFile
@Component
public class RedPacketStatusSetter extends BaseSetter {

    public void setUpdateStatusTask(String tenantId, String redPacketId, long nextExecuteTime) {
        setUpdateStatusTask(tenantId, redPacketId, nextExecuteTime, false);
    }

    public void setUpdateStatusTask(String tenantId, String redPacketId, long nextExecuteTime, boolean judgeRepeat) {
        String name = String.format("更新奖励记录状态任务_%s_%s", ApiNames.RED_PACKET_RECORD_OBJ, redPacketId);
        if (judgeRepeat) {
            List<RetryTaskPO> retryTaskPOList = retryTaskDAO.queryProcessingTaskByName(name);
            if (CollectionUtils.isNotEmpty(retryTaskPOList)) {
                throw new ValidateException(I18N.text(I18NKeys.RED_PACKET_STATUS_SETTER_0));
            }
        }
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", tenantId);
        params.put("redPacketId", redPacketId);
        setInit(tenantId, name, RetryHandlerEnum.RED_PACKET_STATUS_UPDATE_HANDLER.code(), JSON.toJSONString(params), 2, nextExecuteTime);
    }
}
