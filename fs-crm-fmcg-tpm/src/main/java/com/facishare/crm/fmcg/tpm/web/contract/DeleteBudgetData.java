package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface DeleteBudgetData {

    @Data
    @ToString
    class Arg implements Serializable {

        private String code;

        @JSONField(name = "budget_type_id")
        @JsonProperty(value = "budget_type_id")
        @SerializedName("budget_type_id")
        private String budgetTypeId;
    }


    @Data
    @ToString
    class Result implements Serializable {
    }
}
