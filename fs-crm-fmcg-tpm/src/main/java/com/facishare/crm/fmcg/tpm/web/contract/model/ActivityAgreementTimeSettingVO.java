package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityAgreementTimeSettingEntity;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 15:34
 */
@Data
@ToString
public class ActivityAgreementTimeSettingVO implements Serializable {

    /**
     * before_begin_date
     * after_begin_date
     * before_end_date
     * after_end_date
     * unset
     */
    private String type;

    private Long value;

    public static ActivityAgreementTimeSettingVO fromPO(ActivityAgreementTimeSettingEntity po) {
        if (po == null) {
            return null;
        }
        ActivityAgreementTimeSettingVO vo = new ActivityAgreementTimeSettingVO();
        vo.setType(po.getType());
        vo.setValue(po.getValue());
        return vo;
    }
}
