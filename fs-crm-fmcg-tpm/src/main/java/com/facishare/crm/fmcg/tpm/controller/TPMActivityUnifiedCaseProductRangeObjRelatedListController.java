package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.apache.solr.common.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
public class TPMActivityUnifiedCaseProductRangeObjRelatedListController extends StandardRelatedListController {
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private static final String PRODUCT_ID = "product_id";
    private static final String ACTIVITY_UNIFIED_CASE_ID = "activity_unified_case_id";
    private String activityUnifiedCaseId;
    private String rangeType = "";


    @Override
    protected void before(Arg arg) {

        if (apiNameValidateFail()) {
            super.before(arg);
            return;
        }
        ObjectDataDocument masterData = arg.getMasterData();

        String activityTypeId = (String) masterData.get(TPMActivityFields.ACTIVITY_TYPE);
        boolean enableActivityPlan = isEnableActivityPlan(activityTypeId);

        activityUnifiedCaseId = (String) masterData.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID);
        if (enableActivityPlan && Strings.isNullOrEmpty(activityUnifiedCaseId)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_PRODUCT_RANGE_OBJ_RELATED_LIST_CONTROLLER_0));
        }

        if (enableActivityPlan) {
            IObjectData activityUnifiedCase = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), activityUnifiedCaseId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
            JSONObject rangeTypeMap = getRangeType(activityUnifiedCase);
            rangeType = rangeTypeMap.getString("type").toUpperCase();
        }
        //活动方案如果为全部直接查产品
        if (Objects.equals("ALL", rangeType)) {
            this.controllerContext = new ControllerContext(
                    controllerContext.getRequestContext(),
                    ApiNames.PRODUCT_OBJ,
                    controllerContext.getMethodName());
        }
        super.before(arg);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(overrideQuery(query));
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return overrideResult(super.after(arg, result));
    }

    private Result overrideResult(Result result) {
        if (apiNameValidateFail()) {
            return result;
        }
        //方案rangeType为ALL时查产品对象，不需要特殊处理
        if (Objects.equals("ALL", rangeType)) {
            return result;
        }
        List<String> productIds = result.getDataList().stream().map(data -> (String) data.get(PRODUCT_ID)).collect(Collectors.toList());

        List<IObjectData> products = serviceFacade.findObjectDataByIdsIgnoreRelevantTeam(controllerContext.getTenantId(), Lists.newArrayList(productIds), ApiNames.PRODUCT_OBJ);
        IObjectDescribe productDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.PRODUCT_OBJ);
        serviceFacade.fillObjectDataWithRefObject(productDescribe, products, User.systemUser(controllerContext.getTenantId()), null, false);
        Map<String, IObjectData> productMap = products.stream().collect(Collectors.toMap(IObjectData::getId, v -> v, (oldData, newData) -> newData));
        List<ObjectDataDocument> resultList = Lists.newArrayList();

        for (ObjectDataDocument dataDocument : result.getDataList()) {

            String productId = (String) dataDocument.get(PRODUCT_ID);
            IObjectData productObjData = productMap.get(productId);
            if (productObjData == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_UNIFIED_CASE_PRODUCT_RANGE_OBJ_RELATED_LIST_CONTROLLER_1));
            }
            ObjectDataDocument product = new ObjectDataDocument();
            product.putAll(ObjectDataDocument.of(productObjData));
            resultList.add(ObjectDataDocument.of(product));
        }

        result.setDataList(resultList);
        return result;
    }

    private SearchTemplateQuery overrideQuery(SearchTemplateQuery query) {

        if (apiNameValidateFail()) {
            return query;
        }
        //方案rangeType为ALL时查产品对象，不需要特殊处理
        if (Objects.equals("ALL", rangeType)) {
            return query;
        }

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(ACTIVITY_UNIFIED_CASE_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityUnifiedCaseId));
        query.getFilters().add(activityFilter);
        return query;
    }


    private boolean apiNameValidateFail() {
        ObjectDataDocument objectData = arg.getObjectData();
        if (Objects.isNull(objectData)) {
            return true;
        }

        String objectApiName = (String) objectData.get("object_describe_api_name");
        return !ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ.equals(objectApiName);
    }

    private JSONObject getRangeType(IObjectData objectData) {
        String rangeStr = objectData.get("product_range", String.class);
        //是空代表的此条数据是老数据，需要按照老数据处理逻辑：指定产品
        if (StringUtils.isEmpty(rangeStr)) {
            rangeStr = "{\"type\":\"FIXED\",\"value\":\"FIXED\"}";
        }
        return JSON.parseObject(rangeStr);
    }

    private boolean isEnableActivityPlan(String activityTypeId) {

        boolean enableActivityPlan = false;
        if (!Strings.isNullOrEmpty(activityTypeId)) {
            ActivityTypeExt activityType = activityTypeManager.find(controllerContext.getTenantId(), activityTypeId);
            if (!Objects.isNull(activityType.node(NodeType.PLAN_TEMPLATE))) {
                enableActivityPlan = true;
            }
        }
        return enableActivityPlan;
    }
}
