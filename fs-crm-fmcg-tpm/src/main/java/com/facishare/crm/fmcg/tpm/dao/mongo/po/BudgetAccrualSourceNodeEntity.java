package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetAccrualSourceNodeVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/9 上午11:46
 */
@Data
@ToString
public class BudgetAccrualSourceNodeEntity implements Serializable {

    public static final String F_WHERE_CONDITION = "whereCondition";
    public static final String F_CONDITION_CODE = "condition_code";
    public static final String F_PREVIEW = "preview";

    public static final String F_TRIGGER_TIME = "trigger_time";
    public static final String F_AMOUNT_SOURCE = "amount_source";
    public static final String F_AMOUNT_SOURCE_PREVIEW = "amount_source_preview";
    public static final String F_FREQUENCY = "frequency";
    public static final String F_CHANGE_FIELD = "change_field";

    // 条件code
    @Property(F_CONDITION_CODE)
    private String conditionCode;

    //触发条件
    @Embedded(F_WHERE_CONDITION)
    private List<BudgetWhereConditionEntity> whereConditions;

    @Property(F_PREVIEW)
    private String previewString;

    // 计提 触发时机
    @Property(F_TRIGGER_TIME)
    private String triggerTime;

    //金额来源 - 计算公式
    @Property(F_AMOUNT_SOURCE)
    private String amountSource;

    @Property(F_AMOUNT_SOURCE_PREVIEW)
    private String amountSourcePreview;

    @Property(F_CHANGE_FIELD)
    private List<String> changeField;

    //计提频率
    @Property(F_FREQUENCY)
    private String frequency;


    public static BudgetAccrualSourceNodeEntity fromVO(BudgetAccrualSourceNodeVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }
        BudgetAccrualSourceNodeEntity entity = new BudgetAccrualSourceNodeEntity();
        entity.setConditionCode(vo.getConditionCode());
        entity.setPreviewString(vo.getPreviewString());
        if (CollectionUtils.isEmpty(vo.getWhereConditions())) {
            entity.setWhereConditions(new ArrayList<>());
        } else {
            entity.setWhereConditions(vo.getWhereConditions().stream().map(BudgetWhereConditionEntity::fromVO).collect(Collectors.toList()));
        }
        entity.setTriggerTime(vo.getTriggerTime());
        entity.setAmountSource(vo.getAmountSource());
        entity.setFrequency(vo.getFrequency());
        entity.setChangeField(vo.getChangeField());
        return entity;
    }

    public static BudgetAccrualSourceNodeVO toVO(BudgetAccrualSourceNodeEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        BudgetAccrualSourceNodeVO vo = new BudgetAccrualSourceNodeVO();
        vo.setConditionCode(entity.getConditionCode());
        vo.setPreviewString(entity.getPreviewString());
        if (CollectionUtils.isEmpty(entity.getWhereConditions())) {
            vo.setWhereConditions(new ArrayList<>());
        } else {
            vo.setWhereConditions(entity.getWhereConditions().stream().map(BudgetWhereConditionEntity::toVO).collect(Collectors.toList()));
        }
        vo.setTriggerTime(entity.getTriggerTime());
        vo.setAmountSource(entity.getAmountSource());
        vo.setFrequency(entity.getFrequency());
        vo.setChangeField(entity.getChangeField());
        return vo;
    }
}
