package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/28 下午6:17
 */
@Slf4j
public class TPMBudgetAccountObjInsertImportDataAction extends StandardInsertImportDataAction {


    private static final String PK_ID = "_PK_ID";

    @Override
    protected void before(Arg arg) {
        log.info("import budget.arg:{}", arg);
        super.before(arg);
        this.dataList.forEach(data -> {
            data.getData().set(PK_ID, data.getRowNo());
        });
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {

        List<IObjectData> validateData = new ArrayList<>();
        List<ImportError> errorList = new ArrayList<>();
        for (IObjectData valid : validList) {
            ActionContext addActionContext = new ActionContext(actionContext.getRequestContext(), ApiNames.TPM_BUDGET_ACCOUNT, "Add");
            addActionContext.setAttribute("triggerWorkflow", arg.getIsWorkFlowEnabled());
            addActionContext.setAttribute("triggerFlow", arg.getIsApprovalFlowEnabled());
            BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
            saveArg.setObjectData(ObjectDataDocument.of(valid));
            try {
                BaseObjectSaveAction.Result result = serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
                validateData.add(result.getObjectData().toObjectData());
            } catch (Exception ex) {
                ImportError error = new ImportError();
                error.setRowNo(valid.get(PK_ID, Integer.class));
                error.setErrorMessage(ex.getMessage());
                errorList.add(error);
                log.info("insert single data.", ex);
            }
        }
        mergeErrorList(errorList);
        actionContext.setAttribute("triggerWorkflow", false);
        actionContext.setAttribute("triggerFlow", false);
        return validateData;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }


    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
        log.info("data:{}", JSON.toJSONString(dataList));
        List<ImportError> errorList = new ArrayList<>();

        dataList.forEach(data -> {

            if (data.getData().get(TPMBudgetAccountFields.BASE_AMOUNT) == null) {
                ImportError error = new ImportError();
                error.setRowNo(data.getRowNo());
                error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_BUDGET_ACCOUNT_OBJ_INSERT_IMPORT_DATA_ACTION_0));
                errorList.add(error);
            }

        });
        mergeErrorList(errorList);
    }
}
