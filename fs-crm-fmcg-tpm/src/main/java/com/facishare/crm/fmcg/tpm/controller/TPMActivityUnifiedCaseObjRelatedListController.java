package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityUnifiedCaseFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.TPMPreDefineObject;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.vavr.collection.Stream;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/15 下午4:45
 */
public class TPMActivityUnifiedCaseObjRelatedListController extends StandardRelatedListController {

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        List<String> refactorApiNames = Stream.of(TPMPreDefineObject.values()).filter(v -> v.getModule().equals(TPMPreDefineObject.Module.TPM)).map(TPMPreDefineObject::getApiName).collect(Collectors.toList());

        //tpm object need to deal activity type filter
        if (arg.getObjectData() != null && refactorApiNames.contains(arg.getObjectData().toObjectData().getDescribeApiName())) {
            IObjectData relatedData = arg.getObjectData().toObjectData();

            String activityType = relatedData.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
            if (Strings.isNullOrEmpty(activityType)) {
                String activityId = relatedData.get("activity_id", String.class);
                if (!Strings.isNullOrEmpty(activityId)) {
                    IObjectData activity = serviceFacade.findObjectData(controllerContext.getUser(), activityId, ApiNames.TPM_ACTIVITY_OBJ);
                    activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
                }
            }
            if (!Strings.isNullOrEmpty(activityType)) {
                Filter activityTyeFilter = new Filter();
                activityTyeFilter.setFieldName(TPMActivityUnifiedCaseFields.ACTIVITY_TYPE);
                activityTyeFilter.setOperator(Operator.EQ);
                activityTyeFilter.setFieldValues(Lists.newArrayList(activityType));
                query.getFilters().add(activityTyeFilter);
            }

            Filter closeStatusFilter = new Filter();
            closeStatusFilter.setFieldName(TPMActivityUnifiedCaseFields.CLOSE_STATUS);
            closeStatusFilter.setOperator(Operator.EQ);
            closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityUnifiedCaseFields.CLOSE_STATUS__UNCLOSED));
            query.getFilters().add(closeStatusFilter);


            Filter activityStatusFilter = new Filter();
            activityStatusFilter.setFieldName(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS);
            if (TPMGrayUtils.allowScheduleUnifiedCaseFilter(controllerContext.getTenantId())) {
                activityStatusFilter.setOperator(Operator.IN);
                activityStatusFilter.setFieldValues(Lists.newArrayList(
                        TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS,
                        TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__SCHEDULE));
            } else {
                activityStatusFilter.setOperator(Operator.EQ);
                activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS));
            }
            query.getFilters().add(activityStatusFilter);

        }
        return query;
    }


    @Override
    protected Result after(Arg arg, Result result) {
        fillActivityUnifiedCaseRelated(result);
        return super.after(arg, result);
    }

    private void fillActivityUnifiedCaseRelated(Result result) {
        for (ObjectDataDocument activity : result.getDataList()) {
            Long beginDate = (Long) activity.get(TPMActivityUnifiedCaseFields.START_DATE);
            Long endDate = (Long) activity.get(TPMActivityUnifiedCaseFields.END_DATE);

            if (beginDate <= TimeUtils.MIN_DATE) {
                activity.put(TPMActivityUnifiedCaseFields.START_DATE, null);
            }
            if (endDate >= TimeUtils.MAX_DATE) {
                activity.put(TPMActivityUnifiedCaseFields.END_DATE, null);
            }
        }
    }
}
