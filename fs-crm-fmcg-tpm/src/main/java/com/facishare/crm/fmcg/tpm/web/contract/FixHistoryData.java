package com.facishare.crm.fmcg.tpm.web.contract;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface FixHistoryData {

    @Data
    @ToString
    class Arg implements Serializable {

        private String tenantId;

        private List<String> dataIds;

        private Map<String, Object> updateFieldMap;

        private String apiName;

    }


}
