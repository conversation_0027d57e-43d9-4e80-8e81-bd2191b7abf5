package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction;
import com.facishare.paas.appframework.core.predef.action.BaseUpdateImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * author: wuyx
 * description:
 * createTime: 2023/5/5 11:56
 */
@Slf4j
public class TPMDealerActivityCostObjUpdateImportTemplateAction extends BaseUpdateImportTemplateAction<BaseImportTemplateAction.Arg> {

    @Override
    protected List<IFieldDescribe> getImportTemplateField(Arg arg) {
        log.info("init getImportTemplateField arg={}", arg);
        List<IFieldDescribe> templateFields = super.getImportTemplateField(arg);
        System.out.println(templateFields);
        List<String> fieldDescribes = Lists.newArrayList("name");
        return templateFields.stream().filter(iFieldDescribe ->
                Objects.isNull(iFieldDescribe.getApiName())
                        || iFieldDescribe.getApiName().endsWith("__c")
                        || fieldDescribes.contains(iFieldDescribe.getApiName()))
                .collect(Collectors.toList());
    }

}
