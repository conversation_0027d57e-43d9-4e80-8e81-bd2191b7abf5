package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.proof.Enable;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.ActivityCustomerTypeEnum;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IEnableCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.business.dto.GetValidActivitiesByDealerIdResult;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates,unused")
public class TPMActivityProofObjEnableController extends PreDefineController<Enable.Arg, Enable.Result> {

    private static final long CONDITION_THRESHOLD = 15;
    private final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private final MergeJedisCmd redisCmd = SpringUtil.getContext().getBean("redisCmd", MergeJedisCmd.class);
    private boolean hasAllDealerAllRangeActivity = false;
    private static final IDescribeCacheService I_DESCRIBE_CACHE_SERVICE = SpringUtil.getContext().getBean(IDescribeCacheService.class);
    private final IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(IUnifiedActivityCommonLogicBusiness.class);
    private IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);
    private final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private final DataPrivilegeService dataPrivilegeService = SpringUtil.getContext().getBean(DataPrivilegeService.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    private ISearchTemplate activitySearchTemplate;

    private IObjectDescribe activityDescribe;

    private final IEnableCacheService enableCacheService = SpringUtil.getContext().getBean(IEnableCacheService.class);

    private User systemUser;

    private boolean isAdminRequest = false;


    @Override
    protected void before(Enable.Arg arg) {
        this.systemUser = User.systemUser(controllerContext.getTenantId());
        this.isAdminRequest = serviceFacade.isAdmin(controllerContext.getUser());
        this.activityDescribe = serviceFacade.findDescribeAndLayout(this.systemUser, ApiNames.TPM_ACTIVITY_OBJ, false, null).getObjectDescribe();
        this.activitySearchTemplate = serviceFacade.findSearchTemplateByIdAndType(this.systemUser, "", ApiNames.TPM_ACTIVITY_OBJ, "All");
        super.before(arg);
    }

    @Override
    protected Enable.Result doService(Enable.Arg arg) {
        log.info("init proofEnable arg={}", arg);
        if (TPMGrayUtils.skipEnableCheck(controllerContext.getTenantId())) {
            return new Enable.Result(true);
        }

        if (Boolean.TRUE.equals(arg.isUseCache()) && !TPMGrayUtils.skipEnableCacheCheck(controllerContext.getTenantId())) {
            int value = enableCacheService.getCache(controllerContext.getTenantId(), controllerContext.getUser().getUserIdOrOutUserIdIfOutUser(), arg.getStoreId());
            if (value != -1) {
                log.info("init proofEnable cache={}", value);
                return new Enable.Result(value == 1);
            }
        }

        return new Enable.Result(doEnableCheck());
    }


    private boolean doEnableCheck() {
        List<Integer> departmentIds;
        if (!controllerContext.getUser().isOutUser()) {
            departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), Integer.parseInt(controllerContext.getUser().getUserIdOrOutUserIdIfOutUser()));
            if (CollectionUtils.isEmpty(departmentIds)) {
                log.info("init proofEnable departmentIds empty");
                return false;
            }
        } else {
            departmentIds = Lists.newArrayList();
        }
        stopWatch.lap("getDepartmentIds");

        IObjectData store = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        log.info("init proofEnable store={}", store);
        stopWatch.lap("findStoreData");
        if (store == null) {
            return false;
        }
        String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), store);
        if (dealerId == null && storeBusiness.findDealerRecordType(controllerContext.getTenantId()).contains(store.getRecordType())) {
            dealerId = store.getId();
        }
        log.info("init proofEnable dealerId={}", dealerId);
        stopWatch.lap("findDealerId");

        List<IObjectData> activities = queryAllActivity(departmentIds, dealerId);
        stopWatch.lap("queryAllActivity");

        log.info("init proofEnable1 activities={}", JSON.toJSONString(activities.stream().map(DBRecord::getId).collect(Collectors.toList())));

        return checkEnableV2(controllerContext.getTenantId(), store.getId(), activities, dealerId);
    }

    private boolean checkEnableV2(String tenantId, String storeId, List<IObjectData> activities, String dealerId) {
        if (CollectionUtils.isEmpty(activities)) {
            log.info("no fit activity.");
            return false;
        }
        GetValidActivitiesByDealerIdResult validActivitiesByDealerIdResult = unifiedActivityCommonLogicBusiness.getValidActivitiesByDealerId(controllerContext.getTenantId(), activities, dealerId);
        activities = validActivitiesByDealerIdResult.getActivities();
        log.info("init proofEnable2 activities={}", JSON.toJSONString(activities.stream().map(DBRecord::getId).collect(Collectors.toList())));

        if (CollectionUtils.isEmpty(activities)) {
            log.info("no fit validActivitiesByDealerIdResult.");
            return false;
        }

        stopWatch.lap("queryActivityByStore");
        Map<String, Boolean> enableResult = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(tenantId, storeId, dealerId, activities, true, false);
        Map<String, IObjectData> activityMap = activities.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (a, b) -> a));
        for (Map.Entry<String, Boolean> entry : enableResult.entrySet()) {
            String activityId = entry.getKey();
            Boolean res = entry.getValue();
            if (res && activityMap.containsKey(activityId)) {
                IObjectData activity = activityMap.get(activityId);
                String activityDealerId = activity.get(TPMActivityFields.DEALER_ID, String.class);
                String value = activity.get(TPMActivityFields.STORE_RANGE, String.class);
                String customerType = activity.get(TPMActivityFields.CUSTOMER_TYPE, String.class, ActivityCustomerTypeEnum.DEALER_STORE.value());
                if (!ActivityCustomerTypeEnum.DEALER_STORE.value().equals(customerType)) {
                    continue;
                }
                String unifiedStoreRange = validActivitiesByDealerIdResult.getUnifiedId2StoreRange().get(activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
                if (Strings.isNullOrEmpty(activityDealerId) && !Strings.isNullOrEmpty(value) && value.toUpperCase().contains("\"ALL\"")
                        && (Strings.isNullOrEmpty(unifiedStoreRange) || unifiedStoreRange.toUpperCase().contains("\"ALL\""))) {
                    this.hasAllDealerAllRangeActivity = true;
                    break;
                }
            }
        }
        return enableResult.containsValue(true);
    }




    private List<IObjectData> queryAllActivity(List<Integer> departmentIds, String dealerId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");
        int index = 1;
        StringBuilder pattern = new StringBuilder();

        Filter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));
        query.getFilters().add(activityStatusFilter);
        pattern.append(index++);


        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        query.getFilters().add(lifeStatusFilter);
        pattern.append(" and ").append(index++).append(" ");

        //只有含有举证节点的活动需要Enable
        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityTypeFilter.setOperator(Operator.IN);
        activityTypeFilter.setFieldValues(getProofActivityType());
        query.getFilters().add(activityTypeFilter);
        pattern.append(" and ").append(index++).append(" ");

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.EQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));
        query.getFilters().add(closeStatusFilter);
        pattern.append(" and ").append(index++).append(" ");

        Filter dealerActivityFilter = new Filter();
        dealerActivityFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerActivityFilter.setOperator(Operator.IS);
        dealerActivityFilter.setFieldValues(Lists.newArrayList());
        query.getFilters().add(dealerActivityFilter);

        if (!Strings.isNullOrEmpty(dealerId)) {
            Filter dealerIdEqualFilter = new Filter();
            dealerIdEqualFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEqualFilter.setOperator(Operator.EQ);
            dealerIdEqualFilter.setFieldValues(Lists.newArrayList(dealerId));
            query.getFilters().add(dealerIdEqualFilter);
            pattern.append("and ( ").append(index++).append(" or ").append(index++).append(" ) ");
        } else {
            pattern.append(" and ").append(index++).append(" ");
        }


        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            Filter multiDepartmentRangeFilter = new Filter();
            multiDepartmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            multiDepartmentRangeFilter.setOperator(Operator.HASANYOF);
            multiDepartmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            if (I_DESCRIBE_CACHE_SERVICE.isExistField(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
                query.getFilters().add(departmentRangeFilter);
                query.getFilters().add(multiDepartmentRangeFilter);
                pattern.append("and ( ").append(index++).append(" or ").append(index++).append(" ) ");
            } else {
                query.getFilters().add(multiDepartmentRangeFilter);
                pattern.append(" and ").append(index++).append(" ");
            }
        }

        query.setPattern(pattern.toString());
        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, controllerContext.getUser(), query, this.activityDescribe, this.activitySearchTemplate, this.isAdminRequest);
        return QueryDataUtil.find(
                serviceFacade,
                controllerContext.getUser(),
                controllerContext.getRequestContext(),
                ApiNames.TPM_ACTIVITY_OBJ,
                query,
                Lists.newArrayList(CommonFields.ID, TPMActivityFields.STORE_RANGE, TPMActivityFields.DEALER_ID, TPMActivityFields.MULTI_DEPARTMENT_RANGE, TPMActivityFields.CUSTOMER_TYPE, TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID));
    }

    private List<String> getProofActivityType() {
        return activityTypeManager.queryProofActivityTypeIds(controllerContext.getTenantId());
    }

    private List<IObjectData> queryNoDealerActivityV2(List<Integer> departmentIds) {
        List<IObjectData> activities = Lists.newArrayList();
        try {
            String sql = buildQueryNoDealerActivitySql(controllerContext.getTenantId(), departmentIds);
            //todo 改循环查
            List<Map> maps = objectDataService.findBySql(controllerContext.getTenantId(), sql);
            for (Map map : maps) {
                activities.add((IObjectData) ObjectDataDocument.of(map));
            }
        } catch (MetadataServiceException ex) {
            log.error("custom find by sql error : ", ex);
        }
        return activities;
    }

    private String buildQueryNoDealerActivitySql(String tenantId, List<Integer> departmentIds) {

        List<String> strings = departmentIds.stream().map(String::valueOf).collect(Collectors.toList()).stream().map(SqlEscaper::pg_escape).collect(Collectors.toList());
        Object o = JSON.toJSON(strings);
        String s = o.toString().replace("[", "{").replace("]", "}");
        StringBuilder sql = new StringBuilder();
        sql.append("select\n");
        sql.append("    TPMActivityObj.store_range as store_range,\n");
        sql.append("    TPMActivityObj.extend_obj_data_id as extend_obj_data_id,\n");
        sql.append("    TPMActivityObj.id as _id,\n");
        sql.append("    TPMActivityObj.customer_type as customer_type,\n");
        sql.append("    TPMActivityObj.multi_department_range as multi_department_range,\n");
        sql.append("    TPMActivityObj.dealer_id as dealer_id,\n");
        sql.append("    TPMActivityObj.last_modified_time as last_modified_time,\n");
        sql.append("    TPMActivityObj.sys_modified_time as sys_modified_time\n");
        sql.append("from\n");
        sql.append("    fmcg_tpm_activity as TPMActivityObj\n");
        sql.append("where\n");
        sql.append("    ").append(SqlEscaper.pg_op_eq("TPMActivityObj.tenant_id", tenantId)).append("\n");
        sql.append("    and TPMActivityObj.multi_department_range && ").append(SqlEscaper.pg_quote(s)).append("\n");
        sql.append("    and TPMActivityObj.is_deleted = '0'\n");
        sql.append("    and TPMActivityObj.dealer_id is null\n");
        sql.append("    and TPMActivityObj.life_status = 'normal'\n");
        sql.append("    and TPMActivityObj.activity_status = 'in_progress'\n");
        sql.append("order by\n");
        sql.append("    TPMActivityObj.last_modified_time + 0 DESC, \n");
        sql.append("    TPMActivityObj.last_modified_time + 0 DESC \n");
        sql.append("limit\n");
        sql.append("    2000\n");

        log.info("custom sql : {}", sql);
        return sql.toString();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected Enable.Result after(Enable.Arg arg, Enable.Result result) {
        if (this.hasAllDealerAllRangeActivity) {
            enableCacheService.setALLCache(controllerContext.getTenantId(), controllerContext.getUser().getUserIdOrOutUserIdIfOutUser());
        } else {
            enableCacheService.setStoreCache(controllerContext.getTenantId(), controllerContext.getUser().getUserIdOrOutUserIdIfOutUser(), arg.getStoreId(), result.isEnable());
        }
        return super.after(arg, result);
    }

    @Override
    protected void finallyDo() {
        stopWatch.logSlow(200L);
        super.finallyDo();
    }
}