package com.facishare.crm.fmcg.tpm.api.button.action;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/16 上午10:55
 */
public interface TestPreAction {

    @Data
    @ToString
    class Arg implements Serializable {
        @SerializedName("describeApiName")
        private String describeApiName;
        @SerializedName("objectDataId")
        private String objectDataId;
        private List<String> dataIds;
        @SerializedName("buttonApiName")
        private String buttonApiName;
        @SerializedName("args")
        private Map<String, Object> args;
        @SerializedName("objectData")
        private ObjectDataDocument objectData;
        @SerializedName("details")
        Map<String, List<ObjectDataDocument>> details;
        @SerializedName("bizKey")
        String bizKey;
        private String actionStage;
        private Map<String, Object> actionParams;

    }

    @Data
    @ToString
    class Result implements Serializable {
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> details;
        private String targetDescribeApiName;
        private boolean hasReturnValue;
        private Object returnValue;
        private String returnType;
        private boolean block;

        public Result(ObjectDataDocument objectData, Map<String, List<ObjectDataDocument>> details) {
            this.objectData = objectData;
            this.details = details;
            if (this.details == null) {
                this.details = Maps.newHashMap();
            }
            if (this.objectData == null) {
                this.objectData = new ObjectDataDocument();
            }
        }
    }
}
