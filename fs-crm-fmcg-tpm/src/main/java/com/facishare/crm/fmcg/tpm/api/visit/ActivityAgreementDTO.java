package com.facishare.crm.fmcg.tpm.api.visit;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/27 16:38
 */
@Data
@ToString
public class ActivityAgreementDTO implements Serializable {


    @JSONField(name = "agreement_id")
    private String agreementId;

    private String remark;

    @JSONField(name = "images_total_count")
    private long imagesTotalCount;

    private List<ActivityAgreementImageDTO> images;

    private List<ActivityAgreementDetailDTO> details;
}
