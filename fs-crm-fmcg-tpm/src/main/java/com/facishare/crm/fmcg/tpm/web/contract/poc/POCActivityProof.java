package com.facishare.crm.fmcg.tpm.web.contract.poc;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface POCActivityProof {
    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString
    class Arg extends POCBase.Arg implements Serializable {
        private boolean isAgreement;
        private IObjectData activity;
        private List<IObjectData> activityDetails;
        private IObjectData activityAgreement;
        private List<IObjectData> activityAgreementDetails;
        private String activityTypeName;
        //是否合格
        private boolean paas;
        //举证时间
        private Long date;


    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString
    class Result extends POCBase.Result implements Serializable {



    }
}
