package com.facishare.crm.fmcg.tpm.web.custom.abstraction;

import com.facishare.crm.fmcg.tpm.web.contract.kk.*;

/**
 * description : 环球佳酿 - 客开预算服务
 * <p>
 * create by @yangqf
 * create time 2022/11/14 11:33
 */
public interface ICommonBudgetService {

    FreezeValidate.Result freezeValidate(FreezeValidate.Arg arg);

    Freeze.Result freeze(Freeze.Arg arg);

    AppendFreeze.Result appendFreeze(AppendFreeze.Arg arg);

    Cancel.Result cancel(Cancel.Arg arg);

    Unfreeze.Result unfreeze(Unfreeze.Arg arg);

    WriteOffValidate.Result writeOffValidate(WriteOffValidate.Arg arg);

    WriteOff.Result writeOff(WriteOff.Arg arg);

    Close.Result close(Close.Arg arg);
}