package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.reward.dto.ActivityInformation;
import com.facishare.crm.fmcg.tpm.reward.dto.SnInformation;
import com.facishare.crm.fmcg.tpm.reward.dto.WeChatArg;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

public interface ConsumerScanInnerCode {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends WeChatArg implements Serializable {

        private String code;

        private String longitude;

        private String latitude;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private String status;

        @JSONField(name = "activity_information")
        @JsonProperty(value = "activity_information")
        @SerializedName("activity_information")
        private ActivityInformation activityInformation;

        @JSONField(name = "sn_information")
        @JsonProperty(value = "sn_information")
        @SerializedName("sn_information")
        private SnInformation snInformation;
    }
}