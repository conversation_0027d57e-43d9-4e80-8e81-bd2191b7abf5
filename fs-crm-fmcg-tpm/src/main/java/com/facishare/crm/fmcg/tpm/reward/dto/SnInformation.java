package com.facishare.crm.fmcg.tpm.reward.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/3/28 17:12
 */
@Data
@ToString
@Builder
public class SnInformation implements Serializable {

    private String code;

    @JSONField(name = "unlock_qr_code_base_64")
    @JsonProperty(value = "unlock_qr_code_base_64")
    @SerializedName("unlock_qr_code_base_64")
    private String unlockQrCodeBase64;

    @JSONField(name = "product_name")
    @JsonProperty(value = "product_name")
    @SerializedName("product_name")
    private String productName;

    @JSONField(name = "product_code")
    @JsonProperty(value = "product_code")
    @SerializedName("product_code")
    private String productCode;


    @JSONField(name = "reward_method")
    @JsonProperty(value = "reward_method")
    @SerializedName("reward_method")
    private String rewardMethod;

    private String date;

    private String store;

    private boolean sold;
}
