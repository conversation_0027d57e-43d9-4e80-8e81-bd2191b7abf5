package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetWhereConditionVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/2 下午3:21
 */
@Data
@ToString
public class BudgetWhereConditionEntity implements Serializable {

    public static final String F_CONNECTOR = "connector";
    public static final String F_FILTERS = "filters";

    @Property(F_CONNECTOR)
    private String connector;

    @Embedded(F_FILTERS)
    private List<BudgetTriggerConditionEntity> triggerConditions;

    public static BudgetWhereConditionEntity fromVO(BudgetWhereConditionVO vo) {
        if (vo == null) {
            return null;
        }
        BudgetWhereConditionEntity budgetWhereConditionEntity = new BudgetWhereConditionEntity();
        budgetWhereConditionEntity.setConnector(vo.getConnector());
        if (CollectionUtils.isEmpty(vo.getTriggerConditions())) {
            budgetWhereConditionEntity.setTriggerConditions(new ArrayList<>());
        } else {
            budgetWhereConditionEntity.setTriggerConditions(vo.getTriggerConditions().stream().map(BudgetTriggerConditionEntity::fromVO).collect(Collectors.toList()));
        }
        return budgetWhereConditionEntity;
    }

    public static BudgetWhereConditionVO toVO(BudgetWhereConditionEntity entity) {
        if (entity == null) {
            return null;
        }
        BudgetWhereConditionVO budgetWhereConditionVO = new BudgetWhereConditionVO();
        budgetWhereConditionVO.setConnector(entity.getConnector());
        if (CollectionUtils.isEmpty(entity.getTriggerConditions())) {
            budgetWhereConditionVO.setTriggerConditions(new ArrayList<>());
        } else {
            budgetWhereConditionVO.setTriggerConditions(entity.getTriggerConditions().stream().map(BudgetTriggerConditionEntity::toVO).collect(Collectors.toList()));
        }
        return budgetWhereConditionVO;
    }
}
