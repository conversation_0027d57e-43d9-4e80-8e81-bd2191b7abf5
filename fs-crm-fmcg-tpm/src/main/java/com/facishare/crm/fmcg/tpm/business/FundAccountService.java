package com.facishare.crm.fmcg.tpm.business;

import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.AuthorizationDetailFields;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.FAccountAuthorizationFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFundAccountService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2023/7/11 18:11
 */

@Service
public class FundAccountService implements IFundAccountService {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public List<IObjectData> getFundAccountsInWriteOff(String tenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(FAccountAuthorizationFields.AUTHORIZED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.TPM_DEALER_ACTIVITY_COST));
        query.setFilters(Lists.newArrayList(apiNameFilter));
        List<IObjectData> masterDataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.F_ACCOUNT_AUTHORIZATION_OBJ, query, Lists.newArrayList(CommonFields.ID));
        if (CollectionUtils.isEmpty(masterDataList)) {
            return new ArrayList<>();
        }
        IObjectData masterData = masterDataList.get(0);

        SearchTemplateQuery detailQuery = new SearchTemplateQuery();
        detailQuery.setOffset(0);
        detailQuery.setLimit(-1);
        Filter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(AuthorizationDetailFields.MASTER_ID);
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(Lists.newArrayList(masterData.getId()));
        detailQuery.setFilters(Lists.newArrayList(masterIdFilter));

        List<String> accountIds = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.AUTHORIZATION_DETAIL_OBJ, detailQuery, Lists.newArrayList(AuthorizationDetailFields.AUTHORIZE_ACCOUNT_ID))
                .stream().map(v -> v.get(AuthorizationDetailFields.AUTHORIZE_ACCOUNT_ID, String.class)).collect(Collectors.toList());

        return serviceFacade.findObjectDataByIds(tenantId, accountIds, ApiNames.FUND_ACCOUNT_OBJ);
    }
}
