package com.facishare.crm.fmcg.tpm.web.poc;


import com.facishare.crm.fmcg.common.apiname.*;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.tpm.business.abstraction.IPOCTriggerActionService;
import com.facishare.crm.fmcg.tpm.utils.Random;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.TriggerAction;
import com.facishare.crm.fmcg.tpm.web.contract.poc.POCActivityProof;
import com.facishare.crm.fmcg.tpm.web.poc.abstraction.AbstractPOCHandler;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ActivityProofPOCHandler extends AbstractPOCHandler<POCActivityProof.Arg, POCActivityProof.Result> {


    @Resource
    private IPOCTriggerActionService pocTriggerActionService;
    @Resource
    private ProofImgHandler proofImgHandler;

    @Override
    protected void before(POCActivityProof.Arg arg) {

        IObjectData objectData = initActivityProof(arg);


        List<IObjectData> details = initActivityProofDetail(arg);

        BigDecimal total = new BigDecimal("0");
        for (IObjectData detail : details) {
            BigDecimal cost = detail.get(TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD, BigDecimal.class);
            if (cost == null) {
                cost = new BigDecimal("0");
            }
            Integer amount = detail.get(TPMActivityProofDetailFields.AMOUNT, Integer.class);
            if (amount == 0) {
                amount = 0;
            }
            total = total.add(cost.multiply(BigDecimal.valueOf(amount)));
        }
        objectData.set(TPMActivityProofFields.ACTUAL_TOTAL,total);
        arg.setMasterObj(objectData);
        arg.setDetails(details);
        super.before(arg);
    }


    @Override
    protected POCActivityProof.Result doAct(POCActivityProof.Arg arg, POCActivityProof.Result result) {
        ApiContext context = ApiContextManager.getContext();


        BaseObjectSaveAction.Result createResult = pocTriggerActionService.triggerAction(TriggerAction.Arg.builder().user(User.systemUser(context.getTenantId())).objectData(arg.getMasterObj()).details(arg.getDetails()).apiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ)
                .detailApiName(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ).actionName("Add").triggerFlow(false).triggerFlow(false).build());
        result.setMasterObj(createResult.getObjectData().toObjectData());
        result.setDetails(createResult.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()));
        return result;
    }

    @Override
    protected POCActivityProof.Result after(POCActivityProof.Arg arg, POCActivityProof.Result result) {

        return super.after(arg, result);
    }

    private IObjectData initActivityProof(POCActivityProof.Arg arg) {
        IObjectData objectData = super.preInitObj();
        IObjectData activityAgreement;
        if (arg.isAgreement()) {
            activityAgreement = arg.getActivityAgreement();
            objectData.set(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, activityAgreement.getId());
            objectData.set(TPMActivityProofFields.ACTIVITY_ID, activityAgreement.get(TPMActivityProofFields.ACTIVITY_ID));
        } else {
            activityAgreement = arg.getActivity();
            objectData.set(TPMActivityProofFields.ACTIVITY_ID, activityAgreement.getId());
        }
        List<String> nPathByKey;
        if (arg.isPaas()) {
            nPathByKey = proofImgHandler.getNPathByKey(arg.getActivityTypeName(), 1);
        } else {
            nPathByKey = proofImgHandler.getNPathByKey(arg.getActivityTypeName(), 2);
        }

        String nPathUrl = nPathByKey.get(Random.random(1, nPathByKey.size()).get(0));

        List<Map<String, String>> imgList = Lists.newArrayList();
        Map<String, String> imgMap = new HashMap<>();
        imgMap.put("path", nPathUrl);
        imgMap.put("ext", "jpg");
        imgList.add(imgMap);
        objectData.set(TPMActivityProofFields.PROOF_IMAGES, imgList);


        objectData.set(CommonFields.CREATE_BY, activityAgreement.get(CommonFields.OWNER));
        objectData.set("data_own_department", activityAgreement.get(CommonFields.DATA_OWN_DEPARTMENT));
        objectData.set(TPMActivityProofFields.REMARK, arg.getDate());


        objectData.set(TPMActivityProofFields.DEALER_ID, activityAgreement.get(TPMActivityAgreementFields.DEALER_ID));
        objectData.set(TPMActivityProofFields.STORE_ID, activityAgreement.get(TPMActivityAgreementFields.STORE_ID));
        objectData.set(CommonFields.OBJECT_DESCRIBE_API_NAME, ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        objectData.set(CommonFields.OWNER, activityAgreement.get(CommonFields.OWNER));


        return objectData;
    }

    private List<IObjectData> initActivityProofDetail(POCActivityProof.Arg arg) {
        List<IObjectData> details = Lists.newArrayList();
        if (arg.isAgreement()) {
            for (IObjectData activityAgreementDetail : arg.getActivityAgreementDetails()) {
                IObjectData objectData = super.preInitObj();

                objectData.set(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, activityAgreementDetail.getId());
                objectData.set(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID, activityAgreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID));
                objectData.set(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD, activityAgreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD));
                objectData.set(TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD, activityAgreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_COST_STANDARD));
                objectData.set(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, activityAgreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_ITEM_ID));
                objectData.set(TPMActivityProofDetailFields.AMOUNT, 1);
                objectData.set(CommonFields.OBJECT_DESCRIBE_API_NAME, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
                objectData.set(CommonFields.OWNER, activityAgreementDetail.get(CommonFields.OWNER));

                details.add(objectData);
            }
        } else {
            for (IObjectData activityDetail : arg.getActivityDetails()) {
                IObjectData objectData = super.preInitObj();

                objectData.set(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID, activityDetail.getId());
                objectData.set(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD, activityDetail.get(TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD));
                objectData.set(TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD, activityDetail.get(TPMActivityDetailFields.ACTIVITY_COST_STANDARD));

                objectData.set(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, activityDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_ITEM_ID));
                objectData.set(TPMActivityProofDetailFields.AMOUNT, 1);
                objectData.set(CommonFields.OBJECT_DESCRIBE_API_NAME, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
                objectData.set(CommonFields.OWNER, activityDetail.get(CommonFields.OWNER));

                details.add(objectData);
            }

        }
        return details;

    }
}
