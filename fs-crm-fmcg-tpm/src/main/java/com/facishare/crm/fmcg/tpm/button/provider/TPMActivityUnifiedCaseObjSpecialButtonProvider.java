package com.facishare.crm.fmcg.tpm.button.provider;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.button.abs.AbstractTPMSpecialButtonProvider;
import com.facishare.crm.fmcg.tpm.utils.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/13 16:56
 */
@Component
public class TPMActivityUnifiedCaseObjSpecialButtonProvider  extends AbstractTPMSpecialButtonProvider {

    private static final Logger log = LoggerFactory.getLogger(TPMActivityUnifiedCaseObjSpecialButtonProvider.class);

    @Override
    public String getApiName() {
        return ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ;
    }

    @Override
    public List<IButton> getSpecialButtons() {

        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.CLOSE_TPM_ACTIVITY));
        return buttons;
    }
}
