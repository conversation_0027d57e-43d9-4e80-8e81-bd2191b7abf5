package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/1/6 16:26
 */
@Data
@ToString
public class ActivityReportDatumVO implements Serializable {

    @JSONField(name = "template_id")
    @JsonProperty(value = "template_id")
    @SerializedName("template_id")
    private String templateId;

    @JSONField(name = "object_api_name")
    @JsonProperty(value = "object_api_name")
    @SerializedName("object_api_name")
    private String objectApiName;

    @JSONField(name = "object_display_name")
    @JsonProperty(value = "object_display_name")
    @SerializedName("object_display_name")
    private String objectDisplayName;

    private String type;

    @JSONField(name = "count")
    @JsonProperty(value = "count")
    @SerializedName("count")
    private long count = 0;

    @JSONField(name = "total_count")
    @JsonProperty(value = "total_count")
    @SerializedName("total_count")
    private long totalCount = 0;

    @JSONField(name = "schedule_count")
    @JsonProperty(value = "schedule_count")
    @SerializedName("schedule_count")
    private long scheduleCount = 0;

    @JSONField(name = "in_progress_count")
    @JsonProperty(value = "in_progress_count")
    @SerializedName("in_progress_count")
    private long inProgressCount = 0;

    @JSONField(name = "ended_count")
    @JsonProperty(value = "ended_count")
    @SerializedName("ended_count")
    private long endedCount = 0;

}
