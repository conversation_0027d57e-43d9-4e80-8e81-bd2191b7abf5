package com.facishare.crm.fmcg.tpm.service.abstraction;

/**
 * <AUTHOR>
 * @date 2021/1/25 下午5:44
 */
public interface BuryModule {

    class TPM {
        public static final String TPM_ACTIVITY_ITEM = "tpm_activity_item";
        public static final String TPM_ACTIVITY = "tpm_activity";
        public static final String TPM_ACTIVITY_TYPE_TEMPLATE = "tpm_activity_type_template";
        public static final String TPM_ACTIVITY_AGREEMENT = "tpm_activity_agreement";
        public static final String TPM_ACTIVITY_PROOF = "tpm_activity_proof";
        public static final String TPM_ACTIVITY_PROOF_AUDIT = "tpm_activity_proof_audit";
        public static final String TPM_ACTIVITY_BUDGET = "tpm_activity_budget";
        public static final String TPM_ACTIVITY_BUDGET_ADJUST = "tpm_activity_budget_adjust";
        public static final String TPM_ACTIVITY_BUDGET_DETAIL = "tpm_activity_budget_detail";
        public static final String TPM_DEALER_ACTIVITY_COST = "tpm_dealer_activity_cost";
        public static final String TPM_STORE_WRITE_OFF = "tpm_store_write_off";

        public static final String TPM_ACTIVITY_NODE = "tpm_activity_node";

        public static final String TPM_ACTIVITY_TYPE = "tpm_activity_type";
        public static final String TPM_ACTIVITY_ITEM_COST = "tpm_activity_item_cost";
        public static final String TPM_BUDGET_BUSINESS_SUBJECT = "tpm_budget_business_subject";

        public static final String TPM_ACTIVITY_UNIFIED_CASE = "tpm_activity_unified_case";

        public static final String TPM_ACTIVITY_REWARD_RULE = "tpm_activity_reward_rule";
        public static final String TPM_AGREEMENT_PRESET_PROOF_NUMBER = "tpm_agreement_preset_proof_number";
    }

    class ACTIVITY_TYPE_NODE {
        public static final String PLAN = "tpm_activity_plan";
        public static final String AGREEMENT = "tpm_activity_agreement";
        public static final String PROOF = "tpm_activity_proof";
        public static final String AUDIT = "tpm_activity_audit";
        public static final String COST_ASSIGN = "tpm_activity_cost_assign";
        public static final String STORE_WRITE_OFF = "tpm_activity_store_write_off";
        public static final String WRITE_OFF = "tpm_activity_write_off";
        public static final String CUSTOM = "tpm_activity_custom";
    }

    class CASH_TYPE {
        public static final String IS_CASHING_GOODS_KEY = "isCashingGoods";
        public static final String CASH = "cash";
        public static final String GOODS = "goods";
    }

    class STORE_RANGE {
        public static final String ALL = "all";
        public static final String CONDITION = "condition";
        public static final String FIXED = "fixed";
    }

    class Budget {
        public static final String BUDGET_ACCOUNT = "budget_account";

        public static final String BUDGET_ACCOUNT_DETAIL = "budget_account_detail";

        public static final String BUDGET_TRANSFER_DETAIL_ADD = "budget_transfer_detail_add";
        public static final String BUDGET_TRANSFER_DETAIL_DEDUCT = "budget_transfer_detail_deduct";
        public static final String BUDGET_TRANSFER_DETAIL_ADJUST = "budget_transfer_detail_adjust";

        public static final String BUDGET_STATISTIC_TABLE = "budget_statistic_table";

        public static final String BUDGET_TYPE_LABEL = "budget_type_label_%s";

        public static final String BUDGET_DISASSEMBLY = "budget_disassembly";
        public static final String BUDGET_DISASSEMBLY_NEW_ACCOUNT = "budget_disassembly_new_account";
        public static final String BUDGET_DISASSEMBLY_EDIT_ACCOUNT = "budget_disassembly_edit_account";

        public static final String BUDGET_CONSUME_RULE = "budget_consume_rule";
        public static final String BUDGET_CONSUME_RULE_PRESET_OBJ = "budget_consume_rule_preset_obj";
        public static final String BUDGET_CONSUME_RULE_CUSTOM_OBJ = "budget_consume_rule_custom_obj";
        public static final String BUDGET_CONSUME_RULE_FREEZE_DEDUCTION = "budget_consume_rule_freeze_deduction";
        public static final String BUDGET_CONSUME_RULE_DEDUCTION = "budget_consume_rule_deduction";
        public static final String BUDGET_CONSUME_RULE_OBJ_COUNT = "budget_consume_rule_obj_count";
        public static final String BUDGET_CONSUME_RULE_DEDUCT_SELF = "budget_consume_rule_deduct_self";
        public static final String BUDGET_CONSUME_RULE_DEDUCT_OTHER = "budget_consume_rule_deduct_other";
        public static final String BUDGET_CONSUME_RULE_AUTOMATIC = "budget_consume_rule_automatic";
        public static final String BUDGET_CONSUME_RULE_MANUAL = "budget_consume_rule_manual";

        public static final String BUDGET_ACCRUAL_RULE = "budget_accrual_rule";
        public static final String BUDGET_ACCRUAL_RULE_PRESET_OBJ = "budget_accrual_rule_preset_obj";
        public static final String BUDGET_ACCRUAL_RULE_CUSTOM_OBJ = "budget_accrual_rule_custom_obj";
        public static final String BUDGET_ACCRUAL_RULE_ENABLE = "budget_accrual_rule_enable";
        public static final String BUDGET_ACCRUAL_RULE_DISABLE = "budget_accrual_rule_disable";
        public static final String BUDGET_ACCRUAL_RULE_OBJ_COUNT = "budget_accrual_rule_obj_count";

        public static final String BUDGET_CARRY_FORWARD = "budget_carry_forward";

        public static final String BUDGET_ACCRUAL = "budget_accrual";

    }

    class TPM_AMOUNT {
        public static final String ACTIVITY_AMOUNT = "activity_amount";
        public static final String ACTIVITY_ACTUAL_AMOUNT = "activity_actual_amount";
        public static final String ACTUAL_TOTAL_AMOUNT = "actual_total_amount";
        public static final String ACTUAL_TOTAL = "actual_total";
        public static final String AUDIT_TOTAL = "audit_total";
        public static final String CONFIRMED_AMOUNT = "confirmed_amount";
        public static final String AUDITED_AMOUNT = "audited_amount";
    }

    class BUDGET_AMOUNT {
        public static final String BASE_AMOUNT = "base_amount";
        public static final String TOTAL_AMOUNT = "total_amount";
        public static final String TRANSFER_IN_AMOUNT = "transfer_in_amount";
        public static final String TRANSFER_OUT_AMOUNT = "transfer_out_amount";
        public static final String CALCULATE_APPLY_AMOUNT = "calculate_apply_amount";
        public static final String CARRY_FORWARD_AMOUNT = "carry_forward_amount";
    }

}
