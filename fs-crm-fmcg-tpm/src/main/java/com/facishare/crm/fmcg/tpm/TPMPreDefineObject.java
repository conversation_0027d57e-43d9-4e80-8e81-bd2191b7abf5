package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.common.utils.SyncDataUtil;
import com.facishare.paas.appframework.core.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/4 5:33 PM
 */
@SuppressWarnings("all")
@Slf4j
public enum TPMPreDefineObject implements PreDefineObject {
    TPMActivityItemObj("TPMActivityItemObj", Module.TPM),
    TPMActivityItemCostStandardObj("TPMActivityItemCostStandardObj", Module.TPM),
    TPMActivityObj("TPMActivityObj", Module.TPM),
    TPMActivityStoreObj("TPMActivityStoreObj", Module.TPM),
    TPMActivityDetailObj("TPMActivityDetailObj", Module.TPM),
    TPMActivityAgreementObj("TPMActivityAgreementObj", Module.TPM),
    TPMActivityAgreementDetailObj("TPMActivityAgreementDetailObj", Module.TPM),
    TPMActivityProofObj("TPMActivityProofObj", Module.TPM),
    TPMActivityProofDetailObj("TPMActivityProofDetailObj", Module.TPM),
    TPMActivityProofAuditObj("TPMActivityProofAuditObj", Module.TPM),
    TPMActivityProofAuditDetailObj("TPMActivityProofAuditDetailObj", Module.TPM),
    TPMDealerActivityObj("TPMDealerActivityObj", Module.TPM),
    TPMDealerActivityCostObj("TPMDealerActivityCostObj", Module.TPM),
    TPMActivityBudgetObj("TPMActivityBudgetObj", Module.OLD_BUDGET),
    TPMActivityBudgetDetailObj("TPMActivityBudgetDetailObj", Module.OLD_BUDGET),
    TPMActivityBudgetAdjustObj("TPMActivityBudgetAdjustObj", Module.OLD_BUDGET),
    TPMBudgetBusinessSubjectObj("TPMBudgetBusinessSubjectObj", Module.BUDGET),
    TPMBudgetAccountObj("TPMBudgetAccountObj", Module.BUDGET),
    TPMBudgetAccountDetailObj("TPMBudgetAccountDetailObj", Module.BUDGET),
    TPMBudgetTransferDetailObj("TPMBudgetTransferDetailObj", Module.BUDGET),
    TPMBudgetCarryForwardObj("TPMBudgetCarryForwardObj", Module.BUDGET),
    TPMBudgetCarryForwardDetailObj("TPMBudgetCarryForwardDetailObj", Module.BUDGET),
    TPMBudgetDisassemblyObj("TPMBudgetDisassemblyObj", Module.BUDGET),
    TPMBudgetDisassemblyNewDetailObj("TPMBudgetDisassemblyNewDetailObj", Module.BUDGET),
    TPMBudgetDisassemblyExistsDetailObj("TPMBudgetDisassemblyExistsDetailObj", Module.BUDGET),
    TPMCustomActivityPlanObj("tpm_activity_plan__c", Module.OTHER),
    TPMBudgetStatisticTableObj("TPMBudgetStatisticTableObj", Module.BUDGET),
    TPMStoreWriteOffObj("TPMStoreWriteOffObj", Module.TPM),
    TPMBudgetAccrualObj("TPMBudgetAccrualObj", Module.BUDGET),
    TPMBudgetAccrualDetailObj("TPMBudgetAccrualDetailObj", Module.BUDGET),
    TPMBudgetProvisionObj("TPMBudgetProvisionObj", Module.BUDGET),
    TPMBudgetProvisionDetailObj("TPMBudgetProvisionDetailObj", Module.BUDGET),
    TPMActivityCashingProductObj("TPMActivityCashingProductObj", Module.TPM),
    TPMActivityAgreementCashingProductObj("TPMActivityAgreementCashingProductObj", Module.TPM),
    TPMStoreWriteOffCashingProductObj("TPMStoreWriteOffCashingProductObj", Module.TPM),
    TPMDealerActivityCashingProductObj("TPMDealerActivityCashingProductObj", Module.TPM),
    TPMActivityUnifiedCaseObj("TPMActivityUnifiedCaseObj", Module.TPM),
    TPMActivityCashingProductScopeObj("TPMActivityCashingProductScopeObj", Module.TPM),
    TPMActivityDealerScopeObj("TPMActivityDealerScopeObj", Module.TPM),
    TPMActivityUnifiedCaseProductRangeObj("TPMActivityUnifiedCaseProductRangeObj", Module.TPM),
    TPMActivityRewardDetailObj("TPMActivityRewardDetailObj", Module.TPM),
    RedPacketRecordObj("RedPacketRecordObj", Module.TPM),
    RedPacketRecordDetailObj("RedPacketRecordDetailObj", Module.TPM),
    StorePromotionRecordObj("StorePromotionRecordObj", Module.TPM),
    ExpenseClaimFormObj("ExpenseClaimFormObj", Module.OTHER),
    ExpenseClaimFormDetailObj("ExpenseClaimFormDetailObj", Module.OTHER),
    WithdrawRecordObj("WithdrawRecordObj", Module.TPM),
    TPMProofTimePeriodDetailObj("TPMProofTimePeriodDetailObj", Module.TPM),
    TPMActivityProofDisplayImgObj("TPMActivityProofDisplayImgObj", Module.TPM),
    TPMActivityProofProductDetailObj("TPMActivityProofProductDetailObj", Module.TPM),
    TPMActivityProofMaterialDetailObj("TPMActivityProofMaterialDetailObj", Module.TPM),
    TPMDefaultSyncDataObj("TPMDefaultSyncDataObj", Module.OTHER);

    private final String apiName;
    private final Module module;

    private static final String PACKAGE_NAME = TPMPreDefineObject.class.getPackage().getName();

    TPMPreDefineObject(String apiName, Module module) {
        this.apiName = apiName;
        this.module = module;
    }

    public static TPMPreDefineObject getEnum(String apiName) {
        List<TPMPreDefineObject> list = Arrays.asList(TPMPreDefineObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (TPMPreDefineObject object : TPMPreDefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return this.apiName;
    }

    public Module getModule() {
        return this.module;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String clazz;
        if (SyncDataUtil.isSyncDataAll(RequestContextManager.getContext())) {
            clazz = PACKAGE_NAME + ".action." + TPMPreDefineObject.TPMDefaultSyncDataObj.apiName + actionCode + "Action";
        } else {
            clazz = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        }
        return new ActionClassInfo(clazz);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

    public enum Module {
        TPM("TPM"),
        OLD_BUDGET("OLD_BUDGET"),
        BUDGET("BUDGET"),
        OTHER("OTHER");

        Module(String value) {
            this.value = value;
        }

        private String value;

        public String value() {
            return this.value;
        }
    }
}
