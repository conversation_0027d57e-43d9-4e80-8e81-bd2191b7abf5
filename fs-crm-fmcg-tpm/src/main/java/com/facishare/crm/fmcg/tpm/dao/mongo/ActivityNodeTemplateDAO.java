package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeTemplateVO;
import com.facishare.paas.appframework.core.exception.ValidateException;
import de.lab4inf.math.util.Strings;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:36
 */
//IgnoreI18nFile
public class ActivityNodeTemplateDAO extends UniqueIdBaseDAO<ActivityNodeTemplatePO> {

    protected ActivityNodeTemplateDAO(Class<ActivityNodeTemplatePO> clazz) {
        super(clazz);
    }

    public ActivityNodeTemplatePO findActivityPlanNodeTemplate(String tenantId) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityNodeTemplatePO.F_PACKAGE).equal(PackageType.SYSTEM.value())
                .field(ActivityNodeTemplatePO.F_OBJECT_API_NAME).equal(ApiNames.TPM_ACTIVITY_OBJ);
        return query.get();
    }

    public ActivityNodeTemplatePO findByApiName(String tenantId, String apiName) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityNodeTemplatePO.F_OBJECT_API_NAME).equal(apiName);
        return query.get();
    }

    public void edit(String tenantId,
                     int operator,
                     String uniqueId,
                     ActivityNodeTemplateVO template) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId)
                .field(ActivityNodeTemplatePO.F_VERSION).equal(template.getVersion());

        UpdateOperations<ActivityNodeTemplatePO> updateOperations = mongoContext.createUpdateOperations(ActivityNodeTemplatePO.class)
                .set(ActivityNodeTemplatePO.F_NAME, template.getName())
                .set(ActivityNodeTemplatePO.F_DESCRIPTION, template.getDescription())
                .set(ActivityNodeTemplatePO.F_OBJECT_API_NAME, template.getObjectApiName())
                .set(ActivityNodeTemplatePO.F_REFERENCE_FIELD_API_NAME, template.getReferenceFieldApiName())
                .set(ActivityNodeTemplatePO.F_EXCEPTION_STATUS, template.getExceptionStatus())
                .set(ActivityNodeTemplatePO.F_NODE_EXCEPTION_INFO, template.getNodeExceptionInfo())
                .inc(ActivityNodeTemplatePO.F_VERSION)
                .set(MongoPO.F_LAST_UPDATER, operator)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis());

        if (!mongoContext.update(query, updateOperations, false).getUpdatedExisting()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_D_A_O_0));
        }
    }


    public void edit(String tenantId,
                     int operator,
                     String uniqueId,
                     String name,
                     String description) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        UpdateOperations<ActivityNodeTemplatePO> updateOperations = mongoContext.createUpdateOperations(ActivityNodeTemplatePO.class)
                .set(ActivityNodeTemplatePO.F_NAME, name)
                .set(ActivityNodeTemplatePO.F_DESCRIPTION, description)
                .inc(ActivityNodeTemplatePO.F_VERSION)
                .set(MongoPO.F_LAST_UPDATER, operator)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis());

        mongoContext.update(query, updateOperations);
    }

    public boolean isDuplicateName(String tenantId, String name) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityNodeTemplatePO.F_NAME).equal(name);
        return query.countAll() > 0;
    }

    public boolean isDuplicateName(String tenantId, String uniqueId, String name) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(MongoPO.F_UNIQUE_ID).notEqual(uniqueId)
                .field(ActivityNodeTemplatePO.F_NAME).equal(name);
        return query.countAll() > 0;
    }

    public boolean isDuplicateApiName(String tenantId, String apiName) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityNodeTemplatePO.F_OBJECT_API_NAME).equal(apiName);
        return query.countAll() > 0;
    }

    public boolean isDuplicateApiName(String tenantId, String uniqueId, String apiName) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(MongoPO.F_UNIQUE_ID).notEqual(uniqueId)
                .field(ActivityNodeTemplatePO.F_OBJECT_API_NAME).equal(apiName);
        return query.countAll() > 0;
    }

    public List<ActivityNodeTemplatePO> list(String tenantId, String keyword, int limit, int offset) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .order("-" + MongoPO.F_LAST_UPDATE_TIME)
                .limit(limit)
                .offset(offset);

        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(ActivityNodeTemplatePO.F_NAME).containsIgnoreCase(keyword);
        }

        return query.asList();
    }

    /**
     * 查询活动节点模板列表
     * 查询条件：
     * 1. 未删除的记录
     * 2. 按ID排序，分页获取
     * 3. 状态为正常，或异常但异常次数小于3次
     *
     * @param limit 每页记录数
     * @param offset 偏移量
     * @return 活动节点模板列表
     */
    public List<ActivityNodeTemplatePO> query(int limit, int offset) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .order(MongoPO.F_ID)
                .limit(limit)
                .offset(offset);

        // 正常状态 OR (异常状态 AND (异常次数不存在 OR 异常次数小于3))
        query.or(
                query.criteria(ActivityNodeTemplatePO.F_EXCEPTION_STATUS).equal(ExceptionStatusType.NORMAL.value()),
                query.criteria(ActivityNodeTemplatePO.F_EXCEPTION_STATUS).notEqual(ExceptionStatusType.NORMAL.value())
                        .and(
                                query.or(
                                        query.criteria(ActivityNodeTemplatePO.F_EXCEPTION_COUNT).doesNotExist(),
                                        query.criteria(ActivityNodeTemplatePO.F_EXCEPTION_COUNT).lessThan(3)
                                )
                        )
        );
        
        return query.asList();
    }

    public List<ActivityNodeTemplatePO> allListByTenantId(String tenantId, int offset, int limit) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .order(MongoPO.F_ID)
                .limit(limit)
                .offset(offset);
        return query.asList();
    }

    public List<ActivityNodeTemplatePO> list(String tenantId, Collection<String> ids) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).in(ids)
                .field(MongoPO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    public List<ActivityNodeTemplatePO> sysTemList(String tenantId) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityNodeTemplatePO.F_PACKAGE).equal(PackageType.SYSTEM.value())
                .field(MongoPO.F_IS_DELETED).equal(false)
                .order(MongoPO.F_ID);
        return query.asList();
    }

    public List<String> usedApiNames(String tenantId) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false);
        return query.asList().stream().map(ActivityNodeTemplatePO::getObjectApiName).distinct().collect(Collectors.toList());
    }

    public long count(String tenantId, String keyword) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false);

        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(ActivityNodeTemplatePO.F_NAME).containsIgnoreCase(keyword);
        }

        return query.countAll();
    }

    public void initSystemTemplate(String tenantId, int operator, boolean storeWriteOff) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityNodeTemplatePO.F_PACKAGE).equal(PackageType.SYSTEM.value());
        Map<String, ActivityNodeTemplatePO> templateMap = query.asList().stream().collect(Collectors.toMap(ActivityNodeTemplatePO::getType, v -> v));

        final long now = System.currentTimeMillis();
        if (TPMGrayUtils.skipUnifiedCaseHandler(tenantId)) {
            if (!templateMap.containsKey(NodeType.PLAN_TEMPLATE.value())) {
                add(tenantId, operator, NodeType.PLAN_TEMPLATE, "活动方案", ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, "", now + 7);
            }
        }
        if (!templateMap.containsKey(NodeType.PLAN.value())) {
            add(tenantId, operator, NodeType.PLAN, "活动申请", ApiNames.TPM_ACTIVITY_OBJ, "", now + 6);
        }
        if (!templateMap.containsKey(NodeType.AGREEMENT.value())) {
            add(tenantId, operator, NodeType.AGREEMENT, "活动协议", ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.ACTIVITY_ID, now + 5);
        }
        if (!templateMap.containsKey(NodeType.PROOF.value())) {
            add(tenantId, operator, NodeType.PROOF, "活动举证", ApiNames.TPM_ACTIVITY_PROOF_OBJ, TPMActivityProofFields.ACTIVITY_ID, now + 4);
        }
        if (!templateMap.containsKey(NodeType.AUDIT.value())) {
            add(tenantId, operator, NodeType.AUDIT, "活动检核", ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, TPMActivityProofAuditFields.ACTIVITY_ID, now + 3);
        }
        if (!templateMap.containsKey(NodeType.WRITE_OFF.value())) {
            add(tenantId, operator, NodeType.WRITE_OFF, "费用核销", ApiNames.TPM_DEALER_ACTIVITY_COST, TPMDealerActivityCostFields.ACTIVITY_ID, now + 2);
        }
        if (!templateMap.containsKey(NodeType.STORE_WRITE_OFF.value()) && storeWriteOff) {
            add(tenantId, operator, NodeType.STORE_WRITE_OFF, "门店费用核销", ApiNames.TPM_STORE_WRITE_OFF_OBJ, TPMDealerActivityCostFields.ACTIVITY_ID, now + 1);
        }
        if (!templateMap.containsKey(NodeType.COST_ASSIGN.value())) {
            add(tenantId, operator, NodeType.COST_ASSIGN, "门店费用发放", ApiNames.SALES_ORDER_OBJ, TPMDealerActivityCostFields.ACTIVITY_ID, now);
        }
    }

    public void initSystemTPModeTemplate(String tenantId, int operator) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityNodeTemplatePO.F_PACKAGE).equal(PackageType.SYSTEM.value());
        Map<String, ActivityNodeTemplatePO> templateMap = query.asList().stream().collect(Collectors.toMap(ActivityNodeTemplatePO::getType, v -> v));

        final long now = System.currentTimeMillis();
        if (TPMGrayUtils.skipUnifiedCaseHandler(tenantId) && !templateMap.containsKey(NodeType.PLAN_TEMPLATE.value())) {
            add(tenantId, operator, NodeType.PLAN_TEMPLATE, "活动方案", ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, "", now + 7);
        }
        if (!templateMap.containsKey(NodeType.PLAN.value())) {
            add(tenantId, operator, NodeType.PLAN, "活动申请", ApiNames.TPM_ACTIVITY_OBJ, "", now + 6);
        }
    }

    private void add(String tenantId, int operator, NodeType type, String name, String apiName, String fieldApiName, long now) {
        ActivityNodeTemplatePO po = new ActivityNodeTemplatePO();
        po.setName(name);
        po.setDescription("");
        if ("cost_assign".equals(type.value())) {
            po.setDescription("用于给门店进行费用发放");
        }
        po.setObjectApiName(apiName);
        po.setReferenceFieldApiName(fieldApiName);
        po.setPackageType(PackageType.SYSTEM.value());
        po.setType(type.value());
        po.setStatus(StatusType.NORMAL.value());
        po.setVersion(0);
        po.setTenantId(tenantId);
        po.setExceptionStatus(ExceptionStatusType.NORMAL.value());
        po.setExceptionCount(0);
        po.setNodeExceptionInfo(new NodeExceptionInfoEntity(ExceptionStatusType.NORMAL.value(), ExceptionStatusType.NORMAL.value()));
        super.add(tenantId, operator, po);
    }

    public List<ActivityNodeTemplatePO> allCostAssignList(List<String> tenantIds) {
        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityNodeTemplatePO.F_OBJECT_API_NAME).equal(ApiNames.SALES_ORDER_OBJ)
                .field(MongoPO.F_TENANT_ID).notIn(tenantIds);
        return query.asList();
    }


    public void editExceptionStatus(String tenantId, String uniqueId, ActivityNodeTemplatePO nodeTemplate) {

        Query<ActivityNodeTemplatePO> query = mongoContext.createQuery(ActivityNodeTemplatePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        UpdateOperations<ActivityNodeTemplatePO> updateOperations = mongoContext.createUpdateOperations(ActivityNodeTemplatePO.class)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis())
                .set(ActivityNodeTemplatePO.F_EXCEPTION_STATUS, nodeTemplate.getExceptionStatus())
                .set(ActivityNodeTemplatePO.F_EXCEPTION_COUNT, nodeTemplate.getExceptionCount())
                .set(ActivityNodeTemplatePO.F_NODE_EXCEPTION_INFO, nodeTemplate.getNodeExceptionInfo());

        mongoContext.update(query, updateOperations);
    }
}
