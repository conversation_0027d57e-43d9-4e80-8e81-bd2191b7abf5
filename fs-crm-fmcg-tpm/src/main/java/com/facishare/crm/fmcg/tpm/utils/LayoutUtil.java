package com.facishare.crm.fmcg.tpm.utils;

import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/16 下午4:36
 */
@Slf4j
@UtilityClass
public class LayoutUtil {

    public static void setLayoutFieldHiddenOrReadOnly(LayoutExt layout, Set<String> hideFields, Set<String> readonlyFields, Set<String> requiredFields, List<IFormField> additionFields, String additionFieldsGroupName) {
        try {
            FormComponentExt form = layout.getFormComponent().orElse(null);
            if (!Objects.isNull(form)) {
                List<IFieldSection> sections = form.getFieldSections();
                if (CollectionUtils.isNotEmpty(additionFields)) {
                    Set<String> existsFields = new HashSet<>();
                    sections.forEach(section -> section.getFields().forEach(field -> existsFields.add(field.getFieldName())));
                    additionFields = additionFields.stream().filter(field -> !existsFields.contains(field.getFieldName())).collect(Collectors.toList());
                    if (StringUtils.isEmpty(additionFieldsGroupName)) {
                        List<IFormField> newList = sections.get(0).getFields();
                        newList.addAll(additionFields);
                        sections.get(0).setFields(newList);
                    } else {
                        for (IFieldSection section : sections) {
                            if (Objects.equals(additionFieldsGroupName, section.getName())) {
                                List<IFormField> fields = section.getFields();
                                fields.addAll(additionFields);
                                section.setFields(fields);
                            }
                        }
                    }
                }
                for (IFieldSection section : sections) {
                    section.setFields(section.getFields().stream().filter(field -> !hideFields.contains(field.getFieldName())).collect(Collectors.toList()));
                    section.getFields().forEach(field -> {
                        if (readonlyFields.contains(field.getFieldName())) {
                            field.setReadOnly(true);
                        }
                        if (requiredFields.contains(field.getFieldName())) {
                            field.setRequired(true);
                        }
                    });
                }
            }
        } catch (Exception ex) {
            log.info("override layout cause unknown exception  : ", ex);
        }
    }

    public static void overrideDetailLayout(StandardDescribeLayoutController.Result result, Map<String, Set<String>> detailRemoveFieldsMap, Map<String, Set<String>> detailReadonlyFieldsMap) {
        List<DetailObjectListResult> detailObjectList = result.getDetailObjectList();
        for (DetailObjectListResult detailObjectListResult : detailObjectList) {
            String objectApiName = detailObjectListResult.getObjectApiName();
            Set<String> detailReadonlyFields = detailRemoveFieldsMap.get(objectApiName);
            Set<String> detailRemoveFields = detailReadonlyFieldsMap.get(objectApiName);
            for (RecordTypeLayoutStructure structure : detailObjectListResult.getLayoutList()) {
                Map detailLayout = structure.getDetail_layout();
                LayoutExt layoutExt = LayoutExt.of(detailLayout);

                Optional<FormComponentExt> component = layoutExt.getFormComponent();
                if (component.isPresent()) {
                    FormComponentExt formComponent = component.get();
                    for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                        if (CollectionUtils.isNotEmpty(detailRemoveFields)) {
                            fieldSection.setFields(fieldSection.getFields().stream().filter(iFormField -> !detailRemoveFields.contains(iFormField.getFieldName())).collect(Collectors.toList()));
                        }
                        for (IFormField field : fieldSection.getFields()) {
                            if (CollectionUtils.isNotEmpty(detailReadonlyFields) && detailReadonlyFields.contains(field.getFieldName())) {
                                field.setReadOnly(true);
                            }
                        }
                    }
                }
            }
        }
    }

}
