<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.crm.fmcg.tpm.dao.paas.BudgetAccountMapper">
    <update id="updateAccountAmount">
        update fmcg_tpm_budget_account
        <set>
            <if test="all.transfer_in_amount != null ">
                transfer_in_amount = #{all.transfer_in_amount},
            </if>
            <if test="all.transfer_out_amount != null ">
                transfer_out_amount = #{all.transfer_out_amount},
            </if>
            <if test="all.frozen_amount != null ">
                frozen_amount = #{all.frozen_amount},
            </if>
            <if test="all.total_amount != null ">
                total_amount = #{all.total_amount},
            </if>
            <if test="all.used_amount != null ">
                used_amount = #{all.used_amount},
            </if>
            <if test="all.available_amount != null ">
                available_amount = #{all.available_amount},
            </if>
            <if test="all.carried_out_amount != null ">
                carried_out_amount = #{all.carried_out_amount},
            </if>
            <if test="all.calculate_apply_amount != null ">
                calculate_apply_amount = #{all.calculate_apply_amount},
            </if>
            <if test="all.last_modified_time != null and all.last_modified_time != 0">
                last_modified_time = #{all.last_modified_time},
            </if>
        </set>
        where id = #{_id} and tenant_id = #{tenant_id}
    </update>


</mapper>