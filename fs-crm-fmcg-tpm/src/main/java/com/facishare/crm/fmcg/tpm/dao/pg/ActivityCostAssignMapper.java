package com.facishare.crm.fmcg.tpm.dao.pg;

import com.facishare.paas.metadata.ratelimit.DBLimit;
import com.facishare.paas.metadata.ratelimit.MethodType;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2021/6/7 12:40
 */
public interface ActivityCostAssignMapper extends ITenant<ActivityCostAssignMapper> {

    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    long countByActivity(@Param("tenant_id") String tenantId,
                         @Param("activity_id") String activityId);

    @Results({
            @Result(property = "status", column = "status"),
            @Result(property = "count", column = "count")
    })
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    long queryActivityTypeStatisticsData(
            @Param("tenant_id") String tenantId,
            @Param("activity_type_id") String activityTypeId
    );
}
