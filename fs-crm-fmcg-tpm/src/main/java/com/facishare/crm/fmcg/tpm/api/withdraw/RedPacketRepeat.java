package com.facishare.crm.fmcg.tpm.api.withdraw;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface RedPacketRepeat {


    @Data
    @ToString
    class Arg implements Serializable {

    }


    @Data
    @Builder
    @ToString
    class Result implements Serializable {

        @SerializedName("repeat_status")
        @JSONField(name = "repeat_status")
        @JsonProperty("repeat_status")
        private String authStatus;

    }
}
