package com.facishare.crm.fmcg.tpm.business.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson2.JSONWriter;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.ToString;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/4 下午3:36
 */

@ToString
public class BudgetConsumeSession {

    @Resource(name = "redisCmd")
    private static final MergeJedisCmd redisCmd = SpringUtil.getContext().getBean("redisCmd", MergeJedisCmd.class);

    private static final String SESSION_ID_TEMPLATE = "BUDGET:CONSUME:%s:%s";

    public static final String SESSION_BIZ_KEY = "SESSION_BIZ_KEY";

    public static final String SESSION_TRACE_KEY = "SESSION_TRACE_KEY";

    public static final String SESSION_ESCAPE_KEY = "SESSION_ESCAPE_KEY";

    public static final String SESSION_EDIT_INFECTIVE_KEY = "SESSION_EDIT_INFECTIVE_KEY";

    public static final String SESSION_OPERATE_MARK_KEY = "SESSION_OPERATE_MARK_KEY";

    /**
     * F
     * 0 before 1 doAct 2 after 3 finally
     */
    public static final String SESSION_FAIL_STAGE = "SESSION_FAIL_STAGE";

    public static final String SESSION_EXECUTE_STAGE = "SESSION_EXECUTE_STAGE";

    private static final String SESSION_OCCUPY_DETAIL_IDS = "SESSION_OCCUPY_DETAIL_IDS";

    public static final String SESSION_LOCK_PREFIX = "CONSUME:LOCK:PREFIX:%s";

    public static final String SESSION_BUDGET_AMOUNT_MAP = "SESSION_BUDGET_AMOUNT_MAP";

    public static final String SESSION_WITHHOLDING_LIST = "SESSION_WITHHOLDING_LIST";

    public static final String SESSION_IS_TRIGGER_BEFORE_APPROVAL = "SESSION_IS_TRIGGER_BEFORE_APPROVAL";

    public static final String SESSION_BUDGET_DETAIL_IDS = "SESSION_BUDGET_DETAIL_IDS";

    private String id;

    private IObjectData frozenMasterData;

    public BudgetConsumeSession(String id) {
        this.id = String.format(SESSION_ID_TEMPLATE, TraceContext.get().getEi(), id);
        if (!Boolean.TRUE.equals(redisCmd.exists(this.id))) {
            redisCmd.pipeline(pipelineCmd -> {
                pipelineCmd.hset(this.id, "start", "start");
                pipelineCmd.expire(this.id, 120);
            });
        }
    }

    public void setAttribute(String key, String value) {
        redisCmd.hset(id, key, value);
    }

    public String getAttribute(String key) {
        return redisCmd.hget(id, key);
    }

    public String getAttribute(String key, String defaultValue) {
        String value = redisCmd.hget(id, key);
        return value == null ? defaultValue : value;
    }

    public Map<String, String> all() {
        return redisCmd.hgetAll(id);
    }

    public void destroy() {
        redisCmd.del(id);
    }

    public void setBizKey(String bizKey) {
        setAttribute(SESSION_BIZ_KEY, bizKey);
    }

    public String getBizKey() {
        return getAttribute(SESSION_BIZ_KEY);
    }

    public void setTraceId(String traceId) {
        setAttribute(SESSION_TRACE_KEY, traceId);
    }

    public String getTraceId() {
        return getAttribute(SESSION_TRACE_KEY);
    }

    public void setIsTriggerBeforeApproval(Boolean isTriggerBeforeApproval) {
        setAttribute(SESSION_IS_TRIGGER_BEFORE_APPROVAL, String.valueOf(isTriggerBeforeApproval));
    }

    public Boolean getIsTriggerBeforeApproval() {
        return Boolean.TRUE.equals(Boolean.valueOf(getAttribute(SESSION_IS_TRIGGER_BEFORE_APPROVAL)));
    }


    public void setEditInfectiveFlag() {
        setAttribute(SESSION_EDIT_INFECTIVE_KEY, "true");
    }

    public boolean isEditInfective() {
        return Boolean.TRUE.equals(Boolean.valueOf(getAttribute(SESSION_EDIT_INFECTIVE_KEY)));
    }

    public void setOperateMark(String operateMark) {
        setAttribute(SESSION_OPERATE_MARK_KEY, operateMark);
    }

    public String getOperateMark() {
        return getAttribute(SESSION_OPERATE_MARK_KEY, "");
    }

    public void setExecuteStage(String executeStage) {
        setAttribute(SESSION_EXECUTE_STAGE, executeStage);
    }

    public String getExecuteStage() {
        return getAttribute(SESSION_EXECUTE_STAGE, "-1");
    }

    public void setSessionOccupyDetailIds(String ids) {
        setAttribute(SESSION_OCCUPY_DETAIL_IDS, ids);
    }

    public List<String> getSessionOccupyDetailIds() {
        String ids = getAttribute(SESSION_OCCUPY_DETAIL_IDS);
        return Strings.isNullOrEmpty(ids) ? Lists.newArrayList() : JSON.parseArray(ids, String.class);
    }

    public void putBudgetAmountMap(Map<String, BigDecimal> map) {
        setAttribute(SESSION_BUDGET_AMOUNT_MAP, JSON.toJSONString(map));
    }

    public String getBudgetAmountMap() {
        return getAttribute(SESSION_BUDGET_AMOUNT_MAP, "{}");
    }

    public void setWithholdingList(List<IObjectData> withholdingList) {
        setAttribute(SESSION_WITHHOLDING_LIST, JSON.toJSONString(ObjectDataExt.convertData2Map(withholdingList), SerializerFeature.DisableCircularReferenceDetect));
    }

    public List<IObjectData> getWithholdingList() {
        String list = getAttribute(SESSION_WITHHOLDING_LIST);
        return Strings.isNullOrEmpty(list) ? Lists.newArrayList() : JSON.parseArray(list, Map.class).stream().map(ObjectData::new).collect(Collectors.toList());
    }

    /**
     * 不在redis里
     *
     * @param frozenMasterData
     */
    public void setFrozenMasterData(IObjectData frozenMasterData) {
        this.frozenMasterData = frozenMasterData;
    }

    public IObjectData getFrozenMasterData() {
        return this.frozenMasterData;
    }

    public void setBudgetDetailIds(List<String> ids) {
        setAttribute(SESSION_BUDGET_DETAIL_IDS, JSON.toJSONString(ids));
    }

    public List<String> getBudgetDetailIds() {
        return JSON.parseArray(getAttribute(SESSION_BUDGET_DETAIL_IDS, "[]"), String.class);
    }


}
