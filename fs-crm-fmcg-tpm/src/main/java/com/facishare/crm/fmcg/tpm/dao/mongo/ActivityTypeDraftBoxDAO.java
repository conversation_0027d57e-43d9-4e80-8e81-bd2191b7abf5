package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import de.lab4inf.math.util.Strings;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.List;

/**
 * @author: wuyx
 * description:
 * createTime: 2022/5/20 11:37
 */
public class ActivityTypeDraftBoxDAO extends UniqueIdBaseDAO<ActivityTypeDraftBoxPO> {
    protected ActivityTypeDraftBoxDAO(Class<ActivityTypeDraftBoxPO> clazz) {
        super(clazz);
    }

    public List<ActivityTypeDraftBoxPO> all(String tenantId, boolean onlyNormal, boolean includeDeleted) {
        Query<ActivityTypeDraftBoxPO> query = mongoContext.createQuery(ActivityTypeDraftBoxPO.class).field(MongoPO.F_TENANT_ID).equal(tenantId);
        if (onlyNormal) {
            query.field(ActivityTypeDraftBoxPO.F_STATUS).equal(StatusType.NORMAL.value());
        }
        if (!includeDeleted) {
            query.field(MongoPO.F_IS_DELETED).equal(false);
        }
        return query.asList();
    }

    public boolean isDuplicateName(String tenantId, String name) {
        Query<ActivityTypeDraftBoxPO> query = mongoContext.createQuery(ActivityTypeDraftBoxPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityTypeDraftBoxPO.F_NAME).equal(name);
        return query.countAll() > 0;
    }

    public boolean isDuplicateName(String tenantId, String uniqueId, String name) {
        Query<ActivityTypeDraftBoxPO> query = mongoContext.createQuery(ActivityTypeDraftBoxPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).notEqual(uniqueId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityTypeDraftBoxPO.F_NAME).equal(name);
        return query.countAll() > 0;
    }

    public boolean isDuplicateApiName(String tenantId, String name) {
        Query<ActivityTypeDraftBoxPO> query = mongoContext.createQuery(ActivityTypeDraftBoxPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityTypeDraftBoxPO.F_API_NAME).equal(name);
        return query.countAll() > 0;
    }

    public List<ActivityTypeDraftBoxPO> list(String tenantId, String keyword, int limit, int offset) {
        Query<ActivityTypeDraftBoxPO> query = mongoContext.createQuery(ActivityTypeDraftBoxPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .order("-" + MongoPO.F_LAST_UPDATE_TIME)
                .offset(offset);
        if (limit != -1) {
            query.limit(limit);
        }
        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(ActivityTypeDraftBoxPO.F_NAME).containsIgnoreCase(keyword);
        }
        return query.asList();
    }


    public long count(String tenantId, String keyword) {
        Query<ActivityTypeDraftBoxPO> query = mongoContext.createQuery(ActivityTypeDraftBoxPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false);

        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(ActivityTypeDraftBoxPO.F_NAME).containsIgnoreCase(keyword);
        }

        return query.countAll();
    }

    public void edit(String tenantId,
                     String uniqueId,
                     int operator,
                     String name,
                     List<Integer> employeeIds,
                     List<Integer> departmentIds,
                     List<String> roleIds,
                     String scopeDescription,
                     String description,
                     String status,
                     String exceptionStatus,
                     List<ActivityNodeEntity> nodes) {
        Query<ActivityTypeDraftBoxPO> query = mongoContext.createQuery(ActivityTypeDraftBoxPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        UpdateOperations<ActivityTypeDraftBoxPO> updateOperations = mongoContext.createUpdateOperations(ActivityTypeDraftBoxPO.class)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis())
                .set(MongoPO.F_LAST_UPDATER, operator)
                .set(ActivityTypeDraftBoxPO.F_NAME, name)
                .set(ActivityTypeDraftBoxPO.F_EMPLOYEE_IDS, employeeIds)
                .set(ActivityTypeDraftBoxPO.F_DEPARTMENT_IDS, departmentIds)
                .set(ActivityTypeDraftBoxPO.F_ROLE_IDS, roleIds)
                .set(ActivityTypeDraftBoxPO.F_SCOPE_DESCRIPTION, scopeDescription)
                .set(ActivityTypeDraftBoxPO.F_DESCRIPTION, description)
                .set(ActivityTypeDraftBoxPO.F_STATUS, status)
                .set(ActivityTypeDraftBoxPO.F_EXCEPTION_STATUS, exceptionStatus)
                .inc(ActivityTypeDraftBoxPO.F_VERSION)
                .set(ActivityTypeDraftBoxPO.F_ACTIVITY_NODES, nodes);

        mongoContext.update(query, updateOperations);
    }

    public void setStatus(String tenantId, int operator, String uniqueId, String status) {
        Query<ActivityTypeDraftBoxPO> query = mongoContext.createQuery(ActivityTypeDraftBoxPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        UpdateOperations<ActivityTypeDraftBoxPO> updateOperations = mongoContext.createUpdateOperations(ActivityTypeDraftBoxPO.class)
                .set(ActivityTypeDraftBoxPO.F_STATUS, status)
                .set(MongoPO.F_LAST_UPDATER, operator)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis());

        mongoContext.update(query, updateOperations);
    }

}
