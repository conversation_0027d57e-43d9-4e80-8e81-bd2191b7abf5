package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.ICarryForwardActionService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;
import org.slf4j.MDC;

import java.io.Serializable;
import java.util.List;

public class TPMBudgetCarryForwardObjBudgetCarryForwardRetryAction extends PreDefineAction<TPMBudgetCarryForwardObjBudgetCarryForwardRetryAction.Arg, TPMBudgetCarryForwardObjBudgetCarryForwardRetryAction.Result> {
    private final ICarryForwardActionService carryForwardActionService = SpringUtil.getContext().getBean(ICarryForwardActionService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.BUDGET_CARRY_FORWARD_RETRY.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(TPMBudgetCarryForwardObjBudgetCarryForwardRetryAction.Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected TPMBudgetCarryForwardObjBudgetCarryForwardRetryAction.Result doAct(TPMBudgetCarryForwardObjBudgetCarryForwardRetryAction.Arg arg) {
        IObjectData master = serviceFacade.findObjectDataIgnoreAll(actionContext.getUser(), arg.getObjectDataId(), ApiNames.TPM_BUDGET_CARRY_FORWARD);
        String status = master.get(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, String.class);

        MDC.put("is_retry", "true");

        if (TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FAILED.equals(status)) {
            ParallelUtils.createParallelTask().submit(() -> carryForwardActionService.carryForward(actionContext.getUser(), arg.getObjectDataId())).run();
        } else if (TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FROZEN_FAILED.equals(status)) {
            ParallelUtils.createParallelTask().submit(() -> carryForwardActionService.freeze(actionContext.getUser(), arg.getObjectDataId())).run();
        } else if (TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__UNFROZEN_FAILED.equals(status)) {
            ParallelUtils.createParallelTask().submit(() -> carryForwardActionService.unfreeze(actionContext.getUser(), arg.getObjectDataId())).run();
        } else {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_BUDGET_CARRY_FORWARD_OBJ_BUDGET_CARRY_FORWARD_RETRY_ACTION_0));
        }

        return new TPMBudgetCarryForwardObjBudgetCarryForwardRetryAction.Result();
    }

    @Data
    @ToString
    public static class Arg implements Serializable {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String objectDataId;
    }

    @Data
    @ToString
    public static class Result implements Serializable {
    }
}
