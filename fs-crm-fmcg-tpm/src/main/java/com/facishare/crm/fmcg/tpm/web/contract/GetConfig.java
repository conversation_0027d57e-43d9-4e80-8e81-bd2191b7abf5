package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * author: wuyx
 * description:
 * createTime: 2023/3/20 15:44
 */
public interface GetConfig {

    @Data
    @ToString
    @Builder
    class Arg implements Serializable {
        private String key;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "config")
        @JsonProperty(value = "config")
        @SerializedName("config")
        private ConfigVO config;
    }
}