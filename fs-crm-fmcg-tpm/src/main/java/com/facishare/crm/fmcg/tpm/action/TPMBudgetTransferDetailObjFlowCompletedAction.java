package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetTransferDetailFields;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.common.constant.BudgetTransferDetailConstants;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

@Slf4j
@SuppressWarnings("Duplicates,unused")
public class TPMBudgetTransferDetailObjFlowCompletedAction extends StandardFlowCompletedAction {

    protected static final Map<String, BizType> RECORD_TYPE_2_OUT_BIZ_TYPE_MAP = Maps.newHashMap();
    protected static final Map<String, BizType> RECORD_TYPE_2_IN_BIZ_TYPE_MAP = Maps.newHashMap();

    static {
        RECORD_TYPE_2_OUT_BIZ_TYPE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT, BizType.TRANSFER_OUT);
        RECORD_TYPE_2_OUT_BIZ_TYPE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT, BizType.TRANSFER_DEDUCTION);

        RECORD_TYPE_2_IN_BIZ_TYPE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT, BizType.TRANSFER_IN);
        RECORD_TYPE_2_IN_BIZ_TYPE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD, BizType.TRANSFER_APPEND);
    }

    private final ITransactionProxy transProxy = SpringUtil.getContext().getBean(ITransactionProxy.class);
    private final IBudgetTypeManager budgetTypeManager = SpringUtil.getContext().getBean(IBudgetTypeManager.class);

    private IObjectData transferDetailData;
    private boolean invoked;

    private IBudgetOperator transferOutOperator;
    private IBudgetOperator transferInOperator;

    private String approvalTraceId;
    private String businessTraceId;

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        this.approvalTraceId = TraceUtil.getApprovalCallbackTraceId(this.arg.getCallbackData());
        if (Strings.isNullOrEmpty(approvalTraceId)) {
            return;
        }
        this.businessTraceId = TraceUtil.getBusinessCallbackTraceId(this.arg.getCallbackData());

        this.transferDetailData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_BUDGET_TRANSFER_DETAIL);

        String currentStatus = this.transferDetailData.get(TPMBudgetTransferDetailFields.OPERATION_STATUS, String.class);

        this.invoked = TPMBudgetTransferDetailFields.OPERATION_STATUS_SUCCESS.equals(currentStatus);

        if (!this.invoked) {
            if (!TPMBudgetTransferDetailFields.OPERATION_STATUS_FROZEN.equals(currentStatus)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_FLOW_COMPLETED_ACTION_0));
            }

            switch (this.transferDetailData.getRecordType()) {
                case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD:
                    this.prepareAndValidateTransferInData();
                    break;
                case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT:
                    this.prepareAndValidateTransferOutData();
                    break;
                default:
                case TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT:
                    this.prepareAndValidateTransferOutData();
                    this.prepareAndValidateTransferInData();
                    break;
            }
        }
    }

    private void prepareAndValidateTransferOutData() {
        String accountId = (String) this.transferDetailData.get(TPMBudgetTransferDetailFields.TRANSFER_OUT_BUDGET_ACCOUNT_ID);
        this.transferOutOperator = BudgetOperatorFactory.initOperator(RECORD_TYPE_2_OUT_BIZ_TYPE_MAP.get(this.transferDetailData.getRecordType()), actionContext.getUser(), accountId, this.businessTraceId, this.approvalTraceId, this.transferDetailData);
        if (!this.transferOutOperator.tryLock()) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_BUDGET_TRANSFER_DETAIL_OBJ_FLOW_COMPLETED_ACTION_0));
        }
    }

    private void prepareAndValidateTransferInData() {
        String accountId = (String) this.transferDetailData.get(TPMBudgetTransferDetailFields.TRANSFER_IN_BUDGET_ACCOUNT_ID);
        this.transferInOperator = BudgetOperatorFactory.initOperator(RECORD_TYPE_2_IN_BIZ_TYPE_MAP.get(this.transferDetailData.getRecordType()), actionContext.getUser(), accountId, this.businessTraceId, this.approvalTraceId, this.transferDetailData);
        if (!this.transferInOperator.tryLock()) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_BUDGET_TRANSFER_DETAIL_OBJ_FLOW_COMPLETED_ACTION_1));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        return transProxy.call(() -> {
            Result inner = super.doAct(arg);
            this.stopWatch.lap("framework.doAct");

            if (!this.invoked && Boolean.TRUE.equals(inner.getSuccess())) {
                if (arg.isPass()) {
                    try {
                        this.doTransfer();
                        this.stopWatch.lap("doTransfer");
                    } catch (Exception ex) {
                        log.error("unfreeze and do carry forward failed : ", ex);
                        this.setTransferFailed();
                        this.stopWatch.lap("setTransferFailed");
                    }
                } else {
                    this.unfreeze();
                    this.stopWatch.lap("unfreeze");
                }
            }
            return inner;
        });
    }

    private void setTransferFailed() {
        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_FAILED);
        this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), this.transferDetailData, updater);
    }

    private void doTransfer() {
        switch (this.transferDetailData.getRecordType()) {
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD:
                doAddAction();
                break;
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT:
                doDeductAction();
                break;
            default:
            case TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT:
                doTransferAction();
                break;
        }
    }

    private void doAddAction() {
        BigDecimal amount = this.transferDetailData.get(TPMBudgetTransferDetailFields.AMOUNT, BigDecimal.class);
        this.transferInOperator.income(amount);
        this.transferInOperator.recalculate();

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_SUCCESS);
        this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), this.transferDetailData, updater);
    }

    private void doDeductAction() {
        BigDecimal amount = this.transferDetailData.get(TPMBudgetTransferDetailFields.AMOUNT, BigDecimal.class);

        this.transferOutOperator.unfreeze(BizType.RELEASE);
        this.transferOutOperator.expenditure(amount);
        this.transferOutOperator.recalculate();

        String typeId = this.transferOutOperator.getAccount().get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        String nodeId = this.transferOutOperator.getAccount().get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity type = budgetTypeManager.getNode(actionContext.getTenantId(), typeId, nodeId);
        if (type.getAutomaticRecoveryAfterDeduct()) {
            String parentId = this.transferOutOperator.getAccount().get(TPMBudgetAccountFields.PARENT_ID, String.class);
            IBudgetOperator parentOperator = BudgetOperatorFactory.initOperator(BizType.TRANSFER_DEDUCTION_RECOVERY, actionContext.getUser(), parentId, this.businessTraceId, this.approvalTraceId, this.transferDetailData);
            parentOperator.income(amount);
            parentOperator.recalculate();
        }

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_SUCCESS);
        this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), this.transferDetailData, updater);
    }

    private void doTransferAction() {
        BigDecimal amount = this.transferDetailData.get(TPMBudgetTransferDetailFields.AMOUNT, BigDecimal.class);

        this.transferOutOperator.unfreeze(BizType.RELEASE);
        this.transferOutOperator.expenditure(amount);
        this.transferInOperator.income(amount);

        this.transferInOperator.recalculate();
        this.transferOutOperator.recalculate();

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_SUCCESS);
        this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), this.transferDetailData, updater);
    }

    private void unfreeze() {
        if (BudgetTransferDetailConstants.NEED_OCCUPY_RECORD_TYPE_SET.contains(this.transferDetailData.getRecordType())) {
            this.transferOutOperator.unfreeze(BizType.APPROVAL_BACK);
            this.transferOutOperator.recalculate();

            Map<String, Object> updater = Maps.newHashMap();
            updater.put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_INEFFECTIVE);
            this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), this.transferDetailData, updater);
        }
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
            this.stopWatch.lap("flow.finallyDo");
        } finally {
            this.unlockAccount();
            this.stopWatch.lap("unlockAccount");
        }

    }

    private void unlockAccount() {
        if (Objects.nonNull(this.transferOutOperator)) {
            log.info("release transferOutOperator lock,accountId:{}", this.transferOutOperator.getAccount().getId());
            this.transferOutOperator.unlock();
        }
        if (Objects.nonNull(this.transferInOperator)) {
            log.info("release transferInOperator lock,accountId:{}", this.transferInOperator.getAccount().getId());
            this.transferInOperator.unlock();
        }
    }
}
