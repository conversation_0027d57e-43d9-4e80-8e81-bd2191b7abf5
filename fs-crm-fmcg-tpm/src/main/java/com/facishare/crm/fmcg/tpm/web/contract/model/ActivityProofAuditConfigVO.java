package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 17:59
 */
@Data
@ToString
public class ActivityProofAuditConfigVO implements Serializable {

    @JSONField(name = "audit_source_config")
    @JsonProperty(value = "audit_source_config")
    @SerializedName("audit_source_config")
    private ActivityProofAuditSourceConfigVO auditSourceConfig;

    @JSONField(name = "audit_mode_config")
    @JsonProperty(value = "audit_mode_config")
    @SerializedName("audit_mode_config")
    private ActivityProofAuditModeConfigVO auditModeConfig;

    @JSONField(name = "multiple_level_audit_config")
    @JsonProperty(value = "multiple_level_audit_config")
    @SerializedName("multiple_level_audit_config")
    private List<ActivityProofMultipleLevelAuditConfigVO> multipleLevelAuditConfig;

    public static ActivityProofAuditConfigVO fromPO(ActivityProofAuditConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityProofAuditConfigVO vo = new ActivityProofAuditConfigVO();
        vo.setAuditModeConfig(ActivityProofAuditModeConfigVO.fromPO(po.getAuditModeConfig()));
        vo.setAuditSourceConfig(ActivityProofAuditSourceConfigVO.fromPO(po.getAuditSourceConfig()));
        if (CollectionUtils.isNotEmpty(po.getMultipleLevelAuditConfig())) {
            vo.setMultipleLevelAuditConfig(po.getMultipleLevelAuditConfig().stream().map(ActivityProofMultipleLevelAuditConfigVO::fromPO).collect(Collectors.toList()));
        } else {
            vo.setMultipleLevelAuditConfig(Lists.newArrayList());
        }
        return vo;
    }
}