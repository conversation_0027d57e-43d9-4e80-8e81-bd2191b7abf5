package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/27 下午5:47
 */
public interface GetConsumeObject {

    @Data
    @ToString
    class Arg implements Serializable {

    }


    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "consume_objects")
        @JsonProperty(value = "consume_objects")
        @SerializedName("consume_objects")
        private Map<String, String> consumeObjects;

        @JSONField(name = "deduct_objects")
        @JsonProperty(value = "deduct_objects")
        @SerializedName("deduct_objects")
        private Map<String, String> deductObjects;
    }
}
