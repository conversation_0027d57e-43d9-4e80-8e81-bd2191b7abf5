package com.facishare.crm.fmcg.tpm.reward.service;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.reward.dto.AdvancedStoreRewardLimitConfig;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class AdvancedRewardLimitService {

    @Resource
    private ServiceFacade serviceFacade;

    public boolean overlimit(String tenantId, String activityId, String channelId, String storeId, String skuId) {
        if (!TPMGrayUtils.isAllowAdvancedRewardLimit(tenantId)) {
            return false;
        }

        log.info("load config");
        AdvancedStoreRewardLimitConfig config = loadRewardLimitConfig(tenantId, activityId, channelId, storeId, skuId);

        log.info("config loaded");
        if (config.getLimit() > -1 && calculateStoreRewardCount(tenantId, activityId, storeId) >= config.getLimit()) {
            return true;
        }

        if (config.getMonthlyLimit() > -1 && calculateStoreMonthlyCount(tenantId, activityId, storeId) >= config.getMonthlyLimit()) {
            return true;
        }

        if (config.getSkuLimit() > -1 && calculateStoreSkuRewardCount(tenantId, activityId, storeId, skuId) >= config.getSkuLimit()) {
            return true;
        }

        return config.getMonthlySkuLimit() > -1 && calculateMonthlyStoreSkuRewardCount(tenantId, activityId, storeId, skuId) >= config.getMonthlySkuLimit();
    }

    private int calculateMonthlyStoreSkuRewardCount(String tenantId, String activityId, String storeId, String skuId) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long begin = cal.getTimeInMillis();

        cal.add(Calendar.MONTH, 1);
        long end = cal.getTimeInMillis();
        int count = countActivityRewardByFilter(tenantId, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.ACCOUNT_ID__C, Operator.EQ, Lists.newArrayList(storeId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.ACTIVITY_ID, Operator.EQ, Lists.newArrayList(activityId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.PRODUCT_ID, Operator.EQ, Lists.newArrayList(skuId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_TIME, Operator.BETWEEN,
                        Lists.newArrayList(
                                String.valueOf(begin),
                                String.valueOf(end)
                        ))
        ));
        log.info("calculateMonthlyStoreSkuRewardCount:{}", count);
        return count;
    }

    private int calculateStoreSkuRewardCount(String tenantId, String activityId, String storeId, String skuId) {
        int count = countActivityRewardByFilter(tenantId, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.ACCOUNT_ID__C, Operator.EQ, Lists.newArrayList(storeId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.ACTIVITY_ID, Operator.EQ, Lists.newArrayList(activityId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.PRODUCT_ID, Operator.EQ, Lists.newArrayList(skuId))
        ));
        log.info("calculateStoreSkuRewardCount:{}", count);
        return count;
    }

    private int calculateStoreMonthlyCount(String tenantId, String activityId, String storeId) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long begin = cal.getTimeInMillis();

        cal.add(Calendar.MONTH, 1);
        long end = cal.getTimeInMillis();

        int count = countActivityRewardByFilter(tenantId, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.ACCOUNT_ID__C, Operator.EQ, Lists.newArrayList(storeId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.ACTIVITY_ID, Operator.EQ, Lists.newArrayList(activityId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_TIME, Operator.BETWEEN, Lists.newArrayList(String.valueOf(begin), String.valueOf(end)))));
        log.info("calculateStoreMonthlyCount:{}", count);
        return count;
    }

    private int calculateStoreRewardCount(String tenantId, String activityId, String storeId) {
        return countActivityRewardByFilter(tenantId, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.ACCOUNT_ID__C, Operator.EQ, Lists.newArrayList(storeId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.ACTIVITY_ID, Operator.EQ, Lists.newArrayList(activityId))
        ));
    }

    private int countActivityRewardByFilter(String tenantId, List<IFilter> filters) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 2000, filters);
        List<IObjectData> data = serviceFacade.aggregateFindBySearchQuery(
                User.systemUser(tenantId),
                query,
                ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ,
                TPMActivityRewardDetailFields.ACCOUNT_ID__C,
                Lists.newArrayList(AggFunctionArg.builder().aggField(TPMActivityRewardDetailFields.SERIAL_NUMBER_ID).isDistinct(true).aggFunction("count").build())
        );

        log.info("count activity reward result : {}", data);

        return CollectionUtils.isNotEmpty(data) ? data.get(0).get("count_serial_number_id", Integer.class, 0) : 0;
    }

    private AdvancedStoreRewardLimitConfig loadRewardLimitConfig(String tenantId, String activityId, String channelId, String storeId, String skuId) {
        IObjectData storeConfig = loadRewardLimitConfigByStore(tenantId, activityId, storeId);
        if (Objects.nonNull(storeConfig)) {
            return convertToRewardLimitConfig(tenantId, storeConfig, skuId);
        }

        IObjectData channelConfig = loadRewardLimitConfigByChannel(tenantId, activityId, channelId);
        if (Objects.nonNull(channelConfig)) {
            return convertToRewardLimitConfig(tenantId, channelConfig, skuId);
        }

        IObjectData activityConfig = loadRewardLimitConfigByActivity(tenantId, activityId);
        if (Objects.nonNull(activityConfig)) {
            return convertToRewardLimitConfig(tenantId, activityConfig, skuId);
        }
        return convertToRewardLimitConfig();
    }

    private IObjectData loadRewardLimitConfigByActivity(String tenantId, String activityId) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(StoreRewardLimitFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(StoreRewardLimitFields.STORE_ID);
        storeIdFilter.setOperator(Operator.IS);
        storeIdFilter.setFieldValues(Lists.newArrayList());

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(Lists.newArrayList(activityIdFilter, storeIdFilter), Lists.newArrayList(order));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.STORE_REWARD_LIMIT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        StoreRewardLimitFields.ACTIVITY_ID,
                        StoreRewardLimitFields.STORE_ID,
                        StoreRewardLimitFields.TOTAL_LIMIT,
                        StoreRewardLimitFields.MONTHLY_TOTAL_LIMIT,
                        StoreRewardLimitFields.CHANNEL_ID
                )
        ).stream().findFirst().orElse(null);
    }

    private IObjectData loadRewardLimitConfigByStore(String tenantId, String activityId, String storeId) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(StoreRewardLimitFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(StoreRewardLimitFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(Lists.newArrayList(activityIdFilter, storeIdFilter), Lists.newArrayList(order));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.STORE_REWARD_LIMIT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        StoreRewardLimitFields.ACTIVITY_ID,
                        StoreRewardLimitFields.STORE_ID,
                        StoreRewardLimitFields.TOTAL_LIMIT,
                        StoreRewardLimitFields.MONTHLY_TOTAL_LIMIT,
                        StoreRewardLimitFields.CHANNEL_ID
                )
        ).stream().findFirst().orElse(null);
    }

    private IObjectData loadRewardLimitConfigByChannel(String tenantId, String activityId, String channelId) {
        if (Strings.isNullOrEmpty(channelId)) {
            return null;
        }

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(StoreRewardLimitFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(StoreRewardLimitFields.CHANNEL_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(channelId));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(Lists.newArrayList(activityIdFilter, storeIdFilter), Lists.newArrayList(order));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.STORE_REWARD_LIMIT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        StoreRewardLimitFields.ACTIVITY_ID,
                        StoreRewardLimitFields.STORE_ID,
                        StoreRewardLimitFields.TOTAL_LIMIT,
                        StoreRewardLimitFields.MONTHLY_TOTAL_LIMIT,
                        StoreRewardLimitFields.CHANNEL_ID
                )
        ).stream().findFirst().orElse(null);
    }

    private AdvancedStoreRewardLimitConfig convertToRewardLimitConfig(String tenantId, IObjectData activityConfig, String skuId) {
        AdvancedStoreRewardLimitConfig config = new AdvancedStoreRewardLimitConfig();
        Integer limit = activityConfig.get(StoreRewardLimitFields.TOTAL_LIMIT, Integer.class);
        Integer monthlyLimit = activityConfig.get(StoreRewardLimitFields.MONTHLY_TOTAL_LIMIT, Integer.class);
        config.setLimit(Objects.isNull(limit) ? -1 : limit);
        config.setMonthlyLimit(Objects.isNull(monthlyLimit) ? -1 : monthlyLimit);

        IObjectData skuLimitData = loadSkuLimit(tenantId, activityConfig.getId(), skuId);
        if (Objects.nonNull(skuLimitData)) {
            Integer skuLimit = skuLimitData.get(StoreSkuRewardLimitFields.LIMIT, Integer.class);
            Integer monthlySkuLimit = skuLimitData.get(StoreSkuRewardLimitFields.MONTHLY_LIMIT, Integer.class);

            config.setSkuLimit(Objects.isNull(skuLimit) ? -1 : skuLimit);
            config.setMonthlySkuLimit(Objects.isNull(monthlySkuLimit) ? -1 : monthlySkuLimit);
        } else {
            config.setSkuLimit(-1);
            config.setMonthlySkuLimit(-1);
        }
        return config;
    }

    private IObjectData loadSkuLimit(String tenantId, String masterConfigId, String skuId) {
        IFilter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(StoreSkuRewardLimitFields.STORE_REWARD_LIMIT_ID);
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(Lists.newArrayList(masterConfigId));

        IFilter skuIdFilter = new Filter();
        skuIdFilter.setFieldName(StoreSkuRewardLimitFields.SKU_ID);
        skuIdFilter.setOperator(Operator.EQ);
        skuIdFilter.setFieldValues(Lists.newArrayList(skuId));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(Lists.newArrayList(masterIdFilter, skuIdFilter), Lists.newArrayList(order));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.STORE_SKU_REWARD_LIMIT,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        StoreSkuRewardLimitFields.SKU_ID,
                        StoreSkuRewardLimitFields.LIMIT,
                        StoreSkuRewardLimitFields.MONTHLY_LIMIT,
                        StoreSkuRewardLimitFields.STORE_REWARD_LIMIT_ID
                )
        ).stream().findFirst().orElse(null);
    }

    private AdvancedStoreRewardLimitConfig convertToRewardLimitConfig() {
        AdvancedStoreRewardLimitConfig config = new AdvancedStoreRewardLimitConfig();
        config.setLimit(-1);
        config.setMonthlyLimit(-1);
        config.setSkuLimit(-1);
        config.setMonthlySkuLimit(-1);
        return config;
    }
}