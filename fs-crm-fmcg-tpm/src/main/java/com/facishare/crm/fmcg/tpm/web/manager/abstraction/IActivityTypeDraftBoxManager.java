package com.facishare.crm.fmcg.tpm.web.manager.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

/**
 * author: wuyx
 * description:
 * createTime: 2022/5/20 11:44
 */
public interface IActivityTypeDraftBoxManager {

    void basicInformationValidation(String tenantId, IActivityType activityType);

    void duplicateNameValidation(String tenantId, String name);

    void duplicateApiNameValidation(String tenantId, String name);

    void fillObjectDisplayNameAndRecordTypeDisplayName(String tenantId, ActivityTypeVO vo);

    void fillNodeTemplateDescription(String tenantId, ActivityTypeVO vo);

}
