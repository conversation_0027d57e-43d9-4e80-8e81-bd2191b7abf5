package com.facishare.crm.fmcg.tpm.service;

import com.facishare.open.app.center.api.service.QueryAppAdminService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wuyx
 * @description:
 * @createTime: 2022/1/10 15:14
 */
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class AppAdminService {
    @Resource
    private QueryAppAdminService queryAppAdminService;

    public List<Integer> getAppAdminList(String tenantAccount) {
        IChangeableConfig config = ConfigFactory.getConfig("fs-appserver-openproxy-appinfo");
        List<Integer> adminIds = new ArrayList<>();
        String appId = config.get("tpm");
        BaseResult<List<FsUserVO>> admins = queryAppAdminService.findAppAdminListByAppId(tenantAccount, appId);
        if (admins == null || !admins.isSuccess()) {
            log.info("getAppAdminList is err. ea:{}", tenantAccount);
            return Lists.newArrayList();
        }
        admins.getResult().forEach(v -> adminIds.add(v.getUserId()));
        return adminIds;
    }
}
