package com.facishare.crm.fmcg.tpm.business;


import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardFields;
import com.facishare.crm.fmcg.tpm.session.SendMessageService;
import com.facishare.crm.fmcg.tpm.utils.SendMessageArgFormatUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.model.TextCardMessageBody;
import com.fxiaoke.model.TextCardMessageHead;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import groovy.lang.Tuple2;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2023/3/24 18:18
 */
//IgnoreI18nFile
@Component
@SuppressWarnings("Duplicates")
public class CarryForwardMasterDataService {
    private static final Map<String, String> STATUS_TRANSLATE_MAP = Maps.newHashMap();
    private static final Set<String> SUCCESS_STATUS = Sets.newHashSet();
    private static final Set<String> FAIL_STATUS = Sets.newHashSet();
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private CarryForwardDetailDataService carryForwardDetailDataService;
    @Resource
    private SendMessageService sendMessageService;

    static {
        initStatusTranslateValue();
        setSuccessStatus();
        setFailStatus();
    }

    public void finished(User user, IObjectData data, String status, String message) {
        String tenantId = user.getTenantId();
        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, status);
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_TIME, System.currentTimeMillis());

        doMark(tenantId, data, updater);
        doLog(tenantId, data, message);
        doSend(user, data, status, message);
    }

    public void finished(User user, IObjectData data, String status, String message, BigDecimal amount) {
        String tenantId = user.getTenantId();
        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, status);
        updater.put(TPMBudgetCarryForwardFields.AMOUNT, amount);
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_TIME, System.currentTimeMillis());

        doMark(tenantId, data, updater);
        doLog(tenantId, data, message);
        doSend(user, data, status, message);
    }

    public void failed(User user, IObjectData data, String status, String message) {
        String tenantId = user.getTenantId();
        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS, status);

        doMark(tenantId, data, updater);
        doLog(tenantId, data, String.format("结转操作失败，失败信息：%s", message));
        doSend(user, data, status, message);
    }

    private void doLog(String tenantId, IObjectData data, String message) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_CARRY_FORWARD);
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, data, message);
    }

    private void doMark(String tenantId, IObjectData data, Map<String, Object> updater) {
        serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);
    }

    private void doSend(User user, IObjectData data, String status, String message) {
        TextCardMessageHead head = SendMessageArgFormatUtil.buildMessageHead("预算结转异步执行通知");
        TextCardMessageBody body = buildTextCardMessageBody(user, data, status, message);
        SendTextCardMessageArg arg = SendMessageArgFormatUtil.buildTextCardArgForAppNotify(user, ApiNames.TPM_BUDGET_CARRY_FORWARD, data.getId(), head, body);
        sendMessageService.sendTextCardMessage(arg);
    }

    private TextCardMessageBody buildTextCardMessageBody(User user, IObjectData data, String status, String message) {
        String name = data.get(CommonFields.NAME, String.class);
        Tuple2<Integer, Integer> successAndFailCount = calculateSuccessAndFailCount(user, data.getId());
        Map<String, String> formMessage = Maps.newHashMap();
        formMessage.put("执行完成时间", SendMessageArgFormatUtil.formatDateStr(System.currentTimeMillis()));
        formMessage.put("执行状态", STATUS_TRANSLATE_MAP.get(status));
        String contentHeadMessage = String.format("预算结转单：[%s]执行完成。详细信息请点击查看详情。", name);
        if (SUCCESS_STATUS.contains(status)) {
            contentHeadMessage = String.format("预算结转单：[%s]执行完成。结转明细成功%s条，失败%s条。详细信息请点击查看详情。", name, successAndFailCount.getFirst(), successAndFailCount.getSecond());
        } else if (FAIL_STATUS.contains(status)) {
            contentHeadMessage = String.format("预算结转单：[%s]执行完成。结转明细成功%s条，失败%s条(%s)。详细信息请点击查看详情。",
                    name, successAndFailCount.getFirst(), successAndFailCount.getSecond(), message);
        }

        return SendMessageArgFormatUtil.buildMessageBody(contentHeadMessage, formMessage);
    }

    private Tuple2<Integer, Integer> calculateSuccessAndFailCount(User user, String dataId) {
        int successCount = 0;
        int failCount = 0;
        List<IObjectData> details = carryForwardDetailDataService.loadDetails(user.getTenantId(), dataId, Lists.newArrayList(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS));
        for (IObjectData detail : details) {
            String detailCarryForwardStatus = detail.get(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS, String.class);
            if (SUCCESS_STATUS.contains(detailCarryForwardStatus)) {
                successCount += 1;
            } else if (FAIL_STATUS.contains(detailCarryForwardStatus)) {
                failCount += 1;
            }
        }
        return new Tuple2<>(successCount, failCount);
    }

    private static void initStatusTranslateValue() {
        STATUS_TRANSLATE_MAP.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FROZEN_FAILED, "冻结失败");
        STATUS_TRANSLATE_MAP.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__SCHEDULE, "待结转");
        STATUS_TRANSLATE_MAP.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FROZEN, "冻结成功");
        STATUS_TRANSLATE_MAP.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__UNFROZEN, "已解冻");
        STATUS_TRANSLATE_MAP.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__UNFROZEN_FAILED, "解冻失败");
        STATUS_TRANSLATE_MAP.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FAILED, "结转失败");
        STATUS_TRANSLATE_MAP.put(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__SUCCESS, "结转成功");

    }

    private static void setSuccessStatus() {
        SUCCESS_STATUS.add(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__FROZEN);
        SUCCESS_STATUS.add(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__UNFROZEN);
        SUCCESS_STATUS.add(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__SUCCESS);
    }

    private static void setFailStatus() {
        FAIL_STATUS.add(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__FROZEN_FAILED);
        FAIL_STATUS.add(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__UNFROZEN_FAILED);
        FAIL_STATUS.add(TPMBudgetCarryForwardDetailFields.CARRY_FORWARD_STATUS__FAILED);
    }
}