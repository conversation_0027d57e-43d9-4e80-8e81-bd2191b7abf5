package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/23/20 8:42 PM
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityAgreementObjDescribeLayoutController extends StandardDescribeLayoutController {
    private static final Set<String> READ_ONLY_FIELD_API_NAME = Sets.newHashSet();
    private static final Set<String> REQUIRED_FIELD_API_NAME = Sets.newHashSet();

    static {

        REQUIRED_FIELD_API_NAME.add("product_id");
        READ_ONLY_FIELD_API_NAME.add("activity_product_id");
        READ_ONLY_FIELD_API_NAME.add("agreement_price");
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if ("add".equals(arg.getLayout_type()) && Boolean.TRUE.equals(arg.getInclude_layout())) {
            ILayout layout = result.getLayout().toLayout();
            try {
                List<IComponent> components = layout.getComponents();
                for (IComponent component : components) {
                    if (component instanceof IFormComponent) {
                        List<IFieldSection> sections = ((IFormComponent) component).getFieldSections();
                        for (IFieldSection section : sections) {
                            section.setFields(section.getFields().stream().filter(f -> !f.getFieldName().equals(TPMActivityAgreementFields.AGREEMENT_STATUS)).collect(Collectors.toList()));
                        }
                    }
                }
            } catch (MetadataServiceException ex) {
                log.info("after task cause error.", ex);
            }
        }
        try {
            LayoutExt layout = LayoutExt.of(result.getLayout().toLayout());
            FormComponentExt form = layout.getFormComponent().orElse(null);
            if (!Objects.isNull(form)) {
                List<IFieldSection> sections = form.getFieldSections();
                for (IFieldSection section : sections) {
                    section.getFields().forEach(field -> {
                        if (field.getFieldName().equals(TPMActivityAgreementFields.AGREEMENT_CASHING_TYPE)) {
                            field.setReadOnly(true);
                        }
                    });
                }
            }
        } catch (Exception ex) {
            log.info("override TPMActivityAgreementObj layout cause unknown exception  : ", ex);
        }
        try {
            List<DetailObjectListResult> detailObjectList = result.getDetailObjectList();
            for (DetailObjectListResult detailObjectListResult : detailObjectList) {
                String objectApiName = detailObjectListResult.getObjectApiName();
                if (Objects.equals(objectApiName, ApiNames.TPM_ACTIVITY_AGREEMENT_CASHING_PRODUCT_OBJ)) {
                    for (RecordTypeLayoutStructure structure : detailObjectListResult.getLayoutList()) {
                        Map detailLayout = structure.getDetail_layout();
                        LayoutExt layoutExt = LayoutExt.of(detailLayout);

                        Optional<FormComponentExt> component = layoutExt.getFormComponent();
                        if (component.isPresent()) {
                            FormComponentExt formComponent = component.get();
                            for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                                for (IFormField field : fieldSection.getFields()) {
                                    if (READ_ONLY_FIELD_API_NAME.contains(field.getFieldName())) {
                                        field.setReadOnly(true);
                                    }
                                    if (REQUIRED_FIELD_API_NAME.contains(field.getFieldName())){
                                        field.setRequired(true);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.info("override TPMActivityAgreementObj detail layout cause unknown exception  : ", ex);
        }

        return super.after(arg, result);
    }
}
