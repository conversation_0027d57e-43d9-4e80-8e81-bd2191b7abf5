package com.facishare.crm.fmcg.tpm.retry.setter;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandlerEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2023/11/16 17:52
 */
//IgnoreI18nFile
@Component
public class RedPacketWithdrawSetter extends BaseSetter {

    public void setUpdateStatusTask(String tenantId, String redPacketId, String apiName) {
        long nextExecuteTime = System.currentTimeMillis() + 1000 * 5;
        setUpdateStatusTask(tenantId, redPacketId, apiName, nextExecuteTime);
    }

    public void setUpdateStatusTask(String tenantId, String withdrawId, String apiName, long nextExecuteTime) {
        String name = String.format("更新红包提现状态任务_%s_%s", ApiNames.WITHDRAW_RECORD_OBJ, withdrawId);
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", tenantId);
        params.put("withdrawId", withdrawId);
        params.put("apiName", apiName);
        setInit(tenantId, name, RetryHandlerEnum.RED_PACKET_WITHDRAW_UPDATE_HANDLER.code(), JSON.toJSONString(params), 5, nextExecuteTime);
    }
}
