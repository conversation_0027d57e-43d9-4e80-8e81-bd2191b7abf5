package com.facishare.crm.fmcg.tpm.web.custom;

import com.facishare.crm.fmcg.common.apiname.CommonBudgetBizObjectFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountDetailFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.web.contract.kk.*;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.ICommonBudgetManager;
import com.facishare.crm.fmcg.tpm.web.manager.dto.CommonConsumeItem;
import com.facishare.crm.fmcg.tpm.web.manager.dto.CommonWriteOffItem;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.custom.abstraction.ICommonBudgetService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/11/14 11:37
 */
@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class CommonBudgetService extends BaseService implements ICommonBudgetService {

    public static final BigDecimal ZERO = new BigDecimal("0");

    public static final String COMMON_BUSINESS_TRACE_TEMPLATE = "FMCG:COMMON_BUDGET:B:%s.%s";
    public static final String COMMON_APPROVAL_TRACE_TEMPLATE = "FMCG:COMMON_BUDGET:A:%s.%s";
    public static final String BIZ_OBJ_BUDGET_OPERATOR_LOCK_KEY_TEMPLATE = "FMCG:TPM:BIZ_OBJ_BUDGET:LOCK:%s.%s.%s";

    public static final long LOCK_WAIT = 2;
    public static final long LOCK_LEASE = 180;

    @Resource
    private RedissonClient redissonCmd;
    @Resource
    private ICommonBudgetManager commonBudgetManager;

    @Override
    public FreezeValidate.Result freezeValidate(FreezeValidate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        String id = IdGenerator.get();
        String businessTraceId = String.format(COMMON_BUSINESS_TRACE_TEMPLATE, "ValidateObj", id);
        String approvalTraceId = String.format(COMMON_APPROVAL_TRACE_TEMPLATE, "ValidateObj", id);

        // lock biz object for budget operation
        List<CommonConsumeItem> data = Lists.newArrayList();
        try {
            if (CollectionUtils.isEmpty(arg.getFreezeData())) {
                throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_0));
            }

            if (arg.getFreezeData().size() > 20) {
                throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_1));
            }

            for (FreezeValidate.FreezeItemVO freezeDatum : arg.getFreezeData()) {
                if (Strings.isNullOrEmpty(freezeDatum.getAccountId())) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_2));
                }
                if (Objects.isNull(freezeDatum.getAmount())) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_3));
                }
                if (freezeDatum.getAmount().compareTo(ZERO) <= 0) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_4));
                }
            }

            Map<String, BigDecimal> merged = Maps.newHashMap();

            // merge account freeze amount
            for (FreezeValidate.FreezeItemVO datum : arg.getFreezeData()) {
                if (merged.containsKey(datum.getAccountId())) {
                    BigDecimal newAmount = datum.getAmount().add(merged.get(datum.getAccountId()));
                    merged.put(datum.getAccountId(), newAmount);
                } else {
                    merged.put(datum.getAccountId(), datum.getAmount());
                }
            }

            // init budget operator
            for (Map.Entry<String, BigDecimal> entry : merged.entrySet()) {
                IBudgetOperator operator = BudgetOperatorFactory.initOperator(
                        BizType.CONSUME,
                        User.systemUser(context.getTenantId()),
                        entry.getKey(),
                        businessTraceId,
                        approvalTraceId
                );
                data.add(CommonConsumeItem.builder().operator(operator).amount(entry.getValue()).build());
            }

            // lock all operator
            for (CommonConsumeItem datum : data) {
                if (!datum.getOperator().tryLock()) {
                    throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_0), datum.getOperator().getAccount().getName()));
                }
            }

            // validate consumable amount
            for (CommonConsumeItem datum : data) {
                datum.getOperator().validateConsumableAmount(datum.getAmount());
            }
        } catch (Exception ex) {
            throw new MetaDataBusinessException(ex.getMessage());
        } finally {
            for (CommonConsumeItem freezeDatum : data) {
                freezeDatum.getOperator().unlock();
            }
        }

        return FreezeValidate.Result.builder().businessTraceId(businessTraceId).approvalTraceId(approvalTraceId).build();
    }

    @Override
    public Freeze.Result freeze(Freeze.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (Strings.isNullOrEmpty(arg.getApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_5));
        }

        if (Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_6));
        }

        // find business object data and init trace information
        IObjectData bizData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getDataId(), arg.getApiName());
        String businessTraceId = String.format(COMMON_BUSINESS_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());
        String approvalTraceId = String.format(COMMON_APPROVAL_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());

        // lock biz object for budget operation
        if (tryLock(context.getTenantId(), bizData)) {
            List<CommonConsumeItem> data = Lists.newArrayList();
            try {
                String code = bizData.get(CommonBudgetBizObjectFields.BUDGET_OPERATION_CODE, String.class);
                if (CommonBudgetBizObjectFields.BUDGET_CODE_CLOSE.equals(code)) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_7));
                }

                if (CollectionUtils.isEmpty(arg.getFreezeData())) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_8));
                }

                if (arg.getFreezeData().size() > 20) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_9));
                }

                for (Freeze.FreezeItemVO freezeDatum : arg.getFreezeData()) {
                    if (Strings.isNullOrEmpty(freezeDatum.getAccountId())) {
                        throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_10));
                    }
                    if (Objects.isNull(freezeDatum.getAmount())) {
                        throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_11));
                    }
                    if (freezeDatum.getAmount().compareTo(ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_12));
                    }
                }

                Map<String, BigDecimal> merged = Maps.newHashMap();

                // merge account freeze amount
                for (Freeze.FreezeItemVO datum : arg.getFreezeData()) {
                    if (merged.containsKey(datum.getAccountId())) {
                        BigDecimal newAmount = datum.getAmount().add(merged.get(datum.getAccountId()));
                        merged.put(datum.getAccountId(), newAmount);
                    } else {
                        merged.put(datum.getAccountId(), datum.getAmount());
                    }
                }

                List<IObjectData> frozenDetails = commonBudgetManager.queryFrozenDetails(context.getTenantId(), bizData, businessTraceId, approvalTraceId);
                Set<String> frozenAccountIds = frozenDetails.stream()
                        .map(detail -> detail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class))
                        .collect(Collectors.toSet());

                // init budget operator
                for (Map.Entry<String, BigDecimal> entry : merged.entrySet()) {
                    IBudgetOperator operator = BudgetOperatorFactory.initOperator(
                            BizType.CONSUME,
                            User.systemUser(context.getTenantId()),
                            entry.getKey(),
                            businessTraceId,
                            approvalTraceId,
                            bizData
                    );
                    if (frozenAccountIds.contains(operator.getAccount().getId())) {
                        throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_1), operator.getAccount().getName()));
                    }

                    data.add(CommonConsumeItem.builder().operator(operator).amount(entry.getValue()).build());
                }

                // lock all operator
                for (CommonConsumeItem datum : data) {
                    if (!datum.getOperator().tryLock()) {
                        throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_2), datum.getOperator().getAccount().getName()));
                    }
                }

                // validate consumable amount
                for (CommonConsumeItem datum : data) {
                    datum.getOperator().validateConsumableAmount(datum.getAmount());
                }

                // do freeze
                commonBudgetManager.freeze(context.getTenantId(), bizData, data);

            }
            catch (Exception ex) {
                commonBudgetManager.saveError(context.getTenantId(), bizData, ex.getMessage());
                throw new MetaDataBusinessException(ex.getMessage());
            } finally {
                for (CommonConsumeItem freezeDatum : data) {
                    freezeDatum.getOperator().unlock();
                }
                unlock(context.getTenantId(), bizData);
            }
        } else {
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_3), bizData.getName()));
        }
        return Freeze.Result.builder().businessTraceId(businessTraceId).approvalTraceId(approvalTraceId).build();
    }

    @Override
    public AppendFreeze.Result appendFreeze(AppendFreeze.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (Strings.isNullOrEmpty(arg.getApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_13));
        }

        if (Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_14));
        }

        // find business object data and init trace information
        IObjectData bizData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getDataId(), arg.getApiName());
        String businessTraceId = String.format(COMMON_BUSINESS_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());
        String approvalTraceId = String.format(COMMON_APPROVAL_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());

        // lock biz object for budget operation
        if (tryLock(context.getTenantId(), bizData)) {
            List<CommonConsumeItem> data = Lists.newArrayList();
            try {
                String code = bizData.get(CommonBudgetBizObjectFields.BUDGET_OPERATION_CODE, String.class);
                if (CommonBudgetBizObjectFields.BUDGET_CODE_CLOSE.equals(code)) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_15));
                }

                if (CollectionUtils.isEmpty(arg.getFreezeData())) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_16));
                }

                if (arg.getFreezeData().size() > 20) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_17));
                }

                for (AppendFreeze.FreezeItemVO freezeDatum : arg.getFreezeData()) {
                    if (Strings.isNullOrEmpty(freezeDatum.getAccountId())) {
                        throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_18));
                    }
                    if (Objects.isNull(freezeDatum.getAmount())) {
                        throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_19));
                    }
                    if (freezeDatum.getAmount().compareTo(ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_20));
                    }
                }

                Map<String, BigDecimal> merged = Maps.newHashMap();

                // merge account freeze amount
                for (AppendFreeze.FreezeItemVO datum : arg.getFreezeData()) {
                    if (merged.containsKey(datum.getAccountId())) {
                        BigDecimal newAmount = datum.getAmount().add(merged.get(datum.getAccountId()));
                        merged.put(datum.getAccountId(), newAmount);
                    } else {
                        merged.put(datum.getAccountId(), datum.getAmount());
                    }
                }

                List<IObjectData> frozenDetails = commonBudgetManager.queryFrozenDetails(context.getTenantId(), bizData, businessTraceId, approvalTraceId);
                Set<String> frozenAccountIds = frozenDetails.stream()
                        .map(detail -> detail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class))
                        .collect(Collectors.toSet());

                // init budget operator
                for (Map.Entry<String, BigDecimal> entry : merged.entrySet()) {
                    IBudgetOperator operator = BudgetOperatorFactory.initOperator(
                            BizType.CONSUME,
                            User.systemUser(context.getTenantId()),
                            entry.getKey(),
                            businessTraceId,
                            approvalTraceId,
                            bizData
                    );
                    if (!frozenAccountIds.contains(operator.getAccount().getId())) {
                        throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_4), operator.getAccount().getName()));
                    }

                    data.add(CommonConsumeItem.builder().operator(operator).amount(entry.getValue()).build());
                }

                // lock all operator
                for (CommonConsumeItem datum : data) {
                    if (!datum.getOperator().tryLock()) {
                        throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_5), datum.getOperator().getAccount().getName()));
                    }
                }

                // validate consumable amount
                for (CommonConsumeItem datum : data) {
                    datum.getOperator().validateConsumableAmount(datum.getAmount());
                }

                // do freeze
                commonBudgetManager.appendFreeze(context.getTenantId(), bizData, data);

            }
            catch (Exception ex) {
                commonBudgetManager.saveError(context.getTenantId(), bizData, ex.getMessage());
                throw new MetaDataBusinessException(ex.getMessage());
            } finally {
                for (CommonConsumeItem freezeDatum : data) {
                    freezeDatum.getOperator().unlock();
                }
                unlock(context.getTenantId(), bizData);
            }
        } else {
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_6), bizData.getName()));
        }
        return AppendFreeze.Result.builder().businessTraceId(businessTraceId).approvalTraceId(approvalTraceId).build();
    }

    @Override
    public Cancel.Result cancel(Cancel.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (Strings.isNullOrEmpty(arg.getApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_21));
        }

        if (Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_22));
        }

        IObjectData bizData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getDataId(), arg.getApiName());
        String businessTraceId = String.format(COMMON_BUSINESS_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());
        String approvalTraceId = String.format(COMMON_APPROVAL_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());

        List<IObjectData> details = commonBudgetManager.queryRelatedDetails(context.getTenantId(), bizData);

        if (CollectionUtils.isNotEmpty(details)) {
            List<String> accountIds = details.stream().map(detail -> detail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class)).collect(Collectors.toList());
            List<IBudgetOperator> operators = Lists.newArrayList();

            try {
                for (String accountId : accountIds) {
                    IBudgetOperator operator = BudgetOperatorFactory.initOperator(BizType.INVALID_BACK, User.systemUser(context.getTenantId()), accountId, businessTraceId, approvalTraceId, bizData);
                    if (!operator.tryLock()) {
                        throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_7), operator.getAccount().getName()));
                    }
                    operators.add(operator);
                }
                commonBudgetManager.cancel(context.getTenantId(), bizData, details, operators);
            } catch (Exception ex) {
                throw new MetaDataBusinessException(ex.getMessage());
            } finally {
                for (IBudgetOperator operator : operators) {
                    operator.unlock();
                }
            }
        }

        return Cancel.Result.builder().businessTraceId(businessTraceId).approvalTraceId(approvalTraceId).build();
    }

    @Override
    public Unfreeze.Result unfreeze(Unfreeze.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (Strings.isNullOrEmpty(arg.getApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_23));
        }

        if (Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_24));
        }

        // find business object data and init trace information
        IObjectData bizData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getDataId(), arg.getApiName());
        String businessTraceId = String.format(COMMON_BUSINESS_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());
        String approvalTraceId = String.format(COMMON_APPROVAL_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());

        // lock biz object for budget operation
        if (tryLock(context.getTenantId(), bizData)) {
            List<IBudgetOperator> operators = Lists.newArrayList();
            try {
                String code = bizData.get(CommonBudgetBizObjectFields.BUDGET_OPERATION_CODE, String.class);
                if (CommonBudgetBizObjectFields.BUDGET_CODE_CLOSE.equals(code)) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_25));
                }

                List<IObjectData> frozenDetails = commonBudgetManager.queryFrozenDetails(context.getTenantId(), bizData, businessTraceId, approvalTraceId);
                for (IObjectData frozenDetail : frozenDetails) {
                    IBudgetOperator operator = BudgetOperatorFactory.initOperator(
                            BizType.RELEASE,
                            User.systemUser(context.getTenantId()),
                            frozenDetail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class),
                            businessTraceId,
                            approvalTraceId,
                            bizData
                    );
                    operators.add(operator);
                }

                for (IBudgetOperator operator : operators) {
                    if (!operator.tryLock()) {
                        throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_8), operator.getAccount().getName()));
                    }
                }

                commonBudgetManager.unfreeze(context.getTenantId(), bizData, operators);
            } catch (Exception ex) {
                commonBudgetManager.saveError(context.getTenantId(), bizData, ex.getMessage());
                throw new MetaDataBusinessException(ex.getMessage());
            } finally {
                for (IBudgetOperator operator : operators) {
                    operator.unlock();
                }
                unlock(context.getTenantId(), bizData);
            }
        } else {
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_9), bizData.getName()));
        }
        return Unfreeze.Result.builder().businessTraceId(businessTraceId).approvalTraceId(approvalTraceId).build();
    }

    @Override
    public WriteOffValidate.Result writeOffValidate(WriteOffValidate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (Strings.isNullOrEmpty(arg.getApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_26));
        }
        if (Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_27));
        }

        // find business object data and init trace information
        IObjectData bizObj = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getDataId(), arg.getApiName());

        String businessTraceId = String.format(COMMON_BUSINESS_TRACE_TEMPLATE, bizObj.getDescribeApiName(), bizObj.getId());
        String approvalTraceId = String.format(COMMON_APPROVAL_TRACE_TEMPLATE, bizObj.getDescribeApiName(), bizObj.getId());

        List<CommonWriteOffItem> data = Lists.newArrayList();
        try {
            String code = bizObj.get(CommonBudgetBizObjectFields.BUDGET_OPERATION_CODE, String.class);
            if (CommonBudgetBizObjectFields.BUDGET_CODE_CLOSE.equals(code)) {
                throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_28));
            }

            if (CollectionUtils.isEmpty(arg.getWriteOffData())) {
                throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_29));
            }

            for (WriteOffValidate.WriteOffItemVO freezeDatum : arg.getWriteOffData()) {
                if (Strings.isNullOrEmpty(freezeDatum.getAccountId())) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_30));
                }
                if (Objects.isNull(freezeDatum.getAmount())) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_31));
                }
                if (freezeDatum.getAmount().compareTo(ZERO) <= 0) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_32));
                }
            }

            Map<String, BigDecimal> merged = Maps.newHashMap();

            for (WriteOffValidate.WriteOffItemVO datum : arg.getWriteOffData()) {
                if (merged.containsKey(datum.getAccountId())) {
                    BigDecimal newAmount = datum.getAmount().add(merged.get(datum.getAccountId()));
                    merged.put(datum.getAccountId(), newAmount);
                } else {
                    merged.put(datum.getAccountId(), datum.getAmount());
                }
            }

            List<IObjectData> frozenDetails = commonBudgetManager.queryFrozenDetails(context.getTenantId(), bizObj, businessTraceId, approvalTraceId);
            Set<String> frozenAccountIds = frozenDetails.stream()
                    .map(detail -> detail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class))
                    .collect(Collectors.toSet());

            // init budget operator
            for (Map.Entry<String, BigDecimal> entry : merged.entrySet()) {
                IBudgetOperator operator = BudgetOperatorFactory.initOperator(
                        BizType.CONSUME,
                        User.systemUser(context.getTenantId()),
                        entry.getKey(),
                        businessTraceId,
                        approvalTraceId,
                        bizObj
                );
                if (frozenAccountIds.contains(operator.getAccount().getId())) {
                    data.add(CommonWriteOffItem.builder().operator(operator).amount(entry.getValue()).frozen(true).build());
                } else {
                    if (TPMGrayUtils.enableNotFrozenWriteOff(context.getTenantId())) {
                        data.add(CommonWriteOffItem.builder().operator(operator).amount(entry.getValue()).frozen(false).build());
                    } else {
                        throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_10), operator.getAccount().getName()));
                    }
                }
            }

            // lock all operator
            for (CommonWriteOffItem datum : data) {
                if (!datum.getOperator().tryLock()) {
                    throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_11), datum.getOperator().getAccount().getName()));
                }
            }

            // validate consumable amount
            for (CommonWriteOffItem datum : data) {
                if (datum.isFrozen()) {
                    if (TPMGrayUtils.enableOverLimitWriteOff(context.getTenantId())) {
                        datum.getOperator().validateOverLimitWriteOffAmount(datum.getAmount());
                    } else {
                        datum.getOperator().validateWriteOffAmount(datum.getAmount());
                    }
                } else {
                    datum.getOperator().validateConsumableAmount(datum.getAmount());
                }
            }
        } catch (Exception ex) {
            throw new MetaDataBusinessException(ex.getMessage());
        } finally {
            for (CommonWriteOffItem datum : data) {
                datum.getOperator().unlock();
            }
        }

        return WriteOffValidate.Result.builder().businessTraceId(businessTraceId).approvalTraceId(approvalTraceId).build();
    }

    @Override
    public WriteOff.Result writeOff(WriteOff.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (Strings.isNullOrEmpty(arg.getApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_33));
        }
        if (Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_34));
        }

        if (Strings.isNullOrEmpty(arg.getWriteOffObjectApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_35));
        }
        if (Strings.isNullOrEmpty(arg.getWriteOffObjectDataId())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_36));
        }

        // find business object data and init trace information
        IObjectData bizObj = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getDataId(), arg.getApiName());
        IObjectData writeOffObj = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getWriteOffObjectDataId(), arg.getWriteOffObjectApiName());

        String businessTraceId = String.format(COMMON_BUSINESS_TRACE_TEMPLATE, bizObj.getDescribeApiName(), bizObj.getId());
        String approvalTraceId = String.format(COMMON_APPROVAL_TRACE_TEMPLATE, bizObj.getDescribeApiName(), bizObj.getId());

        // lock biz object for budget operation
        if (tryLock(context.getTenantId(), bizObj)) {
            List<CommonWriteOffItem> data = Lists.newArrayList();
            try {
                String code = bizObj.get(CommonBudgetBizObjectFields.BUDGET_OPERATION_CODE, String.class);
                if (CommonBudgetBizObjectFields.BUDGET_CODE_CLOSE.equals(code)) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_37));
                }

                if (CollectionUtils.isEmpty(arg.getWriteOffData())) {
                    throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_38));
                }

                for (WriteOff.WriteOffItemVO freezeDatum : arg.getWriteOffData()) {
                    if (Strings.isNullOrEmpty(freezeDatum.getAccountId())) {
                        throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_39));
                    }
                    if (Objects.isNull(freezeDatum.getAmount())) {
                        throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_40));
                    }
                    if (freezeDatum.getAmount().compareTo(ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_41));
                    }
                }

                Map<String, BigDecimal> merged = Maps.newHashMap();

                for (WriteOff.WriteOffItemVO datum : arg.getWriteOffData()) {
                    if (merged.containsKey(datum.getAccountId())) {
                        BigDecimal newAmount = datum.getAmount().add(merged.get(datum.getAccountId()));
                        merged.put(datum.getAccountId(), newAmount);
                    } else {
                        merged.put(datum.getAccountId(), datum.getAmount());
                    }
                }

                List<IObjectData> frozenDetails = commonBudgetManager.queryFrozenDetails(context.getTenantId(), bizObj, businessTraceId, approvalTraceId);
                Set<String> frozenAccountIds = frozenDetails.stream()
                        .map(detail -> detail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class))
                        .collect(Collectors.toSet());

                // init budget operator
                for (Map.Entry<String, BigDecimal> entry : merged.entrySet()) {
                    IBudgetOperator operator = BudgetOperatorFactory.initOperator(
                            BizType.CONSUME,
                            User.systemUser(context.getTenantId()),
                            entry.getKey(),
                            businessTraceId,
                            approvalTraceId,
                            bizObj
                    );

                    if (frozenAccountIds.contains(operator.getAccount().getId())) {
                        data.add(CommonWriteOffItem.builder().operator(operator).amount(entry.getValue()).frozen(true).build());
                    } else {
                        if (TPMGrayUtils.enableNotFrozenWriteOff(context.getTenantId())) {
                            data.add(CommonWriteOffItem.builder().operator(operator).amount(entry.getValue()).frozen(false).build());
                        } else {
                            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_12), operator.getAccount().getName()));
                        }
                    }
                }

                // lock all operator
                for (CommonWriteOffItem datum : data) {
                    if (!datum.getOperator().tryLock()) {
                        throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_13), datum.getOperator().getAccount().getName()));
                    }
                }

                // validate consumable amount
                for (CommonWriteOffItem datum : data) {
                    if (datum.isFrozen()) {
                        if (TPMGrayUtils.enableOverLimitWriteOff(context.getTenantId())) {
                            datum.getOperator().validateOverLimitWriteOffAmount(datum.getAmount());
                        } else {
                            datum.getOperator().validateWriteOffAmount(datum.getAmount());
                        }
                    } else {
                        datum.getOperator().validateConsumableAmount(datum.getAmount());
                    }
                }

                commonBudgetManager.writeOff(context.getTenantId(), bizObj, writeOffObj, data);
            } catch (Exception ex) {
                commonBudgetManager.saveError(context.getTenantId(), writeOffObj, ex.getMessage());
                throw new MetaDataBusinessException(ex.getMessage());
            } finally {
                for (CommonWriteOffItem datum : data) {
                    datum.getOperator().unlock();
                }
                unlock(context.getTenantId(), bizObj);
            }
        } else {
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_14), bizObj.getName()));
        }
        return WriteOff.Result.builder().businessTraceId(businessTraceId).approvalTraceId(approvalTraceId).build();
    }

    @Override
    public Close.Result close(Close.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (Strings.isNullOrEmpty(arg.getApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_42));
        }

        if (Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18N.text(I18NKeys.COMMON_BUDGET_SERVICE_43));
        }

        // find business object data and init trace information
        IObjectData bizData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getDataId(), arg.getApiName());
        String businessTraceId = String.format(COMMON_BUSINESS_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());
        String approvalTraceId = String.format(COMMON_APPROVAL_TRACE_TEMPLATE, bizData.getDescribeApiName(), bizData.getId());

        // lock biz object for budget operation
        if (tryLock(context.getTenantId(), bizData)) {
            List<IBudgetOperator> operators = Lists.newArrayList();
            try {
                List<IObjectData> frozenDetails = commonBudgetManager.queryFrozenDetails(context.getTenantId(), bizData, businessTraceId, approvalTraceId);
                for (IObjectData frozenDetail : frozenDetails) {
                    IBudgetOperator operator = BudgetOperatorFactory.initOperator(
                            BizType.RELEASE,
                            User.systemUser(context.getTenantId()),
                            frozenDetail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class),
                            businessTraceId,
                            approvalTraceId,
                            bizData
                    );
                    operators.add(operator);
                }

                for (IBudgetOperator operator : operators) {
                    if (!operator.tryLock()) {
                        throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_15), operator.getAccount().getName()));
                    }
                }

                commonBudgetManager.close(context.getTenantId(), arg.getOperator(), bizData, operators);
            } catch (Exception ex) {
                commonBudgetManager.saveError(context.getTenantId(), bizData, ex.getMessage());
                throw new MetaDataBusinessException(ex.getMessage());
            } finally {
                for (IBudgetOperator operator : operators) {
                    operator.unlock();
                }
                unlock(context.getTenantId(), bizData);
            }
        } else {
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_16), bizData.getName()));
        }
        return Close.Result.builder().businessTraceId(businessTraceId).approvalTraceId(approvalTraceId).build();
    }

    private boolean tryLock(String tenantId, IObjectData bizData) {
        String key = String.format(BIZ_OBJ_BUDGET_OPERATOR_LOCK_KEY_TEMPLATE, tenantId, bizData.getDescribeApiName(), bizData.getId());
        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_COMMON_BUDGET_SERVICE_17), bizData.getName()));
        }
    }

    private void unlock(String tenantId, IObjectData bizData) {
        String key = String.format(BIZ_OBJ_BUDGET_OPERATOR_LOCK_KEY_TEMPLATE, tenantId, bizData.getDescribeApiName(), bizData.getId());
        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
