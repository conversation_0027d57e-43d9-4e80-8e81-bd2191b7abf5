package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.api.consume.EndConsume;
import com.facishare.crm.fmcg.tpm.api.consume.PreValidateConsumeRuleAmount;
import com.facishare.crm.fmcg.tpm.api.consume.Reconsume;
import com.facishare.crm.fmcg.tpm.api.plugin.DealApprovalAction;
import com.facishare.crm.fmcg.tpm.api.plugin.DealTriggerAction;
import com.facishare.paas.appframework.core.model.User;

/**
 * <AUTHOR>
 * @date 2022/11/10 下午2:59
 */
public interface IBudgetConsumeV2Service {

    void dealActionTriggerBefore(DealTriggerAction.Arg arg);

    void dealActionTriggerAfter(DealTriggerAction.Arg arg);

    void dealActionFinallyDo(DealTriggerAction.Arg arg);

    void dealFlowCompletedBefore(DealApprovalAction.Arg arg);

    void dealFlowCompletedAfter(DealApprovalAction.Arg arg);

    void dealFlowCompletedFinallyDo(DealApprovalAction.Arg arg);

    EndConsume.Result endConsume(User user, String describeApiName, String dataId, boolean isForceEnd);

    Reconsume.Result reconsume(User user, String describeApiName, String dataId, String action);

    PreValidateConsumeRuleAmount.Result preValidateConsumeRuleAmount(User user, PreValidateConsumeRuleAmount.Arg arg);

    void middleRelease(User user, String describeApiName, String dataId, String action);
}
