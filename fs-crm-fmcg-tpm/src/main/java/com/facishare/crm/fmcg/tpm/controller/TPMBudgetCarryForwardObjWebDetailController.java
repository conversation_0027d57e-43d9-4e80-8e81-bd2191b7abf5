package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.TPMBudgetCarryForwardFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
@SuppressWarnings("all")
public class TPMBudgetCarryForwardObjWebDetailController extends StandardWebDetailController {

    public static final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);
    protected static final Set<String> ALL_TIME_DIMENSION_FIELD_API_NAMES = Sets.newHashSet();

    static {
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.SOURCE_MONTH);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.SOURCE_QUARTER);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.SOURCE_YEAR);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.TARGET_MONTH);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.TARGET_QUARTER);
        ALL_TIME_DIMENSION_FIELD_API_NAMES.add(TPMBudgetCarryForwardFields.TARGET_YEAR);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);

        // 根据预算模版时间维度隐藏掉不必要的时间字段
        removeUselessTimeDimensionFields(rst);
        // 移除作废和编辑按钮
        removeUselessButtons(rst);
        // 添加重试按钮
        fillRetryButton(rst);

        return rst;
    }

    private void fillRetryButton(Result result) {
        Optional<IComponent> component = LayoutExt.of(result.getLayout()).getHeadInfoComponent();
        if (component.isPresent()) {
            IComponent head = component.get();
            String status = (String) result.getData().get(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS);
            if (Objects.equals(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__UNFROZEN_FAILED, status) ||
                    Objects.equals(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FAILED, status) ||
                    Objects.equals(TPMBudgetCarryForwardFields.CARRY_FORWARD_STATUS__FROZEN_FAILED, status)) {
                if (head.getButtons().stream().noneMatch(button -> Objects.equals(button.getName(), ObjectAction.BUDGET_CARRY_FORWARD_RETRY.getButtonApiName()))) {

                    IButton button = new Button();
                    button.setAction(ObjectAction.BUDGET_CARRY_FORWARD_RETRY.getActionCode());
                    button.setLabel(ObjectAction.BUDGET_CARRY_FORWARD_RETRY.getActionLabel());
                    button.setActionType(IButton.ACTION_TYPE_DEFAULT);
                    button.setName(ObjectAction.BUDGET_CARRY_FORWARD_RETRY.getButtonApiName());

                    List<IButton> newButtons = head.getButtons();
                    newButtons.add(button);
                    head.setButtons(newButtons);
                }
            }
        }
    }

    private void removeUselessButtons(Result rst) {

        String lifeStatus = (String) rst.getData().get(ObjectLifeStatus.LIFE_STATUS_API_NAME);
        List<String> blockedActions = Lists.newArrayList("Clone");

        if (!"ineffective".equals(lifeStatus)) {
            blockedActions.add("Edit");
            blockedActions.add("Abolish");
        }

        LayoutExt layoutExt = LayoutExt.of(rst.getLayout());
        Optional<IComponent> component = layoutExt.getHeadInfoComponent();
        if (component.isPresent()) {
            IComponent headInfoComponent = component.get();
            headInfoComponent.setButtons(
                    headInfoComponent.getButtons()
                            .stream()
                            .filter(button -> !blockedActions.contains(button.getAction()))
                            .collect(Collectors.toList())

            );
        }
    }

    private void removeUselessTimeDimensionFields(Result rst) {
        if (Objects.nonNull(rst.getData())) {
            String typeId = (String) rst.getData().get(TPMBudgetCarryForwardFields.BUDGET_TYPE_ID);
            String nodeId = (String) rst.getData().get(TPMBudgetCarryForwardFields.BUDGET_NODE_ID);
            if (!Strings.isNullOrEmpty(nodeId) && !Strings.isNullOrEmpty(typeId)) {
                BudgetTypePO type = budgetTypeDAO.get(controllerContext.getTenantId(), typeId);
                if (Objects.nonNull(type)) {
                    BudgetTypeNodeEntity node = type.getNodes().stream().filter(f -> f.getNodeId().equals(nodeId)).findFirst().orElse(null);

                    Set<String> uselessFieldApiNames = ALL_TIME_DIMENSION_FIELD_API_NAMES.stream()
                            .filter(dimension -> !dimension.endsWith(node.getTimeDimension()))
                            .collect(Collectors.toSet());

                    LayoutExt layoutExt = LayoutExt.of(rst.getLayout());
                    Optional<FormComponentExt> component = layoutExt.getFormComponent();

                    if (component.isPresent()) {
                        FormComponentExt formComponent = component.get();
                        for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                            fieldSection.setFields(fieldSection.getFields().stream()
                                    .filter(field -> !uselessFieldApiNames.contains(field.getFieldName()))
                                    .collect(Collectors.toList()));
                        }
                    }
                }
            }
        }
    }
}
