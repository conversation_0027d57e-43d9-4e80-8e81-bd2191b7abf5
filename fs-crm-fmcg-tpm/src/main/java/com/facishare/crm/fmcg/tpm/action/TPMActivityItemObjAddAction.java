package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.TPMActivityItemFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;
import de.lab4inf.math.util.Strings;


/**
 * <AUTHOR>
 */
public class TPMActivityItemObjAddAction extends StandardAddAction {

    private final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);
    private boolean isTpm2Tenant = false;

    @Override
    protected void before(Arg arg) {
        this.isTpm2Tenant = tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()));
        setDefaultValue(arg);
        super.before(arg);
    }

    private void setDefaultValue(Arg arg) {
        if (Strings.isNullOrEmpty(String.valueOf(arg.getObjectData().get(TPMActivityItemFields.COST_STANDARD)))) {
            arg.getObjectData().put(TPMActivityItemFields.COST_STANDARD, 0);
        }
        if (Strings.isNullOrEmpty(String.valueOf(arg.getObjectData().get(TPMActivityItemFields.AMOUNT_STANDARD)))) {
            arg.getObjectData().put(TPMActivityItemFields.AMOUNT_STANDARD, 1);
        }
        if (Strings.isNullOrEmpty(String.valueOf(arg.getObjectData().get(TPMActivityItemFields.CALCULATE_PATTERN)))) {
            arg.getObjectData().put(TPMActivityItemFields.CALCULATE_PATTERN, "1");
        }
        if (Strings.isNullOrEmpty(String.valueOf(arg.getObjectData().get(TPMActivityItemFields.AMOUNT_STANDARD_CHECK)))) {
            arg.getObjectData().put(TPMActivityItemFields.AMOUNT_STANDARD_CHECK, false);
        }
        if (Strings.isNullOrEmpty(String.valueOf(arg.getObjectData().get(TPMActivityItemFields.IS_ACTIVATED)))) {
            arg.getObjectData().put(TPMActivityItemFields.IS_ACTIVATED, true);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_ITEM, BuryOperation.CREATE, isTpm2Tenant);
        return super.after(arg, result);
    }
}
