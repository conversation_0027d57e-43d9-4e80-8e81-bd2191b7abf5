package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetTransferDetailFields;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.common.constant.BudgetTransferDetailConstants;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.FormatUtil;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * author: wuyx
 * description:
 * createTime: 2022/7/14 10:46
 */
@Slf4j
@SuppressWarnings("Duplicates,unused")
public class TPMBudgetTransferDetailObjEditAction extends StandardEditAction {

    protected static final Map<String, BizType> RECORD_TYPE_2_OUT_BIZ_TYPE_MAP = Maps.newHashMap();
    protected static final Map<String, BizType> RECORD_TYPE_2_IN_BIZ_TYPE_MAP = Maps.newHashMap();
    protected static final Map<String, String> RECORD_TYPE_2_MODULE_MAP = Maps.newHashMap();
    protected static final Set<String> SUPPORT_RECORD_TYPES = Sets.newHashSet();

    static {
        SUPPORT_RECORD_TYPES.add(TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT);
        SUPPORT_RECORD_TYPES.add(TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT);
        SUPPORT_RECORD_TYPES.add(TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD);

        RECORD_TYPE_2_MODULE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT, BuryModule.Budget.BUDGET_TRANSFER_DETAIL_ADJUST);
        RECORD_TYPE_2_MODULE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT, BuryModule.Budget.BUDGET_TRANSFER_DETAIL_DEDUCT);
        RECORD_TYPE_2_MODULE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD, BuryModule.Budget.BUDGET_TRANSFER_DETAIL_ADD);

        RECORD_TYPE_2_OUT_BIZ_TYPE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT, BizType.TRANSFER_OUT);
        RECORD_TYPE_2_OUT_BIZ_TYPE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT, BizType.TRANSFER_DEDUCTION);

        RECORD_TYPE_2_IN_BIZ_TYPE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT, BizType.TRANSFER_IN);
        RECORD_TYPE_2_IN_BIZ_TYPE_MAP.put(TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD, BizType.TRANSFER_APPEND);
    }

    private final ITransactionProxy transProxy = SpringUtil.getContext().getBean(ITransactionProxy.class);
    private final IBudgetTypeManager budgetTypeManager = SpringUtil.getContext().getBean(IBudgetTypeManager.class);

    private User systemUser;
    private String recordType;

    private BigDecimal amount;

    private IObjectData oldData;

    private IBudgetOperator transferOutOperator;
    private IBudgetOperator transferInOperator;

    private String approvalTraceId;
    private String businessTraceId;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        this.stopWatch.lap("framework.before");

        // 校验是否可编辑
        this.editableValidate();
        this.stopWatch.lap("editableValidate");

        // 准备上下文数据
        this.prepareContext();
        this.stopWatch.lap("prepareContext");

        // 准备并校验预算调整信息
        this.prepareAndValidateTransferData();
        this.stopWatch.lap("prepareAndValidateTransferData");

        // 占用预算表
        this.tryOccupy();
        this.stopWatch.lap("tryOccupy");
    }

    private void editableValidate() {
        this.oldData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_BUDGET_TRANSFER_DETAIL);
        if (!"ineffective".equals(oldData.get("life_status"))) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_0));
        }
    }

    private void prepareContext() {
        this.systemUser = User.systemUser(actionContext.getTenantId());

        this.approvalTraceId = TraceUtil.initApprovalTraceId();
        this.businessTraceId = TraceUtil.initBusinessTraceId();

        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_APPROVAL_CALLBACK_TRACE_ID_KEY, this.approvalTraceId);
        this.customCallbackData.put(TraceUtil.FMCG_TPM_BUDGET_BUSINESS_CALLBACK_TRACE_ID_KEY, this.businessTraceId);
    }


    private void prepareAndValidateTransferData() {
        // 校验业务类型合法性
        this.recordType = (String) arg.getObjectData().get(CommonFields.RECORD_TYPE);
        if (!SUPPORT_RECORD_TYPES.contains(recordType)) {
            throw new ValidateException("[record_type] not supported.");
        }

        // 给调拨类型以及操作时赋默认值
        arg.getObjectData().put(TPMBudgetTransferDetailFields.TRANSFER_TYPE, this.recordType);
        arg.getObjectData().put(TPMBudgetTransferDetailFields.OPERATE_TIME, System.currentTimeMillis());
        arg.getObjectData().put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_FAILED);
        arg.getObjectData().put(TPMBudgetTransferDetailFields.BUDGET_CHANGE_DETAIL, FormatUtil.formBudgetChangeDetail(this.businessTraceId, "Add", null));

        // 校验预算调整金额
        this.amount = arg.getObjectData().toObjectData().get(TPMBudgetTransferDetailFields.AMOUNT, BigDecimal.class);
        if (this.amount == null || this.amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_1));
        }

        // 根据不同的调拨操作加载以及校验相关数据
        switch (this.recordType) {
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD:
                // 当预算增加时，准备并校验转入预算表信息
                this.prepareAndValidateTransferInData(amount);
                break;
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT:
                // 当预算扣减时，准备并校验转出预算表信息
                this.prepareAndValidateTransferOutData(amount);
                break;
            default:
            case TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT:
                // 当预算调拨时，准备并校验转入，转出预算表信息
                this.prepareAndValidateTransferOutData(amount);
                this.prepareAndValidateTransferInData(amount);

                if(!TPMGrayUtils.skipBudgetTransferRangeCheck(actionContext.getTenantId())) {
                    // 同一张预算表不允许调拨
                    if (this.transferOutOperator.getAccount().getId().equals(this.transferInOperator.getAccount().getId())) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_2));
                    }
                    // 同 parent 的预算表才可以调拨
                    String outParentId = this.transferOutOperator.getAccount().get(TPMBudgetAccountFields.PARENT_ID, String.class);
                    String inParentId = this.transferInOperator.getAccount().get(TPMBudgetAccountFields.PARENT_ID, String.class);
                    if (!Objects.equals(outParentId, inParentId)) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_3));
                    }
                    // 不同类型的预算表不允许调拨
                    if (!this.transferOutOperator.getAccount().get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class)
                            .equals(this.transferInOperator.getAccount().get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class))) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_4));
                    }
                }
                break;
        }
    }

    private void tryOccupy() {
        if (BudgetTransferDetailConstants.NEED_OCCUPY_RECORD_TYPE_SET.contains(this.recordType)) {
            this.transferOutOperator.occupy(this.amount);
        }
    }

    private void prepareAndValidateTransferInData(BigDecimal amount) {
        String transferInAccountId = (String) this.arg.getObjectData().get(TPMBudgetTransferDetailFields.TRANSFER_IN_BUDGET_ACCOUNT_ID);
        if (Strings.isNullOrEmpty(transferInAccountId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_5));
        }

        if (!oldData.get(TPMBudgetTransferDetailFields.TRANSFER_IN_BUDGET_ACCOUNT_ID, String.class)
                .equals(transferInAccountId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_6));
        }

        this.transferInOperator = BudgetOperatorFactory.initOperator(RECORD_TYPE_2_IN_BIZ_TYPE_MAP.get(recordType), actionContext.getUser(), transferInAccountId, this.businessTraceId, this.approvalTraceId);

        if (!this.transferInOperator.tryLock()) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_0));
        }

        BigDecimal available = this.transferInOperator.realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
        this.arg.getObjectData().toObjectData().set(TPMBudgetTransferDetailFields.AMOUNT_BEFORE_TRANSFER_IN, available);

        BigDecimal after = amount.add(available);
        this.arg.getObjectData().toObjectData().set(TPMBudgetTransferDetailFields.AMOUNT_AFTER_TRANSFER_IN, after);
    }

    private void prepareAndValidateTransferOutData(BigDecimal amount) {
        String transferOutAccountId = (String) this.arg.getObjectData().get(TPMBudgetTransferDetailFields.TRANSFER_OUT_BUDGET_ACCOUNT_ID);
        if (Strings.isNullOrEmpty(transferOutAccountId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_7));
        }
        if (!oldData.get(TPMBudgetTransferDetailFields.TRANSFER_OUT_BUDGET_ACCOUNT_ID, String.class)
                .equals(transferOutAccountId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_8));
        }

        this.transferOutOperator = BudgetOperatorFactory.initOperator(RECORD_TYPE_2_OUT_BIZ_TYPE_MAP.get(recordType), actionContext.getUser(), transferOutAccountId, this.businessTraceId, this.approvalTraceId);

        if (!this.transferOutOperator.tryLock()) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_BUDGET_TRANSFER_DETAIL_OBJ_EDIT_ACTION_1));
        }

        this.transferOutOperator.validateOperableAmount(amount);

        BigDecimal available = this.transferOutOperator.realAmount().get(TPMBudgetAccountFields.AVAILABLE_AMOUNT);
        this.arg.getObjectData().toObjectData().set(TPMBudgetTransferDetailFields.AMOUNT_BEFORE_TRANSFER_OUT, available);

        BigDecimal after = available.subtract(amount);
        this.arg.getObjectData().toObjectData().set(TPMBudgetTransferDetailFields.AMOUNT_AFTER_TRANSFER_OUT, after);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result inner = super.after(arg, result);
        this.stopWatch.lap("framework.after");

        IObjectData data = inner.getObjectData().toObjectData();

        if (!isApprovalFlowStartSuccess(data.getId())) {
            this.doTransfer(data);
            this.stopWatch.lap("doTransfer");
        } else {
            this.tryFreeze(data);
            this.stopWatch.lap("tryFreeze");
        }
        return inner;
    }

    private void doTransfer(IObjectData data) {
        switch (this.recordType) {
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD:
                doAddAction(data);
                break;
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT:
                doDeductAction(data);
                break;
            default:
            case TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT:
                doTransferAction(data);
                break;
        }
    }

    private void tryFreeze(IObjectData data) {
        transProxy.run(() -> {
            if (BudgetTransferDetailConstants.NEED_OCCUPY_RECORD_TYPE_SET.contains(this.recordType)) {
                this.transferOutOperator.setWhat(data);
                this.transferOutOperator.releaseOccupy();
                this.transferOutOperator.freeze(this.amount);
                this.transferOutOperator.recalculate();
            }
            Map<String, Object> updater = new HashMap<>();
            updater.put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_FROZEN);
            this.serviceFacade.updateWithMap(this.systemUser, data, updater);
        });
    }

    private void doAddAction(IObjectData data) {
        transProxy.run(() -> {
            this.transferInOperator.setWhat(data);

            this.transferInOperator.income(this.amount);
            this.transferInOperator.recalculate();

            Map<String, Object> updater = new HashMap<>();
            updater.put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_SUCCESS);
            this.serviceFacade.updateWithMap(this.systemUser, data, updater);
        });
    }

    private void doDeductAction(IObjectData data) {
        transProxy.run(() -> {
            this.transferOutOperator.setWhat(data);

            this.transferOutOperator.releaseOccupy();
            this.transferOutOperator.expenditure(this.amount);
            this.transferOutOperator.recalculate();

            String typeId = this.transferOutOperator.getAccount().get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
            String nodeId = this.transferOutOperator.getAccount().get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
            BudgetTypeNodeEntity type = budgetTypeManager.getNode(actionContext.getTenantId(), typeId, nodeId);
            if (type.getAutomaticRecoveryAfterDeduct()) {
                String parentId = this.transferOutOperator.getAccount().get(TPMBudgetAccountFields.PARENT_ID, String.class);
                IBudgetOperator parentOperator = BudgetOperatorFactory.initOperator(BizType.TRANSFER_DEDUCTION_RECOVERY, actionContext.getUser(), parentId, this.businessTraceId, this.approvalTraceId, data);
                parentOperator.income(this.amount);
                parentOperator.recalculate();
            }

            Map<String, Object> updater = new HashMap<>();
            updater.put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_SUCCESS);
            this.serviceFacade.updateWithMap(this.systemUser, data, updater);
        });
    }

    private void doTransferAction(IObjectData data) {
        transProxy.run(() -> {
            this.transferInOperator.setWhat(data);
            this.transferOutOperator.setWhat(data);

            this.transferOutOperator.releaseOccupy();
            this.transferOutOperator.expenditure(this.amount);
            this.transferInOperator.income(this.amount);

            this.transferInOperator.recalculate();
            this.transferOutOperator.recalculate();

            Map<String, Object> updater = new HashMap<>();
            updater.put(TPMBudgetTransferDetailFields.OPERATION_STATUS, TPMBudgetTransferDetailFields.OPERATION_STATUS_SUCCESS);
            this.serviceFacade.updateWithMap(this.systemUser, data, updater);
        });
    }

    @Override
    protected void finallyDo() {
        this.unlockAccount();
        this.stopWatch.lap("unlockAccount");

        super.finallyDo();
        this.stopWatch.lap("framework.finallyDo");
    }

    private void unlockAccount() {
        if (Objects.nonNull(this.transferOutOperator)) {
            this.transferOutOperator.unlock();
        }
        if (Objects.nonNull(this.transferInOperator)) {
            this.transferInOperator.unlock();
        }
    }
}
