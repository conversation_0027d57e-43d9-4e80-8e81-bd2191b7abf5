package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityProofMultipleLevelAuditConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;

/**
 * 8.9.0 RIO
 * <p>
 * create by @yangqf
 * create time 2021/12/6 19:42
 */
@Data
@ToString
@SuppressWarnings("Duplicates")
public class ActivityProofMultipleLevelAuditConfigEntity implements Serializable {

    @Property("audit_action_name")
    private String auditActionName;

    @Property("object_record_type")
    private String objectRecordType;

    @Property("display_field_api_names_of_master")
    private List<String> displayFieldApiNamesOfMaster;

    @Property("display_field_api_names_of_detail")
    private List<String> displayFieldApiNamesOfDetail;

    @Property("is_detail_show_big_image")
    private Boolean isDetailShowBigImage;

    @Property("display_field_api_names_of_audit_master")
    private List<String> displayFieldApiNamesOfAuditMaster;

    @Property("display_field_api_names_of_audit_detail")
    private List<String> displayFieldApiNamesOfAuditDetail;

    @Property("is_audit_detail_show_big_image")
    private Boolean isAuditDetailShowBigImage;

    public static ActivityProofMultipleLevelAuditConfigEntity fromVO(ActivityProofMultipleLevelAuditConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityProofMultipleLevelAuditConfigEntity po = new ActivityProofMultipleLevelAuditConfigEntity();
        po.setAuditActionName(vo.getAuditActionName());
        po.setObjectRecordType(vo.getObjectRecordType());
        po.setDisplayFieldApiNamesOfMaster(vo.getDisplayFieldApiNamesOfMaster());
        po.setDisplayFieldApiNamesOfDetail(vo.getDisplayFieldApiNamesOfDetail());
        po.setIsDetailShowBigImage(vo.getIsDetailShowBigImage());
        po.setDisplayFieldApiNamesOfAuditMaster(vo.getDisplayFieldApiNamesOfAuditMaster());
        po.setDisplayFieldApiNamesOfAuditDetail(vo.getDisplayFieldApiNamesOfAuditDetail());
        po.setIsAuditDetailShowBigImage(vo.getIsAuditDetailShowBigImage());
        return po;
    }
}
