package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityAccountScopeConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

@Data
@ToString
public class ActivityAccountScopeConfigEntity implements Serializable {

    /**
     * 定义客户范围的从对象
     */
    @Property("account_scope_api_name")
    private String accountScopeApiName;

    /**
     * 主从关联字段
     */
    @Property("ref_master_field_api_name")
    private String referenceMasterFieldApiName;

    /**
     * 定义客户范围位置的从对象中，查找关联「客户」对象字段
     */
    @Property("ref_account_field_api_name")
    private String referenceAccountFieldApiName;

    public static ActivityAccountScopeConfigEntity fromVO(ActivityAccountScopeConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityAccountScopeConfigEntity po = new ActivityAccountScopeConfigEntity();
        po.setAccountScopeApiName(vo.getAccountScopeApiName());
        po.setReferenceMasterFieldApiName(vo.getReferenceMasterFieldApiName());
        po.setReferenceAccountFieldApiName(vo.getReferenceAccountFieldApiName());
        return po;
    }
}
