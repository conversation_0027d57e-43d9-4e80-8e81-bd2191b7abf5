package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/6/23 14:42
 */
public enum BudgetPeriodEnum {

    YEAR("year","按年"),
    QUARTER("quarter","按季度"),
    MONTH("month","按月份");
    BudgetPeriodEnum(String value,String label){
        this.value = value;
        this.label = label;
    }

    private static Map<String,BudgetPeriodEnum> value2EnumMap = Stream.of(BudgetPeriodEnum.values()).collect(Collectors.toMap(BudgetPeriodEnum::value,v->v,(before,after)->before));
    private String value;
    private String label;

    public String value(){
        return this.value;
    }

    public static BudgetPeriodEnum of(String value){
        return value2EnumMap.get(value);
    }
}
