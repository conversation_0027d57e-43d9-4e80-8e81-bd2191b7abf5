package com.facishare.crm.fmcg.tpm.retry.setter;


import com.facishare.crm.fmcg.tpm.dao.mongo.RetryTaskDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskStatusEnum;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/8/9 17:34
 */
public abstract class BaseSetter {

    @Resource
    protected RetryTaskDAO retryTaskDAO;

    protected static final Long[] TIME_INTERNAL = new Long[]{10L, 30L, 60L, 600L, 3600L, 3600L * 2, 3600L * 6, 3600L * 12};

    public static final String APP_NAME = System.getProperty("app.name");

    public static final String PROFILES = System.getProperty("process.profile");

    public void setInit(String tenantId, String name, String handler, String param, Integer maxRetryCount, Long nextExecuteTime) {
        set(tenantId, name, handler, param, maxRetryCount, nextExecuteTime, RetryTaskStatusEnum.INIT.code());
    }


    public void set(String tenantId, String name, String handler, String param, Integer maxRetryCount, Long nextExecuteTime, String status) {
        RetryTaskPO retryTaskPO = new RetryTaskPO();
        retryTaskPO.setTenantId(tenantId);
        retryTaskPO.setHandler(handler);
        retryTaskPO.setName(name);
        retryTaskPO.setRetryCount(0);
        retryTaskPO.setDeleted(false);
        retryTaskPO.setMaxRetryCount(maxRetryCount);
        retryTaskPO.setParams(param);
        retryTaskPO.setStatus(status);
        retryTaskPO.setNextExecuteTime(nextExecuteTime);
        retryTaskDAO.save(retryTaskPO);
    }

    public void setNewStatus(String id, String status) {
        if (RetryTaskStatusEnum.FAIL.code().equals(status)) {
            RetryTaskPO retryTaskPO = retryTaskDAO.get(id);
            int retryIndex = retryTaskPO.getRetryCount();
            if (retryIndex >= retryTaskPO.getMaxRetryCount()) {
                retryTaskDAO.updateStatus(id, RetryTaskStatusEnum.EXCEPTION.code());
                return;
            }
            long nextExecuteTime = System.currentTimeMillis() + TIME_INTERNAL[Math.min(retryIndex, TIME_INTERNAL.length - 1)] * 1000;
            retryTaskDAO.update(id, status, nextExecuteTime);
        } else {
            retryTaskDAO.updateStatus(id, status);
        }
    }

    protected String getTenantId(String tenantId) {
        if ("fs-crm-fmcg-service-gray".equals(APP_NAME) || "foneshare-gray".equals(PROFILES) || "jacoco".equals(PROFILES)) {
            return tenantId;
        }
        return null;
    }
}
