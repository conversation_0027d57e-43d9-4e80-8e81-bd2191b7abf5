package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.plugin.DealerActivityCostEnterAccount;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostRebateService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.TriggerAction;
import com.facishare.crm.fmcg.tpm.web.service.DealerActivityCostEnterAccountService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IDealerActivityCostEnterAccountService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.GetConfigValueByKey;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DealerActivityCostRebateService extends BaseService implements IDealerActivityCostRebateService {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private IDealerActivityCostEnterAccountService dealerActivityCostEnterAccountService;
    @Resource
    private TPMTriggerActionService triggerActionService;
    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    private IDealerActivityCostRebateService dealerActivityCostRebateService;

    @Override
    public IObjectData initRebate(DealerActivityCostEnterAccount.Arg afterArg) {
        String fundAccountId = afterArg.getDbMasterData().get(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID, String.class);
        if (StringUtils.isEmpty(fundAccountId)) {
            return null;
        }
        boolean openRebate = dealerActivityCostRebateService.isOpenRebate(afterArg.getUser().getTenantId());
        if (!openRebate) {
            return null;
        }
        if (!isRebateAccount(afterArg.getUser().getTenantId(), fundAccountId)) {
            return null;
        }
        if (!Boolean.TRUE.equals(afterArg.getDbMasterData().get(TPMDealerActivityCostFields.ENTER_ACCOUNT, Boolean.class))) {
            log.info("未开启核销单入账");
            return null;
        }
        IObjectData rebate = convertRebate(afterArg);
        log.info("rebate:{}", JSON.toJSONString(rebate));
        TriggerAction.Arg pricePolicyLimitAccountArg = TriggerAction.Arg.builder()
                .actionName("Add")
                .apiName(ApiNames.REBATE_OBJ)
                .objectData(rebate)
                .user(User.systemUser(afterArg.getUser().getTenantId()))
                .triggerFlow(false)
                .triggerWorkflow(false)
                .build();

        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(pricePolicyLimitAccountArg);
        return result.getObjectData().toObjectData();
    }

    @Override
    public boolean isOpenRebate(String tenantId) {
        GetConfigValueByKey.Arg getConfigArg = new GetConfigValueByKey.Arg();
        getConfigArg.setKey("rebate");
        GetConfigValueByKey.Result configValueByKey = paasDataProxy.getConfigValueByKey(Integer.parseInt(tenantId), -10000, getConfigArg);
        if (configValueByKey.getCode() != 0) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_DEALER_ACTIVITY_COST_REBATE_SERVICE_0));
        }
        return Objects.equals(configValueByKey.getData().getValue(), "1");
    }

    private IObjectData convertRebate(DealerActivityCostEnterAccount.Arg afterArg) {
        IObjectData rebate = new ObjectData();
        try {
            IObjectData dealerActivityCostObj = afterArg.getDbMasterData();
            String activityId = dealerActivityCostObj.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
            String dealerId = dealerActivityCostObj.get(TPMDealerActivityCostFields.DEALER_ID, String.class);
            String cashType = dealerActivityCostObj.get(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, String.class);
            String goodsPayUsage = dealerActivityCostObj.get(TPMDealerActivityCostFields.GOODS_PAY_USAGE, String.class);
            String cashUsage = dealerActivityCostObj.get(TPMDealerActivityCostFields.CASH_USAGE, String.class);
            String fundAccountId = dealerActivityCostObj.get(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID, String.class);
            Long effectiveDate = dealerActivityCostObj.get(TPMDealerActivityCostFields.EFFECTIVE_DATE, Long.class);
            Long expiringDate = dealerActivityCostObj.get(TPMDealerActivityCostFields.EXPIRING_DATE, Long.class);

            rebate.setTenantId(afterArg.getUser().getTenantId());
            rebate.setDescribeApiName(ApiNames.REBATE_OBJ);
            rebate.set(CommonFields.CREATE_BY, dealerActivityCostObj.get(CommonFields.CREATE_BY));
            rebate.set(CommonFields.OWNER, dealerActivityCostObj.get(CommonFields.OWNER));
            rebate.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);
            rebate.set(RebateFields.TOPIC, I18N.text(I18NKeys.DEALER_COST_REBATE));
            rebate.set(RebateFields.TPM_ACTIVITY_ID, activityId);
            rebate.set(RebateFields.TPM_DEALER_ACTIVITY_COST_ID, dealerActivityCostObj.getId());
            rebate.set(RebateFields.ACCOUNT_ID, dealerId);
            String rebateType = translateRebateType(cashType);
            rebate.set(RebateFields.REBATE_TYPE, rebateType);
            if (TPMActivityCashingProductFields.GOODS.equals(cashType) && !StringUtils.isEmpty(goodsPayUsage)) {
                rebate.set(RebateFields.USE_TYPE, goodsPayUsage);
            } else if (TPMActivityCashingProductFields.CASH.equals(cashType) && !StringUtils.isEmpty(cashUsage)) {
                rebate.set(RebateFields.USE_TYPE, cashUsage);
            } else {
                //默认值
                if (TPMActivityCashingProductFields.GOODS.equals(cashType)) {
                    rebate.set(RebateFields.USE_TYPE, TPMDealerActivityCostFields.GOODS_PAY_USAGE__QUANTITY);
                } else {
                    rebate.set(RebateFields.USE_TYPE, TPMDealerActivityCostFields.CASH_USAGE__DISCOUNT);
                }
            }


            IObjectData fundAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(afterArg.getUser().getTenantId()), fundAccountId, ApiNames.FUND_ACCOUNT_OBJ);
            if (fundAccount == null) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_REBATE_SERVICE_0), fundAccountId));
            }
            rebate.set(RebateFields.START_DATE, effectiveDate);
            rebate.set(RebateFields.END_DATE, expiringDate);

            rebate.set(RebateFields.SUM_AMOUNT, translateSumAmount(fundAccount, dealerActivityCostObj, afterArg.getCashingProductDetails()));
            rebate.set(RebateFields.ACTIVE_STATUS, RebateFields.ACTIVE_STATUS__ENABLE);

            List<IObjectData> cashingProductIdsByDealerCostId = getCashingProductIdsByDealerCostId(afterArg.getUser().getTenantId(), dealerActivityCostObj.getId());
            rebate.set(RebateFields.PRODUCT_CONDITION_TYPE, "ALL");
            if (CollectionUtils.isEmpty(cashingProductIdsByDealerCostId)) {
                rebate.set(RebateFields.PRODUCT_RANGE_TYPE, "ALL");
            } else {
                if (RebateFields.REBATE_TYPE__PRODUCT.equals(rebateType)) {
                    rebate.set(RebateFields.PRODUCT_RANGE_TYPE, "FIXED");
                    rebate.set(RebateFields.PRODUCT_RANGE, build(afterArg.getUser().getTenantId(), cashingProductIdsByDealerCostId, RebateFields.REBATE_TYPE__PRODUCT));
                } else {
                    rebate.set(RebateFields.PRODUCT_RANGE_TYPE, "FIXED");
                    rebate.set(RebateFields.PRODUCT_CONDITION_CONTENT, build(afterArg.getUser().getTenantId(), cashingProductIdsByDealerCostId, RebateFields.REBATE_TYPE__MONEY));
                }

            }
        } catch (Exception ex) {
            log.error("convert rebate error,", ex);
            throw ex;
        }
        return rebate;
    }

    private RebateProductRangeContent build(String tenantId, List<IObjectData> cashingProductIdsByDealerCostId, String rebateType) {
        List<String> productIds = cashingProductIdsByDealerCostId.stream().map(data -> data.get(TPMDealerActivityCashingProductFields.PRODUCT_ID, String.class)).collect(Collectors.toList());
        List<IObjectData> products = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, productIds, ApiNames.PRODUCT_OBJ);
        RebateProductRangeContent rebateProductRangeContent = new RebateProductRangeContent();
        rebateProductRangeContent.setObjectApiName(ApiNames.SALES_ORDER_PRODUCT_OBJ);

        List<RebateProductRangeContentData> data = Lists.newArrayList();
        for (IObjectData product : products) {
            RebateProductRangeContentData rebateProductRangeContentData = new RebateProductRangeContentData();
            rebateProductRangeContentData.setProductId(product.getId());
            rebateProductRangeContentData.setIsDeleted(false);
            rebateProductRangeContentData.setIsMultipleUnit(product.get("is_multiple_unit", Boolean.class));
            rebateProductRangeContentData.setProductIdR(product.getName());
            rebateProductRangeContentData.setProductIdS(product.getName());
            if (rebateType.equals(RebateFields.REBATE_TYPE__PRODUCT)) {
                String unitValue = product.get("unit", String.class);
                String unitLabel = "";
                IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.PRODUCT_OBJ);
                SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) describe.getFieldDescribe("unit");
                if (selectOneFieldDescribe != null) {
                    for (ISelectOption selectOption : selectOneFieldDescribe.getSelectOptions()) {
                        if (Objects.equals(selectOption.getValue(), unitValue)) {
                            unitLabel = selectOption.getLabel();
                            break;
                        }
                    }
                }

                rebateProductRangeContentData.setUnitId(unitValue);
                rebateProductRangeContentData.setUnit(unitLabel);
            }
            data.add(rebateProductRangeContentData);
        }
        rebateProductRangeContent.setData(data);
        return rebateProductRangeContent;
    }

    private String translateRebateType(String cashType) {
        switch (cashType) {
            case TPMActivityCashingProductFields.CASH: {
                return RebateFields.REBATE_TYPE__MONEY;

            }
            case TPMActivityCashingProductFields.GOODS: {
                return RebateFields.REBATE_TYPE__PRODUCT;
            }
            default:
                return RebateFields.REBATE_TYPE__MONEY;
        }
    }

    private String translateSumAmount(IObjectData fundAccount, IObjectData dealerActivityCostObj, List<IObjectData> details) {
        String accountType = fundAccount.get(FundAccountFields.ACCOUNT_TYPE, String.class);
        String accessModule = fundAccount.get(FundAccountFields.ACCESS_MODULE, String.class);
        String amount = dealerActivityCostObj.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, String.class);
        if (StringUtils.isEmpty(accountType)) {
            accountType = FundAccountFields.ACCOUNT_TYPE__AMOUNT;
        }
        if (Objects.isNull(accessModule) || Objects.equals(accessModule, FundAccountFields.ACCESS_MODULE_DEFAULT)) {
            return amount;
        }
        switch (accountType) {
            case FundAccountFields.ACCOUNT_TYPE__AMOUNT:
            case FundAccountFields.ACCOUNT_TYPE__GOODS_AMOUNT: {
                return amount;
            }
            case FundAccountFields.ACCOUNT_TYPE__GOODS_NUMBER: {
                return calculateCashingProductTotalNumber(details);
            }
            default:
                throw new ValidateException(String.format(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_REBATE_SERVICE_1), accountType));
        }
    }

    private String calculateCashingProductTotalNumber(List<IObjectData> details) {
        int total = 0;
        for (IObjectData detail : details) {
            Integer number = detail.get(TPMDealerActivityCostFields.COST_CASHING_QUANTITY, Integer.class);
            if (number != null) {
                total += number;
            }
        }
        return Integer.toString(total);
    }


    @Override
    public void syncFundAccountToRebate(User user, String fundAccountId) {
        String tenantId = user.getTenantId();
        IObjectData fAccountAuthorizations = getFAccountAuthorizations(tenantId);
        if (fAccountAuthorizations == null) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_DEALER_ACTIVITY_COST_REBATE_SERVICE_1));
        }
        String id = fAccountAuthorizations.getId();
        List<IObjectData> details = authorizationDetails(tenantId, id);

        if (details.stream().anyMatch(detail -> fundAccountId.equals(detail.get(AuthorizationDetailFields.AUTHORIZE_ACCOUNT_ID, String.class)))) {
            return;
        }

        serviceFacade.saveObjectData(user, buildDetail(tenantId, fundAccountId, id));
    }


    @Override
    public void invalidRebateObjData(RequestContext requestContext, List<String> costIds) {
        RequestContext adminRequest = RequestContext.builder().user(User.systemUser(requestContext.getTenantId())).tenantId(requestContext.getTenantId()).ea(requestContext.getEa())
                .appId(requestContext.getAppId()).requestSource(requestContext.getRequestSource()).build();
        List<String> rebateIds;
        try {
            rebateIds = getRebateDataIdsByDealerCostIds(requestContext.getTenantId(), costIds).stream().map(DBRecord::getId).collect(Collectors.toList());
        } catch (ObjectDefNotFoundError ex) {
            return;
        }

        if (CollectionUtils.isEmpty(rebateIds)) {
            return;
        }

        ActionContext invalidActionContext = new ActionContext(adminRequest, ApiNames.REBATE_OBJ, "BulkInvalid");
        invalidActionContext.setAttribute("triggerWorkflow", false);
        invalidActionContext.setAttribute("triggerFlow", false);
        invalidActionContext.setAttribute("origin", DealerActivityCostEnterAccountService.ENTER_ACCOUNT_PEER_NAME);
        StandardBulkInvalidAction.Arg arg = new StandardBulkInvalidAction.Arg();
        arg.setJson(buildInvalidJsonArg(requestContext.getTenantId(), rebateIds));
        arg.putExtraData(TPMTriggerActionService.REQUEST_FROM, TPMTriggerActionService.REQUEST_APP_NAME);

        try {
            serviceFacade.triggerAction(invalidActionContext, arg, StandardBulkInvalidAction.Result.class);
        } catch (Exception ex) {
            log.error("作废返利单失败：{},", JSON.toJSONString(rebateIds), ex);
        }
    }

    private List<IObjectData> getRebateDataIdsByDealerCostIds(String tenantId, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(RebateFields.TPM_DEALER_ACTIVITY_COST_ID);
        masterFilter.setOperator(Operator.IN);
        masterFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(masterFilter));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.REBATE_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID
                ));
    }

    private List<IObjectData> getCashingProductIdsByDealerCostId(String tenantId, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMDealerActivityCashingProductFields.DEALER_ACTIVITY_COST_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(masterFilter));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.TPM_DEALER_ACTIVITY_CASHING_PRODUCT_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID,
                        TPMDealerActivityCashingProductFields.PRODUCT_ID
                ));
    }

    private String buildInvalidJsonArg(String tenantId, List<String> ids) {
        List<InvalidData> invalidData = ids.stream().map(id -> buildInvalidSingleArg(tenantId, id)).collect(Collectors.toList());
        InvalidArg invalidArg = new InvalidArg();
        invalidArg.setDataList(invalidData);
        return JSON.toJSONString(invalidArg);
    }

    private static InvalidData buildInvalidSingleArg(String tenantId, String id) {
        InvalidData invalidData = new InvalidData();
        invalidData.setObjectDescribeApiName(ApiNames.REBATE_OBJ);
        invalidData.setTenantId(tenantId);
        invalidData.setId(id);
        return invalidData;

    }

    private IObjectData buildDetail(String tenantId, String accountId, String masterId) {
        IObjectData detail = new ObjectData();
        detail.set(CommonFields.TENANT_ID, tenantId);
        detail.set(CommonFields.OWNER, Lists.newArrayList("-10000"));
        detail.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);
        detail.set(AuthorizationDetailFields.AUTHORIZE_ACCOUNT_ID, accountId);
        detail.set(AuthorizationDetailFields.IS_DEFAULT_ENTRY_ACCOUNT, false);
        detail.set(CommonFields.LIFE_STATUS, CommonFields.LIFE_STATUS__NORMAL);
        detail.set(CommonFields.LOCK_STATUS, 0);
        detail.set(CommonFields.OBJECT_DESCRIBE_API_NAME, ApiNames.AUTHORIZATION_DETAIL_OBJ);
        detail.set(CommonFields.RECORD_TYPE, CommonFields.RECORD_TYPE__DEFAULT);
        detail.set(AuthorizationDetailFields.STATUS, AuthorizationDetailFields.STATUS__ON);
        detail.set(AuthorizationDetailFields.F_ACCOUNT_AUTHORIZATION_ID, masterId);
        return detail;
    }

    private List<IObjectData> authorizationDetails(String tenantId, String masterId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(AuthorizationDetailFields.MASTER_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(masterId));

        query.setFilters(Lists.newArrayList(masterFilter));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.AUTHORIZATION_DETAIL_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID,
                        AuthorizationDetailFields.AUTHORIZE_ACCOUNT_ID
                ));
    }

    private IObjectData getFAccountAuthorizations(String tenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(FAccountAuthorizationFields.AUTHORIZED_OBJECT_API_NAME);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(ApiNames.REBATE_OBJ));

        query.setFilters(Lists.newArrayList(masterFilter));
        List<IObjectData> master = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.F_ACCOUNT_AUTHORIZATION_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID
                ));
        if (master.size() == 0) {
            return null;
        }

        return master.get(0);
    }

    @Override
    public boolean isRebateAccount(String tenantId, String fundAccountId) {
        if (StringUtils.isEmpty(fundAccountId)) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_REBATE_SERVICE_2));
        }
        IObjectData fundAccount =
                serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), fundAccountId, ApiNames.FUND_ACCOUNT_OBJ);
        if (fundAccount != null) {
            String accessModule = fundAccount.get(FundAccountFields.ACCESS_MODULE, String.class);
            return Objects.equals(accessModule, FundAccountFields.ACCESS_MODULE_BACK);

        }
        return false;
    }

    @Data
    @ToString
    static class InvalidArg {
        private List<InvalidData> dataList;
    }

    @Data
    @ToString
    static class InvalidData {
        @SerializedName("object_describe_api_name")
        @JSONField(name = "object_describe_api_name")
        @JsonProperty("object_describe_api_name")
        private String objectDescribeApiName;
        @SerializedName("tenant_id")
        @JSONField(name = "tenant_id")
        @JsonProperty("tenant_id")
        private String tenantId;
        @SerializedName("_id")
        @JSONField(name = "_id")
        @JsonProperty("_id")
        private String id;
    }

    @Data
    @ToString
    static class RebateProductRangeContent {
        @SerializedName("object_api_name")
        @JSONField(name = "object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;

        private List<RebateProductRangeContentData> data;
    }

    @Data
    @ToString
    static class RebateProductRangeContentData {
        @SerializedName("is_deleted")
        @JSONField(name = "is_deleted")
        @JsonProperty("is_deleted")
        private Boolean isDeleted;

        @SerializedName("is_multiple_unit")
        @JSONField(name = "is_multiple_unit")
        @JsonProperty("is_multiple_unit")
        private Boolean isMultipleUnit;
        @SerializedName("product_id")
        @JSONField(name = "product_id")
        @JsonProperty("product_id")
        private String productId;
        @SerializedName("product_id__r")
        @JSONField(name = "product_id__r")
        @JsonProperty("product_id__r")
        private String productIdR;
        @SerializedName("product_id__s")
        @JSONField(name = "product_id__s")
        @JsonProperty("product_id__s")
        private String productIdS;
        @SerializedName("unit_id")
        @JSONField(name = "unit_id")
        @JsonProperty("unit_id")
        private String unitId;
        @SerializedName("unit__s")
        @JSONField(name = "unit__s")
        @JsonProperty("unit__s")
        private String unit;
    }

}
