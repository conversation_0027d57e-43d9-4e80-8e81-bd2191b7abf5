package com.facishare.crm.fmcg.tpm.web.poc;


import com.facishare.crm.fmcg.common.apiname.*;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.tpm.business.abstraction.IPOCTriggerActionService;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.TriggerAction;
import com.facishare.crm.fmcg.tpm.web.contract.poc.POCActivityProofAudit;
import com.facishare.crm.fmcg.tpm.web.poc.abstraction.AbstractPOCHandler;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@SuppressWarnings({"unchecked", "Duplicates"})
public class ActivityProofAuditPOCHandler extends AbstractPOCHandler<POCActivityProofAudit.Arg, POCActivityProofAudit.Result> {


    @Resource
    private IPOCTriggerActionService pocTriggerActionService;
    @Resource
    private ProofImgHandler proofImgHandler;

    @Override
    protected void before(POCActivityProofAudit.Arg arg) {

        IObjectData objectData = initActivityProofAudit(arg);


        List<IObjectData> details = initActivityProofAuditDetail(arg);

        BigDecimal total = new BigDecimal("0");
        for (IObjectData detail : details) {
            total = total.add(detail.get(TPMActivityProofAuditDetailFields.SUBTOTAL, BigDecimal.class));
        }

        objectData.set(TPMActivityProofAuditFields.TOTAL, "0");
        objectData.set(TPMActivityProofAuditFields.AUDIT_TOTAL, "0");

        if (TPMActivityProofAuditFields.AUDIT_STATUS__PASS.equals(objectData.get(TPMActivityProofAuditFields.AUDIT_STATUS, String.class))) {
            objectData.set(TPMActivityProofAuditFields.TOTAL, String.valueOf(total));
            objectData.set(TPMActivityProofAuditFields.AUDIT_TOTAL, String.valueOf(total));
        }


        arg.setMasterObj(objectData);
        arg.setDetails(details);
        super.before(arg);
    }


    @Override
    protected POCActivityProofAudit.Result doAct(POCActivityProofAudit.Arg arg, POCActivityProofAudit.Result result) {
        ApiContext context = ApiContextManager.getContext();


        BaseObjectSaveAction.Result createResult = pocTriggerActionService.triggerAction(TriggerAction.Arg.builder().user(User.systemUser(context.getTenantId())).objectData(arg.getMasterObj()).details(arg.getDetails()).apiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ)
                .detailApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ).actionName("Add").triggerFlow(false).triggerFlow(false).build());
        result.setMasterObj(createResult.getObjectData().toObjectData());
        result.setDetails(createResult.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()));
        return result;
    }

    @Override
    protected POCActivityProofAudit.Result after(POCActivityProofAudit.Arg arg, POCActivityProofAudit.Result result) {

        return super.after(arg, result);
    }

    private IObjectData initActivityProofAudit(POCActivityProofAudit.Arg arg) {
        IObjectData proof = arg.getProof();
        IObjectData objectData = super.preInitObj();

        objectData.set(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID, proof.get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID));
        objectData.set(TPMActivityProofAuditFields.ACTIVITY_ID, proof.get(TPMActivityProofFields.ACTIVITY_ID));
        objectData.set(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, proof.getId());
        objectData.set(TPMActivityProofAuditFields.AUDIT_STATUS, TPMActivityProofAuditFields.AUDIT_STATUS__PASS);
        Object img = proof.get(TPMActivityProofFields.PROOF_IMAGES);
        if (img != null) {
            List<Map<String, Object>> imgCast = (List<Map<String, Object>>) img;
            if (!CollectionUtils.isEmpty(imgCast)) {
                Map<String, Object> imgMap = imgCast.get(0);
                if (proofImgHandler.containsDimImg((String) imgMap.get("path"))) {
                    objectData.set(TPMActivityProofAuditFields.AUDIT_STATUS, TPMActivityProofAuditFields.AUDIT_STATUS__REJECT);
                }
            }
        }
        objectData.set(TPMActivityProofAuditFields.DEALER_ID, proof.get(TPMActivityProofFields.DEALER_ID));
        objectData.set(CommonFields.OBJECT_DESCRIBE_API_NAME, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
        objectData.set(TPMActivityProofAuditFields.STORE_ID, proof.get(TPMActivityProofFields.STORE_ID));
        objectData.set(CommonFields.OWNER, proof.get(CommonFields.OWNER));
        objectData.set(TPMActivityProofAuditFields.AUDIT_TIME, proof.get(TPMActivityProofFields.REMARK));


        return objectData;
    }

    private List<IObjectData> initActivityProofAuditDetail(POCActivityProofAudit.Arg arg) {
        List<IObjectData> details = Lists.newArrayList();
        for (IObjectData proofDetail : arg.getProofDetails()) {
            IObjectData objectData = super.preInitObj();

            objectData.set(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_DETAIL_ID, proofDetail.getId());
            objectData.set(TPMActivityProofAuditDetailFields.AUDIT_AMOUNT, "1");
            objectData.set(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_ID, proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID));
            objectData.set(TPMActivityProofAuditDetailFields.SUBTOTAL, proofDetail.get(TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD));
            objectData.set(CommonFields.OBJECT_DESCRIBE_API_NAME, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ);
            objectData.set(CommonFields.OWNER, proofDetail.get(CommonFields.OWNER));
            details.add(objectData);
        }


        return details;
    }


}
