package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyNewDetailsFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetDisassemblyService;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class TPMBudgetDisassemblyNewDetailObjUnionInsertImportDataAction extends StandardUnionInsertImportDataAction {

    private final IBudgetDisassemblyService budgetDisassemblyService = SpringUtil.getContext().getBean(IBudgetDisassemblyService.class);
    private final Map<String, IObjectData> masterDataMap = Maps.newHashMap();

    private final Map<String, List<IObjectData>> newDetailsGroupByMasterId = Maps.newHashMap();
    private final Map<IObjectData, List<IObjectData>> ARG = Maps.newHashMap();

    private static final String PK_ID = "_PK_ID";

    @Override
    protected void before(Arg arg) {
        log.info("import TPMBudgetDisassemblyNewDetail arg:{}", arg);
        super.before(arg);
        this.dataList.forEach(data -> {
            data.getData().set(PK_ID, data.getRowNo());
        });
    }

    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);

        List<ImportError> errorList = new ArrayList<>();
        Set<String> exceptionMasterIds = Sets.newHashSet();

        List<String> masterDataIds = dataList.stream().map(v -> v.getData().get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DISASSEMBLY_ID, String.class)).distinct().collect(Collectors.toList());
        for (ImportData newDetailImportData : dataList) {
            String masterDataId = newDetailImportData.getData().get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DISASSEMBLY_ID, String.class);
            IObjectData masterData = masterDataMap.get(masterDataId);
            if (masterData == null) {
                IObjectData masterDataFromDb = budgetDisassemblyService.findData(actionContext.getTenantId(), masterDataId, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
                if (masterDataFromDb == null) {
                    exceptionMasterIds.add(masterDataId);
                    ImportError error = new ImportError();
                    error.setRowNo(newDetailImportData.getRowNo());
                    error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ_UNION_INSERT_IMPORT_DATA_ACTION_0));
                    errorList.add(error);
                    continue;
                }
                masterDataMap.put(masterDataId, masterDataFromDb);
            }

            if (newDetailsGroupByMasterId.get(masterDataId) == null) {
                newDetailsGroupByMasterId.put(masterDataId, Lists.newArrayList(newDetailImportData.getData()));
            } else {
                newDetailsGroupByMasterId.get(masterDataId).add(newDetailImportData.getData());
            }
        }

        for (String masterDataId : masterDataIds) {
            if (exceptionMasterIds.contains(masterDataId)) {
                continue;
            }
            ARG.put(masterDataMap.get(masterDataId), newDetailsGroupByMasterId.get(masterDataId));
        }
        mergeErrorList(errorList);
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        log.info("import data custom:{}", JSON.toJSONString(validList));
        List<IObjectData> validateData = new ArrayList<>();
        List<ImportError> errorList = new ArrayList<>();


        for (Map.Entry<IObjectData, List<IObjectData>> arg : ARG.entrySet()) {
            IObjectData masterData = arg.getKey();
            List<IObjectData> newDetailData = arg.getValue();
            BigDecimal disassemblyAmount = new BigDecimal("0");

            String sourceBudgetType = masterData.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
            String targetBudgetNodeId = masterData.get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID, String.class);


            for (IObjectData newDetailDatum : newDetailData) {

                newDetailDatum.set(TPMBudgetDisassemblyNewDetailsFields.BUDGET_TYPE_ID, sourceBudgetType);
                newDetailDatum.set(TPMBudgetDisassemblyNewDetailsFields.BUDGET_NODE_ID, targetBudgetNodeId);

                correctDetailFieldValue(newDetailDatum);

                BigDecimal cur = new BigDecimal(newDetailDatum.get(TPMBudgetDisassemblyNewDetailsFields.AMOUNT, String.class));
                disassemblyAmount = disassemblyAmount.add(cur);
            }

            masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_AMOUNT, disassemblyAmount);
        }

        for (Map.Entry<IObjectData, List<IObjectData>> actionArg : ARG.entrySet()) {
            List<Integer> rowList = actionArg.getValue().stream().map(v -> v.get(PK_ID, Integer.class)).collect(Collectors.toList());

            ActionContext addActionContext = new ActionContext(actionContext.getRequestContext(), ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, "Edit");
            addActionContext.setAttribute("triggerWorkflow", arg.getIsWorkFlowEnabled());
            addActionContext.setAttribute("triggerFlow", arg.getIsApprovalFlowEnabled());
            addActionContext.setAttribute("isFromImport", "true");
            BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
            saveArg.setObjectData(ObjectDataDocument.of(actionArg.getKey()));
            Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();

            details.put(ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ, actionArg.getValue().stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
            saveArg.setDetails(details);
            try {
                BaseObjectSaveAction.Result result = serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
                validateData.add(result.getObjectData().toObjectData());

            } catch (Exception ex) {
                if (ex instanceof AppBusinessException) {
                    log.info("TPMBudgetDisassemblyNewDetailObjUnionInsertImportDataAction trigger TPMBudgetDisassemblyObjEditAction throws exception.", ex);
                } else {
                    log.error("TPMBudgetDisassemblyNewDetailObjUnionInsertImportDataAction trigger TPMBudgetDisassemblyObjEditAction throws exception.", ex);
                }
                for (Integer row : rowList) {
                    log.info("row:{}", row);
                    buildErrorList(errorList, ex.getMessage(), row);
                }

            }


        }

        mergeErrorList(errorList);
        actionContext.setAttribute("triggerWorkflow", false);
        actionContext.setAttribute("triggerFlow", false);
        return validateData;
    }

    private void buildErrorList(List<ImportError> errorList, String msg, int rowNo) {
        ImportError error = new ImportError();
        error.setRowNo(rowNo);
        error.setErrorMessage(msg);
        errorList.add(error);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }


    private void correctDetailFieldValue(IObjectData newDetail) {
        newDetail.set(TPMBudgetDisassemblyNewDetailsFields.TAKE_APART_IN_AMOUNT, newDetail.get(TPMBudgetDisassemblyNewDetailsFields.AMOUNT));

    }


}
