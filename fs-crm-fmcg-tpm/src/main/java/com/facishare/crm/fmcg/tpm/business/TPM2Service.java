package com.facishare.crm.fmcg.tpm.business;

import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.contract.fmcgservice.GetLicense;
import com.fmcg.framework.http.contract.fmcgservice.QueryAll;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Sets;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TPM2Service implements ITPM2Service {

    @Resource
    private FmcgServiceProxy fmcgServiceProxy;

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;

    @Resource
    private IActivityTypeManager activityTypeManager;

    @Resource
    private LicenseService licenseService;

    @Resource
    private LicenseClient licenseClient;


    private static final String TPM_LICENSE_CODE_REDIS_KEY = "TPM_LICENSE_EXIST_%s:%s";
    private static final String TPM_2_LICENSE_CODE = "trade_promotion_management_app";
    private static final String TPM_2_APP_CODE = "FMCG.TPM.2";
    private static final String TPM_CODE_LICENSE_CODE = "code_marketing_campaign_app";
    private static final String TPM_CODE_APP_CODE = "FMCG.TPM_CODE";
    private static final String FMCG_AI_PRODUCT_RECOGNITION_APP = "fmcg_ai_product_recognition_app";

    @Override
    public boolean isTPM2Tenant(Integer tenantId) {
        return true;
    }

    @Override
    public List<String> queryTPMTenant() {
        QueryAll.Arg arg = new QueryAll.Arg();
        arg.setAppCodes(Lists.newArrayList("FMCG.TPM.2", "FMCG.TPM"));
        QueryAll.Result result = fmcgServiceProxy.queryAllTenant(0, -10000, arg);
        if (CollectionUtils.isNotEmpty(result.getLicenses())) {
            return result.getLicenses().stream().map(licenseDto -> String.valueOf(licenseDto.getTenantId())).distinct().collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<String> queryBudgetTenant() {
        QueryAll.Arg arg = new QueryAll.Arg();
        arg.setAppCodes(Lists.newArrayList("FMCG.TPM_BUDGET_ACCOUNT"));
        QueryAll.Result result = fmcgServiceProxy.queryAllTenant(0, -10000, arg);
        if (CollectionUtils.isNotEmpty(result.getLicenses())) {
            return result.getLicenses().stream().map(licenseDto -> String.valueOf(licenseDto.getTenantId())).distinct().collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public boolean isNeedAgreement(Integer tenantId, IObjectData activity) {
        return isTPM2Tenant(tenantId) ? activityTypeManager.find(tenantId.toString(), activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class, "")).agreementNode() != null : Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AGREEMENT_REQUIRED));
    }

    @Override
    public boolean existTPMLicenseTenant(Integer tenantId) {
        return existLicenseTenant(tenantId, TPM_2_APP_CODE, TPM_2_LICENSE_CODE);
    }

    @Override
    public boolean existTPMLicenseTenantV2(Integer tenantId) {
        return existLicenseTenantV2(tenantId, TPM_2_APP_CODE, TPM_2_LICENSE_CODE);
    }

    private boolean existLicenseTenantV2(Integer tenantId, String appCode, String licenseCode) {
        String key = String.format(TPM_LICENSE_CODE_REDIS_KEY, licenseCode, tenantId);

        try {
            String value = validationLicenseIfExist(tenantId, appCode);
            if (Strings.isNotEmpty(value)) {
                boolean existed = existProductModule(tenantId, licenseCode);
                return Boolean.TRUE.equals(Boolean.valueOf(value)) && existed;
            }
        } catch (Exception e) {
            log.info("existTPMLicenseTenant err ,tenantId = {}", tenantId, e);
            return false;
        }
        return false;
    }

    private boolean existLicenseTenant(Integer tenantId, String appCode, String licenseCode) {
        //String key = String.format(TPM_LICENSE_CODE_REDIS_KEY, licenseCode, tenantId);

        try {
            String value = validationLicenseIfExist(tenantId, appCode);
            if (Strings.isNotEmpty(value)) {
                /* if (!existLicenseCode) {
                    redisCmd.set(key, value, SetParams.setParams().nx().ex(60L));
                    return existLicenseCode;
                }
                Map<String, Boolean> existModule = licenseService.existModule(String.valueOf(tenantId), Sets.newSet(licenseCode));
                Boolean existTPM = existModule.getOrDefault(licenseCode, Boolean.FALSE);
                redisCmd.set(key, existTPM.toString(), SetParams.setParams().nx().ex(60L));*/
                return Boolean.TRUE.equals(Boolean.valueOf(value));
            }
        } catch (Exception e) {
            log.info("existTPMLicenseTenant err ,tenantId = {}", tenantId, e);
            return false;
        }
        return false;
    }

    @NotNull
    private String validationLicenseIfExist(Integer tenantId, String appCode) {
        String value;
        GetLicense.Arg arg = new GetLicense.Arg();
        arg.setAppCode(appCode);
        GetLicense.Result result = fmcgServiceProxy.getLicense(tenantId, -10000, arg);
        value = String.valueOf(result.getLicense() != null);
        return value;
    }

    @Override
    public boolean existTPMCodeLicenseTenant(Integer tenantId) {
        String value = validationLicenseIfExist(tenantId, TPM_CODE_APP_CODE);
        boolean existLicenseCode = Boolean.TRUE.equals(Boolean.valueOf(value));
        if (existLicenseCode) {
            return true;
        }
        return existLicenseTenant(tenantId, TPM_CODE_APP_CODE, TPM_CODE_LICENSE_CODE);
    }

    @Override
    public boolean existTPMLicenseCode(Integer tenantId, String licenseCode) {
        String value = validationLicenseIfExist(tenantId, licenseCode);
        return Boolean.TRUE.equals(Boolean.valueOf(value));
    }

    @Override
    public boolean existSfaCarPreSaleLicenseCode(Integer tenantId, String licenseCode) {
        return existProductModule(tenantId, licenseCode);
    }

    @Override
    public boolean existFmcgAiProductRecognitionAppLicenseCode(Integer tenantId) {
        return existProductModule(tenantId, FMCG_AI_PRODUCT_RECOGNITION_APP);
    }

    public boolean existProductModule(Integer tenantId, String licenseCode) {
        String key = String.format(TPM_LICENSE_CODE_REDIS_KEY, licenseCode, tenantId);

        String existLicenseCode = redisCmd.get(key);
        if (Boolean.valueOf(existLicenseCode).equals(Boolean.TRUE)) {
            return true;
        }

        try {
            Set<String> versionList = getVersionList(tenantId);
            Set<String> moduleList = getModuleList(tenantId);
            if (versionList.contains(licenseCode) || moduleList.contains(licenseCode)) {
                redisCmd.set(key, "true", SetParams.setParams().nx().ex(60L));
                return true;
            }
        } catch (Exception e) {
            log.info("existProductModule err ,tenantId = {}", tenantId, e);
        }
        return false;
    }

    private Set<String> getVersionList(int tenantId) {
        List<ProductVersionPojo> versionPojoList;
        LicenseContext context = buildLicenseContext(tenantId);
        QueryProductArg arg = new QueryProductArg();
        arg.setLicenseContext(context);
        LicenseVersionResult licenseVersionResult = licenseClient.queryProductVersion(arg);
        if (!Objects.isNull(licenseVersionResult) && isSuccess(licenseVersionResult) && CollectionUtils.isNotEmpty(licenseVersionResult.getResult())) {
            versionPojoList = licenseVersionResult.getResult();
        } else {
            versionPojoList = Lists.newArrayList();
        }
        return versionPojoList.stream().map(ProductVersionPojo::getCurrentVersion).collect(Collectors.toSet());
    }

    private Set<String> getModuleList(int tenantId) {
        LicenseContext context = buildLicenseContext(tenantId);
        QueryModuleArg arg = new QueryModuleArg();
        arg.setLicenseContext(context);
        ModuleInfoResult moduleInfoResult = licenseClient.queryModule(arg);
        if (!Objects.isNull(moduleInfoResult) && isSuccess(moduleInfoResult) && CollectionUtils.isNotEmpty(moduleInfoResult.getResult())) {
            return moduleInfoResult.getResult().stream().map(ModuleInfoPojo::getModuleCode).collect(Collectors.toSet());
        } else {
            return Sets.newHashSet();
        }
    }

    private LicenseContext buildLicenseContext(long tenantId) {
        LicenseContext context = new LicenseContext();
        context.setTenantId(String.valueOf(tenantId));
        context.setUserId("-10000");
        context.setAppId("CRM");
        return context;
    }

    private boolean isSuccess(Result result) {
        return Objects.equals(result.getErrCode(), 0);
    }

}
