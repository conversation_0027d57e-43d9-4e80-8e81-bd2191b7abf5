package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityBudgetDetailFields;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
public class TPMActivityBudgetDetailObjBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getDataIds(), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ);
        List<String> budgetIds = dataList.stream().map(v -> v.get(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID, String.class)).collect(Collectors.toList());
        List<IObjectData> budgets = getBudgets(budgetIds);
        if(!CollectionUtils.isEmpty(budgetIds)){
            List<String> names = budgets.stream().map(IObjectData::getName).collect(Collectors.toList());
            throw  new ValidateException(I18N.text(I18NKeys.ACTIVITY_BUDGET_DETAIL_OBJ_BULK_INVALID_ACTION_0)+names);
        }
        super.before(arg);
    }

    private List<IObjectData> getBudgets(List<String> budgetIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(budgetIds);

        Filter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldName("is_deleted");
        isDeletedFilter.setOperator(Operator.EQ);
        isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, isDeletedFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_BUDGET, query);
    }
}
