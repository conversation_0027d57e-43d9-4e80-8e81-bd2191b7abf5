package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetRuleTypeNodeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ReleaseNodeVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/26 下午6:25
 */
@Data
@ToString
public class BudgetRuleTypeNodeEntity implements Serializable {

    public static final String F_TYPE = "type";
    public static final String F_TRIGGER_TIME = "trigger_time";
    public static final String F_WHERE_CONDITION = "whereCondition";
    public static final String F_RETURN_WHERE_CONDITION = "return_whereCondition";
    public static final String F_CONDITION_CODE = "condition_code";
    public static final String F_SOURCE_FIELD = "source_field";
    public static final String F_CONDITION_PATTERN = "condition_pattern";
    public static final String F_PREVIEW = "preview";
    public static final String F_REFERENCED_FIELD_API_NAME = "referenced_field_api_name";

    public static final String F_APPROVAL_TRIGGER_TIME = "approval_trigger_time";

    public static final String F_RELEASE_NODE = "f_release_node";

    //deduction - 扣减，freeze - 冻结
    @Property(F_TYPE)
    private String type;

    //触发时机
    @Property(F_TRIGGER_TIME)
    private String triggerTime;

    @Embedded(F_RELEASE_NODE)
    private List<ReleaseNodeEntity> releaseNode;

    @Property(F_APPROVAL_TRIGGER_TIME)
    private String approvalTriggerTime = ApprovalTriggerTime.AFTER.value();

    // 条件code
    @Property(F_CONDITION_CODE)
    private String conditionCode;

    //触发条件
    @Embedded(F_WHERE_CONDITION)
    private List<BudgetWhereConditionEntity> whereConditions;

    //条件回显字段 （前端）
    @Property(F_PREVIEW)
    private String previewString;

    //来源字段
    @Property(F_SOURCE_FIELD)
    private String sourceField;

    @Property(F_REFERENCED_FIELD_API_NAME)
    private String referencedFieldApiName;


    public static BudgetRuleTypeNodeEntity fromVO(BudgetRuleTypeNodeVO vo) {
        if (vo == null) {
            return null;
        }
        BudgetRuleTypeNodeEntity budgetRuleTypeNodeEntity = new BudgetRuleTypeNodeEntity();
        budgetRuleTypeNodeEntity.setType(vo.getType());
        budgetRuleTypeNodeEntity.setTriggerTime(vo.getTriggerTime());
        if (CollectionUtils.isEmpty(vo.getReleaseNode())){
            budgetRuleTypeNodeEntity.setReleaseNode(new ArrayList<>());
        }else {
            budgetRuleTypeNodeEntity.setReleaseNode(vo.getReleaseNode().stream().map(ReleaseNodeEntity::fromVO).collect(Collectors.toList()));
        }
        budgetRuleTypeNodeEntity.setConditionCode(vo.getConditionCode());
        if (CollectionUtils.isEmpty(vo.getWhereConditions())) {
            budgetRuleTypeNodeEntity.setWhereConditions(new ArrayList<>());
        } else {
            budgetRuleTypeNodeEntity.setWhereConditions(vo.getWhereConditions().stream().map(BudgetWhereConditionEntity::fromVO).collect(Collectors.toList()));
        }
        budgetRuleTypeNodeEntity.setPreviewString(vo.getPreviewString());
        budgetRuleTypeNodeEntity.setSourceField(vo.getSourceField());
        budgetRuleTypeNodeEntity.setApprovalTriggerTime(vo.getApprovalTriggerTime());
        budgetRuleTypeNodeEntity.setReferencedFieldApiName(vo.getReferencedFieldApiName());
        return budgetRuleTypeNodeEntity;
    }

    public static BudgetRuleTypeNodeVO toVO(BudgetRuleTypeNodeEntity entity) {
        if (entity == null) {
            return null;
        }
        BudgetRuleTypeNodeVO budgetRuleTypeNodeVO = new BudgetRuleTypeNodeVO();
        budgetRuleTypeNodeVO.setType(entity.getType());
        budgetRuleTypeNodeVO.setTriggerTime(entity.getTriggerTime());
        if (CollectionUtils.isEmpty(entity.getReleaseNode())){
            budgetRuleTypeNodeVO.setReleaseNode(new ArrayList<>());
        }else {
            budgetRuleTypeNodeVO.setReleaseNode(entity.getReleaseNode().stream().map(ReleaseNodeEntity::toVO).collect(Collectors.toList()));
        }
        budgetRuleTypeNodeVO.setConditionCode(entity.getConditionCode());
        if (CollectionUtils.isEmpty(entity.getWhereConditions())) {
            budgetRuleTypeNodeVO.setWhereConditions(new ArrayList<>());
        } else {
            budgetRuleTypeNodeVO.setWhereConditions(entity.getWhereConditions().stream().map(BudgetWhereConditionEntity::toVO).collect(Collectors.toList()));
        }
        budgetRuleTypeNodeVO.setPreviewString(entity.getPreviewString());
        budgetRuleTypeNodeVO.setSourceField(entity.getSourceField());
        budgetRuleTypeNodeVO.setApprovalTriggerTime(entity.getApprovalTriggerTime());
        budgetRuleTypeNodeVO.setReferencedFieldApiName(entity.getReferencedFieldApiName());
        return budgetRuleTypeNodeVO;
    }
}
