package com.facishare.crm.fmcg.tpm.api.rule;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2023/9/16 14:42
 */
public interface DeleteRewardRule {

    @Data
    @ToString
    @NoArgsConstructor
    @Builder
    @AllArgsConstructor
    class Arg implements Serializable {

        @JsonProperty(value = "tenant_id")
        @SerializedName("tenant_id")
        @JSONField(name = "tenant_id")
        private String tenantId;

        @JsonProperty(value = "unique_id")
        @SerializedName("unique_id")
        @JSONField(name = "unique_id")
        private String uniqueId;
    }


    @Data
    @ToString
    class Result implements Serializable{


    }
}
