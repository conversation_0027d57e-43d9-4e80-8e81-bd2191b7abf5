package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.business.dto.SimpleBudgetAccountDetailDTO;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.BudgetDetailOperateMark;
import com.facishare.crm.fmcg.tpm.business.enums.MainType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IBudgetAccountDetailService {

    boolean existBudgetDetail(String tenantId, String budgetAccountId);

    /**
     * add without 'budgetFreezeDetailId'
     */
    @MetadataTransactional
    IObjectData add(
            User user,
            String budgetAccountId,
            MainType mainType,
            BizType bizType,
            BudgetDetailOperateMark operateMark,
            BigDecimal amount,
            IObjectData relatedObj,
            String bizTraceId,
            String approvalId);


    @MetadataTransactional
    IObjectData add(
            User user,
            String budgetAccountId,
            MainType mainType,
            BizType bizType,
            BudgetDetailOperateMark operateMark,
            BigDecimal amount,
            IObjectData relatedObj,
            String bizTraceId,
            String approvalId,
            String budgetFreezeDetailId);

    /**
     * add without 'operateMark','budgetFreezeDetailId'
     */
    @MetadataTransactional
    IObjectData add(
            User user,
            String budgetAccountId,
            MainType mainType,
            BizType bizType,
            BigDecimal amount,
            IObjectData relatedObj,
            String bizTraceId,
            String approvalId);

    /**
     * add without 'approvalId','operateMark','budgetFreezeDetailId'
     */
    @MetadataTransactional
    IObjectData add(
            User user,
            String budgetAccountId,
            MainType mainType,
            BizType bizType,
            BigDecimal amount,
            IObjectData relatedObj,
            String bizTraceId);

    BizType getTransferDetailBusinessType(String recordType, boolean isTransferOutBudget, boolean needFreeze);

    boolean existsConsumeRuleDetailByRuleId(String tenantId, String ruleId);

    boolean existsConsumeRuleDetailByObjectData(String tenantId, String ruleId, String objectApiName, String dataId);

    List<IObjectData> queryDetailsByRelatedData(String tenantId, String apiName, String dataId);

    //todo:可能是审批通过又驳回的
    List<IObjectData> queryFrozenDetailByRelateObjectWithoutUnfreeze(User user, String apiName, String dataId);

    void updateOperateMark(User user, IObjectData detail, BudgetDetailOperateMark operateMark);

    List<IObjectData> queryByBusinessType(User user, String budgetId, BizType bizType);

    String queryConsumeDetailBusinessId(User user, String relatedApiName, String relatedDataId, String consumeRuleId, boolean enableWithholding);

    Map<String, BigDecimal> queryFrozenAmountByBusinessId(User user, String businessId);

    List<IObjectData> batchAdd(User user, List<IObjectData> details);


    IObjectData fromDetail(
            User user,
            String budgetAccountId,
            MainType mainType,
            BizType bizType,
            BudgetDetailOperateMark operateMark,
            BigDecimal amount,
            IObjectData relatedObj,
            String bizTraceId,
            String approvalId,
            String budgetFreezeDetailId);

    Map<String, String> getConsumeFrozenDetailIdByBusinessId(User user, String businessId, List<String> budgetIds);

    List<IObjectData> queryDetailsByTraceIdAndBusinessId(User user, String businessId, String traceId);

    /**
     * 通过businessId获取审批前处理的明细
     *
     * @param user
     * @param businessId
     * @return
     */
    List<IObjectData> getAdvanceDetailByBusinessId(User user, String businessId);


    int countDetailByTraceId(User user, String traceId);

    void deleteUselessBudgetDetail(String tenantId, List<String> detailIds);
}
