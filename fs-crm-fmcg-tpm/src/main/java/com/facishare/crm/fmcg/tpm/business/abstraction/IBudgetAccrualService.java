package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/26 下午6:01
 */
public interface IBudgetAccrualService {

    void triggerAccrualRule(String tenantId, String triggerAction, IObjectData triggerData, Map<String, Object> updateMaps);

    boolean existsAccrualRuleDataByApiName(String tenantId, String apiName, String ruleId);

    void updateAccrualStatus(User user, IObjectData triggerData, String message, boolean isSendCrmMessage, Set<Integer> receiverIds, IObjectData accrualData);

    void addTriggerRuleStatusField(String tenantId, String describeApiName);
}
