package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("Duplicates")
@Entity(value = "fmcg_tpm_budget_template", noClassnameStored = true)
public class BudgetTemplatePO extends MongoPO {

    public static final String F_NAME = "name";
    public static final String F_RECORD_TYPE = "record_type";
    public static final String F_DEPARTMENT_DIMENSION_LEVEL = "department_dimension_level";
    public static final String F_TIME_DIMENSION = "time_dimension";
    public static final String F_DIMENSIONS = "dimensions";
    public static final String F_CONTROL_STRATEGY = "control_strategy";
    public static final String F_CONTROL_TIME_DIMENSION = "control_time_dimensions";
    public static final String F_CONTROL_DIMENSIONS = "control_dimensions";
    public static final String F_STATUS = "status";
    public static final String F_VERSION = "version";
    public static final String F_ENABLE_CARRY_FORWARD = "enable_carry_forward";

    @Property(F_VERSION)
    private long version;

    @Property(F_NAME)
    private String name;

    @Property(F_RECORD_TYPE)
    private String recordType;

    @Property(F_STATUS)
    private String status;

    @Property(F_DEPARTMENT_DIMENSION_LEVEL)
    private int departmentDimensionLevel;

    @Property(F_TIME_DIMENSION)
    private String timeDimension;

    @Embedded(F_DIMENSIONS)
    private List<BudgetDimensionEntity> dimensions;

    public List<BudgetDimensionEntity> getDimensions() {
        return CollectionUtils.isEmpty(this.dimensions) ? Lists.newArrayList() : this.dimensions;
    }

    @Property(F_CONTROL_STRATEGY)
    private String controlStrategy;

    @Property(F_CONTROL_TIME_DIMENSION)
    private String controlTimeDimension;

    @Embedded(F_CONTROL_DIMENSIONS)
    private List<BudgetDimensionEntity> controlDimensions;

    public List<BudgetDimensionEntity> getControlDimensions() {
        return CollectionUtils.isEmpty(this.controlDimensions) ? Lists.newArrayList() : this.controlDimensions;
    }

    @Property(F_ENABLE_CARRY_FORWARD)
    private boolean enableCarryForward;
}
