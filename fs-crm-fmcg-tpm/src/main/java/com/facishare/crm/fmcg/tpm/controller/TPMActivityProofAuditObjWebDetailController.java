package com.facishare.crm.fmcg.tpm.controller;

import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.AuditModeType;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMActivityProofAuditObjWebDetailController extends StandardWebDetailController {

    private ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(arg, result);
        return super.after(arg, result);
    }

    private void buttonFilter(Arg arg, Result result) {
        List components = (ArrayList) (result.getLayout().get("components"));
        if (!"mobile".equals(arg.getLayoutAgentType())) {
            List<String> removeList = Lists.newArrayList(ObjectAction.TPM_PROOF_RANDOM_AUDIT.getActionCode(), "Edit");
            removeButton(components, removeList);
        } else {
            ActivityTypeExt activityTypeExt = activityTypeManager.findByActivityId(controllerContext.getTenantId(), (String) result.getData().get(TPMActivityProofAuditFields.ACTIVITY_ID));
            String randomAuditStatus = (String) result.getData().get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS);
            if (!AuditModeType.RANDOM.value().equals(activityTypeExt.auditModeConfig().getAuditMode()) || TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS__CHECKED.equals(randomAuditStatus)) {
                List<String> removeList = Lists.newArrayList(ObjectAction.TPM_PROOF_RANDOM_AUDIT.getActionCode());
                ArrayList buttons = (ArrayList) result.getLayout().get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    return removeList.contains(btn.get("action"));
                });
            }
        }
    }


    private void removeButton(List components, List<String> removeList) {
        for (Object component : components) {
            Map com = (Map) component;
            if ("head_info".equals(com.get("api_name"))) {
                ArrayList buttons = (ArrayList) com.get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    return removeList.contains(btn.get("action"));
                });
            }
        }
    }
}
