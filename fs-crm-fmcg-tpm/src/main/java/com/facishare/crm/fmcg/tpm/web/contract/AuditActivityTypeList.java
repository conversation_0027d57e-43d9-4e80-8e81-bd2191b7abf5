package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.UnauditedActivityTypeVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/24 14:07
 */
public interface AuditActivityTypeList {

    @Data
    @ToString
    class Arg implements Serializable {
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "activity_type_list")
        @JsonProperty(value = "activity_type_list")
        @SerializedName("activity_type_list")
        private List<UnauditedActivityTypeVO> activityTypeList;
    }
}
