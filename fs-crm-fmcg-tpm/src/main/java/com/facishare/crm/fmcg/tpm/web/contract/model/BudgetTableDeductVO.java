package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/14 下午7:44
 */
@Data
@ToString
public class BudgetTableDeductVO implements Serializable {

    @JSONField(name = "api_name")
    @JsonProperty(value = "api_name")
    @SerializedName("api_name")
    private String apiName;

    @JSONField(name = "relation_field")
    @JsonProperty(value = "relation_field")
    @SerializedName("relation_field")
    private String relationField;

    @JSONField(name = "amount_field")
    @JsonProperty(value = "amount_field")
    @SerializedName("amount_field")
    private String amountField;

    @JSONField(name = "master_detail")
    @JsonProperty(value = "master_detail")
    @SerializedName("master_detail")
    private String masterDetail;
}
