package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/28 下午8:12
 */
@Data
@ToString
public class BudgetTableNodeVO implements Serializable {

    @JSONField(name = "node_id")
    @JsonProperty(value = "node_id")
    @SerializedName("node_id")
    private String nodeId;

    @JSONField(name = "field_relation")
    @JsonProperty(value = "field_relation")
    @SerializedName("field_relation")
    private List<BudgetFieldRelationVO> fieldRelation;

    //比例
    private String ratio;
}
