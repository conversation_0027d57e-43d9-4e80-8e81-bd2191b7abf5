package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/28 12:26
 */
public interface StoreAuditDataList {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "data_id")
        @JsonProperty(value = "data_id")
        @SerializedName("data_id")
        private String dataId;

        @JSONField(name = "activity_id")
        @JsonProperty(value = "activity_id")
        @SerializedName("activity_id")
        private String activityId;

        @JSONField(name = "dealer_id")
        @JsonProperty(value = "dealer_id")
        @SerializedName("dealer_id")
        private String dealerId;

        @JSONField(name = "id")
        @JsonProperty(value = "id")
        @SerializedName("id")
        private String id;

        @JSONField(name = "data_id_list")
        @JsonProperty(value = "data_id_list")
        @SerializedName("data_id_list")
        private List<String> dataIdList;

        @JSONField(name = "need_return_describe_and_layout")
        @JsonProperty(value = "need_return_describe_and_layout")
        @SerializedName("need_return_describe_and_layout")
        private boolean needReturnDescribeAndLayout;

        @JSONField(name = "view_mode")
        @JsonProperty(value = "view_mode")
        @SerializedName("view_mode")
        private String viewMode;

        @JSONField(name = "audit_object_record_type")
        @JsonProperty(value = "audit_object_record_type")
        @SerializedName("audit_object_record_type")
        private String auditObjectRecordType;

        @JSONField(name = "out_tenant_id")
        @JsonProperty(value = "out_tenant_id")
        @SerializedName("out_tenant_id")
        private String outTenantId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "object_describe_map")
        @JsonProperty(value = "object_describe_map")
        @SerializedName("object_describe_map")
        private Map<String, ObjectDescribeDocument> objectDescribeMap;

        @JSONField(name = "layout_map")
        @JsonProperty(value = "layout_map")
        @SerializedName("layout_map")
        private Map<String, Map<String, LayoutDocument>> layoutMap;

        @JSONField(name = "field_show_name_map")
        @JsonProperty(value = "field_show_name_map")
        @SerializedName("field_show_name_map")
        private Map<String, Map<String, Map<String, String>>> fieldShowNameMap;

        @JSONField(name = "main_object_data")
        @JsonProperty(value = "main_object_data")
        @SerializedName("main_object_data")
        private ObjectDataDocument mainObjectData;

        @JSONField(name = "data")
        @JsonProperty(value = "data")
        @SerializedName("data")
        private List<AuditDatumVO> data;

        @JSONField(name = "activity_type")
        @JsonProperty(value = "activity_type")
        @SerializedName("activity_type")
        private ActivityTypeVO activityType;

        @JSONField(name = "need_store")
        @JsonProperty(value = "need_store")
        @SerializedName("need_store")
        private Boolean needStore;
    }

    @Data
    @ToString
    class AuditDatumVO implements Serializable {

        @JSONField(name = "all_images")
        @JsonProperty(value = "all_images")
        @SerializedName("all_images")
        private Map<String, List<AuditImageVO>> allImages;

        @JSONField(name = "visit_data")
        @JsonProperty(value = "visit_data")
        @SerializedName("visit_data")
        private VisitDataVO visitData;

        @JSONField(name = "application_data")
        @JsonProperty(value = "application_data")
        @SerializedName("application_data")
        private ObjectDataVO applicationData;

        @JSONField(name = "audit_data")
        @JsonProperty(value = "audit_data")
        @SerializedName("audit_data")
        private ObjectDataVO auditData;

        @JSONField(name = "multiple_level_audit_data")
        @JsonProperty(value = "multiple_level_audit_data")
        @SerializedName("multiple_level_audit_data")
        private Map<String, ObjectDataVO> multipleLevelAuditData;
    }

    @Data
    @ToString
    @Builder
    class ObjectDataVO implements Serializable {

        @JSONField(name = "is_random_audit")
        @JsonProperty(value = "is_random_audit")
        @SerializedName("is_random_audit")
        private Boolean isRandomAudit;

        @JSONField(name = "is_enable_edit")
        @JsonProperty(value = "is_enable_edit")
        @SerializedName("is_enable_edit")
        private Boolean isEnableEdit;

        @JSONField(name = "audit_status")
        @JsonProperty(value = "audit_status")
        @SerializedName("audit_status")
        private String auditStatus;

        private ObjectDataDocument data;

        private Map<String, List<ObjectDataDocument>> details;
    }

    @Data
    @ToString
    class VisitDataVO implements Serializable {

        private boolean deleted;

        @JSONField(name = "check_in_time")
        @JsonProperty(value = "check_in_time")
        @SerializedName("check_in_time")
        private Long checkInTime;

        @JSONField(name = "check_out_time")
        @JsonProperty(value = "check_out_time")
        @SerializedName("check_out_time")
        private Long checkOutTime;

        private Long span;

        @JSONField(name = "nearby_customers_count")
        @JsonProperty(value = "nearby_customers_count")
        @SerializedName("nearby_customers_count")
        private Integer nearbyCustomersCount;

        @JSONField(name = "visit_times_last_30days")
        @JsonProperty(value = "visit_times_last_30days")
        @SerializedName("visit_times_last_30days")
        private Integer visitTimesLast30Days;

        @JSONField(name = "visit_user_count_last_30days")
        @JsonProperty(value = "visit_user_count_last_30days")
        @SerializedName("visit_user_count_last_30days")
        private Integer visitUserCountLast30Days;

        @JSONField(name = "device_name")
        @JsonProperty(value = "device_name")
        @SerializedName("device_name")
        private String deviceName;

        private Integer distance;

        @JSONField(name = "display_store_detail_list")
        @JsonProperty(value = "display_store_detail_list")
        @SerializedName("display_store_detail_list")
        private List<DisplayStoreDetailVO> displayStoreDetailList;
    }

    @Data
    @ToString
    class AuditImageVO implements Serializable {

        private String path;

        private Long size;

        private String ext;

        private String label;

        private String filename;

        public static AuditImageVO fromJSON(String fieldDisplayName, JSONObject json) {
            AuditImageVO vo = new AuditImageVO();
            vo.setPath(json.getString("path"));
            BigDecimal size = json.getBigDecimal("size");
            vo.setSize(size == null ? null : size.longValue());
            vo.setExt(json.getString("ext"));
            vo.setLabel(json.getString("label"));
            vo.setFilename(json.getString("filename"));
            vo.setLabel(fieldDisplayName);
            return vo;
        }
    }

    @Data
    @ToString
    class DisplayStoreDetailVO implements Serializable {

        private String label;

        private String value;
    }
}
