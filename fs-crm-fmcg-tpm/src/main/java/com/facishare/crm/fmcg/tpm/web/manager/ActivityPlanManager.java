package com.facishare.crm.fmcg.tpm.web.manager;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.pg.ActivityMapper;
import com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityTypeStatisticsDatumPO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityPlanReportDatumVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeReportDatumVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityPlanManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/22 15:08
 */
@Component
public class ActivityPlanManager implements IActivityPlanManager {

    @Resource
    private ActivityMapper activityMapper;

    @Override
    public ActivityTypeReportDatumVO loadActivityTypeReport(String tenantId, String activityTypeId, ActivityNodeEntity node, Map<String, String> displayNameMap, ActivityTypePO activityType) {
        Map<String, Long> activityPlanReportData = activityMapper.setTenantId(tenantId).queryActivityTypeStatisticsData(tenantId, activityTypeId)
                .stream()
                .collect(Collectors.toMap(ActivityTypeStatisticsDatumPO::getStatus, ActivityTypeStatisticsDatumPO::getCount));

        return ActivityTypeReportDatumVO.builder()
                .templateId(node.getTemplateId())
                .type(node.getType())
                .nodeDisplayName(node.getName())
                .objectApiName(ApiNames.TPM_ACTIVITY_OBJ)
                .objectDisplayName(displayNameMap.get(ApiNames.TPM_ACTIVITY_OBJ))
                .count(activityPlanReportData.getOrDefault(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS, 0L))
                .totalCount(activityPlanReportData.values().stream().mapToLong(v -> v).sum())
                .scheduleCount(activityPlanReportData.getOrDefault(TPMActivityFields.ACTIVITY_STATUS__SCHEDULE, 0L))
                .inProgressCount(activityPlanReportData.getOrDefault(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS, 0L))
                .endedCount(activityPlanReportData.getOrDefault(TPMActivityFields.ACTIVITY_STATUS__END, 0L))
                .build();
    }

    @Override
    public ActivityPlanReportDatumVO loadActivityPlanReport(String tenantId, String activityPlanId) {
        return ActivityPlanReportDatumVO.builder()
                .count(0)
                .build();
    }
}
