package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.PromotionPolicyPO;
import com.facishare.crm.fmcg.tpm.web.contract.PromotionPolicyEdit;
import com.facishare.crm.fmcg.tpm.web.contract.PromotionPolicyGet;
import com.facishare.crm.fmcg.tpm.web.contract.StatisticPolicyOccupy;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/28 20:28
 */
public interface IPromotionPolicyService {

    void saveOrUpdate(String tenantId, String userId, IObjectData objectData, String json, Boolean isUpdateLimitAccount);

    PromotionPolicyGet.Result get(PromotionPolicyGet.Arg arg);

    void revertPromotionPolicyRule(String tenantId, String userId, IObjectData toObjectData);

    void createSFAPromotionPolicy(String tenantId, String userId, IObjectData toObjectData);

    void updateSFAPromotionPolicy(String tenantId, String userId, IObjectData toObjectData);

    void batchUpdateSFAPromotionPolicy(String tenantId, String userId, String modeType, List<IObjectData> objectDataList);

    void disablePromotionPolicy(String tenantId, String userId, IObjectData toObjectData);

    void enablePromotionPolicy(String tenantId, String userId, IObjectData toObjectData);

    void batchInvalidPromotionPolicy(String tenantId, String userId, List<IObjectData> objectDataList);

    Boolean isOpenPromotionPolicy(String tenantId);

    PromotionPolicyEdit.Result doEnableEditPromotionPolicy(String tenantId, IObjectData objectData);

    PromotionPolicyPO getPromotionPolicyData(String tenantId, IObjectData toObjectData);

    PromotionPolicyEdit.Result enableEditPromotionPolicy(PromotionPolicyEdit.Arg arg);

    boolean judgedIsPromotionPolicyData(IObjectData objectData);

    boolean doEnableInvalidPromotionPolicy(String tenantId, IObjectData objectData);

    Map<String, List<IObjectData>> queryIsNoSalesOrderActivityMap(String tenantId, List<IObjectData> objectDatas, String apiName);

    boolean validationIsNeedActivityId(IObjectData objectData);

    boolean validationIsInvalid(IObjectData objectData);

    boolean pricePolicyIsContainsActivity(IObjectData objectData);

    List<String> queryIsNotValidaNames(List<ObjectDataDocument> objectDataList);

    void bindPluginInstance(String tenantId, int employeeId, String apiName, String pluginName);

    void updateSFAPromotionPolicyTest(String id);

    void createSFAPromotionPolicyTest(String id);

    StatisticPolicyOccupy.Result statisticPolicyOccupy(StatisticPolicyOccupy.Arg arg);

    void asyncCreateRIOPricePolicyLimitAccount(String tenantId, String userId, IObjectData toObjectData, String pricePolicyId);
}
