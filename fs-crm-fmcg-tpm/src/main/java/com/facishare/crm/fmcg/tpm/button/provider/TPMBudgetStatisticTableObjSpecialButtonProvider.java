package com.facishare.crm.fmcg.tpm.button.provider;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.button.abs.AbstractTPMSpecialButtonProvider;
import com.facishare.crm.fmcg.tpm.utils.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/29 15:13
 */
@Component
public class TPMBudgetStatisticTableObjSpecialButtonProvider extends AbstractTPMSpecialButtonProvider {

    private static final Logger LOG = LoggerFactory.getLogger(TPMBudgetStatisticTableObjSpecialButtonProvider.class);

    @Override
    public String getApiName() {
        return ApiNames.TPM_BUDGET_STATISTIC_TABLE;
    }

    @Override
    public List<IButton> getSpecialButtons() {

        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.STATISTIC_TABLE_REFRESH));
        return buttons;
    }
}
