package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.apiname.ActivityCustomerTypeEnum;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @author: wuyx
 * @description: TPM 客户范围
 * @createTime: 2021/12/29 11:04
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjRelatedCustomerListController extends StandardRelatedListController {

    public static final String ACTIVITY_ID_FIELD = "activity_id";

    public static final String DEALER_ID_KEY = "__dealer_id";
    public static final String DEALER_NAME_KEY = "__dealer_id__r";
    public static final String AGREEMENT_CASHING_TYPE_KEY = "__agreement_cashing_type";
    public static final String DEALER_COST_CASHING_TYPE_KEY = "__dealer_cost_cashing_type";

    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private final IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(IUnifiedActivityCommonLogicBusiness.class);


    private IObjectData activity;

    /**
     * 重新设置上下文，将上下文改为客户对象
     *
     * @param arg 搜索客户入参
     */
    @Override
    protected void before(Arg arg) {
        this.controllerContext = new ControllerContext(
                controllerContext.getRequestContext(),
                ApiNames.ACCOUNT_OBJ,
                controllerContext.getMethodName());

        log.info("store filter arg : {}", arg);

        super.before(arg);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        List<IFilter> filters = query.getFilters();
        List<IFilter> objectDescribeNameList = filters.stream().filter(iFilter -> "object_describe_api_name".equals(iFilter.getFieldName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(objectDescribeNameList)) {
            IFilter filter = new Filter();
            filter.setFieldName("object_describe_api_name");
            filter.setConnector("AND");
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(arg.getObjectApiName()));
            query.setFilters(Lists.newArrayList(filter));
        }
        overrideAccountQuery(query);

        super.beforeQueryData(query);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return overrideDealerInformation(super.after(arg, result));
    }

    private Result overrideDealerInformation(Result result) {
        String fieldApiName = storeBusiness.findDealerFieldApiName(controllerContext.getTenantId());
        List<String> recordType = storeBusiness.findDealerRecordType(controllerContext.getTenantId());
        String dealerCashingType = TPMActivityCashingProductFields.CASH;
        String storeCashingType = TPMActivityCashingProductFields.CASH;
        if (TPMGrayUtils.agreementCashingTypeAllowNull(controllerContext.getTenantId())) {
            dealerCashingType = "";
            storeCashingType = "";
        }
        if (activity != null) {
            if (TPMGrayUtils.agreementCashingTypeAllowNull(controllerContext.getTenantId())) {
                dealerCashingType = StringUtils.isEmpty(activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class)) ? "" : activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class);
                storeCashingType = StringUtils.isEmpty(activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class)) ? "" : activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class);
            } else {
                dealerCashingType = StringUtils.isEmpty(activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class)) ? TPMActivityCashingProductFields.CASH : activity.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class);
                storeCashingType = StringUtils.isEmpty(activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class)) ? TPMActivityCashingProductFields.CASH : activity.get(TPMActivityFields.STORE_CASHING_TYPE, String.class);
            }
        }
        Set<String> dealerIds = Sets.newHashSet();
        for (ObjectDataDocument datum : result.getDataList()) {
            String id = (String) datum.get(CommonFields.ID);
            String recordTypeValue = (String) datum.get(CommonFields.RECORD_TYPE);
            if (recordType.contains(recordTypeValue)) {
                datum.put(DEALER_ID_KEY, id);
                dealerIds.add(id);
            } else {
                String dealerId = (String) datum.get(fieldApiName);
                if (!Strings.isNullOrEmpty(dealerId)) {
                    datum.put(DEALER_ID_KEY, dealerId);
                    dealerIds.add(dealerId);
                }
            }

            if (recordType.contains(recordTypeValue)) {
                datum.put(AGREEMENT_CASHING_TYPE_KEY, dealerCashingType);
                datum.put(DEALER_COST_CASHING_TYPE_KEY, dealerCashingType);
            } else if (Objects.equals(CommonFields.RECORD_TYPE__DEFAULT, recordTypeValue)) {
                datum.put(AGREEMENT_CASHING_TYPE_KEY, storeCashingType);
                datum.put(DEALER_COST_CASHING_TYPE_KEY, storeCashingType);
            } else {
                datum.put(AGREEMENT_CASHING_TYPE_KEY, TPMActivityCashingProductFields.CASH);
            }

        }
        Map<String, String> dealerNameMap = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(dealerIds), ApiNames.ACCOUNT_OBJ)
                .stream()
                .collect(Collectors.toMap(DBRecord::getId, IObjectData::getName));

        for (ObjectDataDocument datum : result.getDataList()) {
            String dealerId = (String) datum.get(DEALER_ID_KEY);
            datum.put(DEALER_NAME_KEY, dealerNameMap.get(dealerId));
        }

        return result;
    }

    /**
     * 依据活动方案信息，注入额外的 query 信息
     *
     * @param query 搜索 query
     * @return 搜索结果
     */
    private void overrideAccountQuery(SearchTemplateQuery query) {
        String tenantId = controllerContext.getTenantId();
        String activityId = (String) arg.getObjectData().get(ACTIVITY_ID_FIELD);
        String dealerFieldApiName = storeBusiness.findDealerFieldApiName(controllerContext.getTenantId());

        if (!Strings.isNullOrEmpty(activityId)) {
            activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
            if (!Objects.isNull(activity)) {
                String dealerId = activity.get(TPMActivityFields.DEALER_ID, String.class);
                String customerType = activity.get(TPMActivityFields.CUSTOMER_TYPE, String.class, ActivityCustomerTypeEnum.DEALER_STORE.value());
                JSONObject storeRange = JSON.parseObject(activity.get(TPMActivityFields.STORE_RANGE, String.class));

                AtomicInteger count = new AtomicInteger(1);
                StringBuilder pattern = new StringBuilder();

                if (!CollectionUtils.isEmpty(query.getFilters())) {
                    pattern.append("( ");
                    for (int i = 0; i < query.getFilters().size(); i++) {
                        if (i != 0) {
                            pattern.append(" and ");
                        }
                        pattern.append(count.getAndIncrement());
                    }
                    pattern.append(") and");
                }

                pattern.append(" ( ");

                //todo lingmj 会拼 and （） 语句
                switch (storeRange.getString("type").toUpperCase()) {
                    case "CONDITION":
                        List<Wheres> tmpWhereList = JSON.parseArray(storeRange.getString("value"), Wheres.class);
                        if (setAdditionFilterForDealer(query, dealerId, customerType, storeBusiness.findDealerRecordType(tenantId), dealerFieldApiName, pattern, count)) {
                            pattern.append(" and ");
                        }
                        if (!CollectionUtils.isEmpty(tmpWhereList)) {
                            pattern.append(" ( ");
                            for (int index = 0; index < tmpWhereList.size(); index++) {
                                Wheres where = tmpWhereList.get(index);
                                pattern.append(" ( ");
                                for (int i = 0; i < where.getFilters().size(); i++) {
                                    query.getFilters().add(where.getFilters().get(i));
                                    pattern.append(" ").append(count.getAndIncrement());
                                    if (i < where.getFilters().size() - 1) {
                                        pattern.append(" and");
                                    }
                                }
                                String midString = pattern.toString();
                                if (midString.endsWith(" ( ")) {
                                    pattern.delete(pattern.length() - 2, 3);
                                } else {
                                    pattern.append(" )");
                                    if (index < tmpWhereList.size() - 1) {
                                        pattern.append(" or ");
                                    }
                                }
                            }
                            pattern.append(" ) ");
                        }
                        break;
                    case "FIXED":
                        List<String> storeIds = queryStoreIds(tenantId, activityId);
                        log.info("fixed store ids : {}", storeIds);

                        if (CollectionUtils.isNotEmpty(storeIds)) {
                            Filter storeIdFilter = new Filter();
                            storeIdFilter.setFieldName(CommonFields.ID);
                            storeIdFilter.setOperator(Operator.IN);
                            storeIdFilter.setFieldValues(storeIds);
                            query.getFilters().add(storeIdFilter);
                            pattern.append(" ").append(count.getAndIncrement()).append(" ");
                        }
                        break;
                    case "ALL":
                        setAdditionFilterForDealer(query, dealerId, customerType, storeBusiness.findDealerRecordType(tenantId), dealerFieldApiName, pattern, count);
                        break;
                    default:
                        break;
                }
                pattern.append(" )");
                log.info("override before pattern:{}", pattern);
                query.setPattern(removeUseless(pattern.toString()));
                log.info("after pattern:{}", query.getPattern());
            }
        }
        log.info("final pattern:{},filters；{}", query.getPattern(), JSON.toJSONString(query.getFilters()));
    }


    private boolean setAdditionFilterForDealer(SearchTemplateQuery query, String dealerId, String customerType, List<String> dealerRecordType, String dealerFieldApiName, StringBuilder pattern, AtomicInteger count) {
        boolean needAnd = true;
        if (Strings.isNullOrEmpty(dealerId)) {
            needAnd = setUnifiedFilter(controllerContext.getTenantId(), query, dealerFieldApiName, pattern, count, customerType);
        } else {
            Filter dealerSelfFilter = new Filter();
            dealerSelfFilter.setFieldName(CommonFields.ID);
            dealerSelfFilter.setOperator(Operator.EQ);
            dealerSelfFilter.setFieldValues(Lists.newArrayList(dealerId));
            query.getFilters().add(dealerSelfFilter);
            pattern.append(" (").append(count.getAndIncrement()).append(" or ");

            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(dealerFieldApiName);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
            query.getFilters().add(dealerIdFilter);
            pattern.append(" ").append(count.getAndIncrement()).append(" ) ");
        }

        //添加参与客户业务类型校验
        if (ActivityCustomerTypeEnum.DEALER.value().equals(customerType)) {
            if (needAnd) {
                pattern.append(" and ");
            }
            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setOperator(Operator.IN);
            recordTypeFilter.setFieldValues(dealerRecordType);
            query.getFilters().add(recordTypeFilter);
            pattern.append(" ").append(count.getAndIncrement()).append(" ");
            needAnd = true;
        } else if (ActivityCustomerTypeEnum.STORE.value().equals(customerType)) {
            if (needAnd) {
                pattern.append(" and ");
            }
            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setOperator(Operator.NIN);
            recordTypeFilter.setFieldValues(dealerRecordType);
            query.getFilters().add(recordTypeFilter);
            pattern.append(" ").append(count.getAndIncrement()).append(" ");
            needAnd = true;
        }
        return needAnd;
    }

    private String removeUseless(String pattern) {
        if (pattern == null || Strings.isNullOrEmpty(pattern.trim())) {
            return null;
        }
        String right = " ";
        String template = "";
        int removeIndex = pattern.length();
        for (int i = pattern.length() - 1; i >= 0; i--) {
            char ch = pattern.charAt(i);
            if (ch == ' ') {
                if (!template.equals("")) {
                    if (template.equals("and") || template.equals("or")) {
                        if (right.equals(")") || right.equals(" ") || right.isEmpty()) {
                            removeIndex = i;
                        }
                    }
                    if (right.length() > 0 && Character.isDigit(right.charAt(0)) && right.equals("(")) {
                        removeIndex = i;
                    }
                    right = template;
                    template = "";
                }
                continue;
            }
            if (ch == '(') {
                if (right.equals(")") || right.equals("and") || right.equals("or")) {
                    removeIndex = i;
                }
            }
            if (ch == ')') {
                if (right.equals("(") || right.length() > 0 && Character.isDigit(right.charAt(0))) {
                    removeIndex = i;
                } else {
                    break;
                }
            }
            template = ch + template;
        }
        String result = pattern.substring(0, removeIndex);
        if (!pattern.equals(result)) {
            return removeUseless(result);
        }
        return result.trim().length() == 0 ? null : result.trim();
    }


    private boolean setUnifiedFilter(String tenantId, SearchTemplateQuery query, String dealerFieldApiName, StringBuilder pattern, AtomicInteger count, String customerType) {
        String unifiedActivityId = activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
        if (!Strings.isNullOrEmpty(unifiedActivityId)) {
            IObjectData unifiedActivity = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(), unifiedActivityId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
            Set<String> dealerIds = unifiedActivityCommonLogicBusiness.getDealerIdsOfUnifiedActivity(tenantId, unifiedActivity);
            if (CollectionUtils.isEmpty(dealerIds)) {
                dealerIds.add("no_dealer");
            }
            if (!dealerIds.contains("-1")) {
                pattern.append(" ( ");
                if (ActivityCustomerTypeEnum.DEALER_STORE.value().equals(customerType) || ActivityCustomerTypeEnum.DEALER.value().equals(customerType)) {
                    Filter dealerIdFilter = new Filter();
                    dealerIdFilter.setFieldName(dealerFieldApiName);
                    dealerIdFilter.setOperator(Operator.IN);
                    dealerIdFilter.setFieldValues(new ArrayList<>(dealerIds));
                    query.getFilters().add(dealerIdFilter);
                    pattern.append(count.getAndIncrement()).append(" or ");
                }
                Filter dealerIdSelfFilter = new Filter();
                dealerIdSelfFilter.setFieldName(CommonFields.ID);
                dealerIdSelfFilter.setOperator(Operator.IN);
                dealerIdSelfFilter.setFieldValues(new ArrayList<>(dealerIds));
                query.getFilters().add(dealerIdSelfFilter);
                pattern.append(count.getAndIncrement()).append(" ) ");
                return true;
            }
        }
        return false;
    }

    private List<String> queryStoreIds(String tenantId, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        query.setFilters(Lists.newArrayList(activityIdFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_STORE_OBJ, query)
                .stream().map(m -> m.get(TPMActivityStoreFields.STORE_ID, String.class))
                .collect(Collectors.toList());
    }
}
