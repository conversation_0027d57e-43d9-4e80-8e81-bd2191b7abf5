package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetOccupationDetailFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOccupyService;
import com.facishare.crm.fmcg.tpm.dao.paas.BudgetOccupationDetailMapper;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
//IgnoreI18nFile
@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class BudgetOccupyService implements IBudgetOccupyService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private BudgetOccupationDetailMapper budgetOccupationDetailMapper;

    @Override
    public IObjectData occupy(String tenantId, String accountId, String businessTraceId, String approvalTraceId, BigDecimal amount) {
        return occupy(tenantId, accountId, amount, "", businessTraceId, approvalTraceId, "", null, null);
    }

    @Override
    public IObjectData occupy(
            String tenantId,
            String accountId,
            BigDecimal amount,
            String bizCode,
            String businessTraceId,
            String approvalTraceId,
            String operateMark,
            String relatedApiName,
            String relateObjectId) {
        IObjectData data = new ObjectData();

        data.setOwner(Lists.newArrayList("-10000"));
        data.setTenantId(tenantId);
        data.setRecordType("default__c");
        data.setDescribeApiName(ApiNames.TPM_BUDGET_OCCUPATION_DETAIL);

        data.set(TPMBudgetOccupationDetailFields.BUDGET_ACCOUNT_ID, accountId);
        data.set(TPMBudgetOccupationDetailFields.AMOUNT, amount);
        data.set(TPMBudgetOccupationDetailFields.OPERATE_TIME, System.currentTimeMillis());
        data.set(TPMBudgetOccupationDetailFields.BIZ_CODE, bizCode);
        data.set(TPMBudgetOccupationDetailFields.BIZ_TRACE_ID, businessTraceId);
        data.set(TPMBudgetOccupationDetailFields.APPROVAL_TRACE_ID, approvalTraceId);
        data.set(TPMBudgetOccupationDetailFields.OPERATE_MARK_DETAIL, operateMark);

        data.set(TPMBudgetOccupationDetailFields.RELATED_OBJECT_API_NAME, relatedApiName);
        data.set(TPMBudgetOccupationDetailFields.RELATED_OBJECT_ID, relateObjectId);

        return serviceFacade.saveObjectData(User.systemUser(tenantId), data);
    }

    @Override
    public void release(String tenantId, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        long now = System.currentTimeMillis();

        List<Map<String, Object>> updateList = new ArrayList<>();
        List<IObjectData> data = new ArrayList<>();

        for (String id : ids) {
            if (Strings.isNullOrEmpty(id)) {
                continue;
            }
            Map<String, Object> datum = new HashMap<>();
            datum.put(CommonFields.ID, id);
            datum.put(CommonFields.LAST_MODIFY_TIME, now);
            datum.put(TPMBudgetOccupationDetailFields.OCCUPIED_STATUS, TPMBudgetOccupationDetailFields.OCCUPIED_STATUS_RELEASE);
            data.add(new ObjectData(datum));
            updateList.add(datum);
        }
        budgetOccupationDetailMapper.setTenantId(tenantId).updateOccupationStatus(tenantId, updateList);

        IObjectDescribe describe = serviceFacade.findObjectIncludeDeleted(tenantId, ApiNames.TPM_BUDGET_OCCUPATION_DETAIL);
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.UPDATE_FIELD, describe, data, "释放占用金额。");
    }

    @Override
    public BigDecimal statistics(String tenantId, String accountId) {
        BigDecimal amount = budgetOccupationDetailMapper.setTenantId(tenantId).statisticOccupiedMoney(tenantId, accountId);
        return amount == null ? new BigDecimal("0") : amount;
    }

    @Override
    public List<IObjectData> query(String tenantId, String approvalTraceId) {
        Filter approvalFilter = new Filter();
        approvalFilter.setFieldName(TPMBudgetOccupationDetailFields.APPROVAL_TRACE_ID);
        approvalFilter.setOperator(Operator.EQ);
        approvalFilter.setFieldValues(Lists.newArrayList(approvalTraceId));

        return queryByFilter(tenantId, Lists.newArrayList(approvalFilter));
    }

    private List<String> query(String tenantId, String accountId, String businessTraceId, String approvalTraceId) {

        Filter accountIdFilter = new Filter();
        accountIdFilter.setFieldName(TPMBudgetOccupationDetailFields.BUDGET_ACCOUNT_ID);
        accountIdFilter.setOperator(Operator.EQ);
        accountIdFilter.setFieldValues(Lists.newArrayList(accountId));

        Filter bizFilter = new Filter();
        bizFilter.setFieldName(TPMBudgetOccupationDetailFields.BIZ_TRACE_ID);
        bizFilter.setOperator(Operator.EQ);
        bizFilter.setFieldValues(Lists.newArrayList(businessTraceId));

        Filter approvalFilter = new Filter();
        approvalFilter.setFieldName(TPMBudgetOccupationDetailFields.APPROVAL_TRACE_ID);
        approvalFilter.setOperator(Operator.EQ);
        approvalFilter.setFieldValues(Lists.newArrayList(approvalTraceId));

        return queryByFilter(tenantId, Lists.newArrayList(accountIdFilter, bizFilter, approvalFilter), Lists.newArrayList("_id"))
                .stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    @Override
    public Map<String, BigDecimal> batchStatistics(String tenantId, List<String> accountIds) {
        return budgetOccupationDetailMapper.statisticOccupiedMoney(tenantId, accountIds);
    }

    @Override
    public IObjectData get(String tenantId, String id) {
        if (Strings.isNullOrEmpty(id)) {
            return null;
        }
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(id), ApiNames.TPM_BUDGET_OCCUPATION_DETAIL);
        return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0);
    }

    @Override
    public void release(String tenantId, String accountId, String businessTraceId, String approvalTraceId) {
        this.release(tenantId, query(tenantId, accountId, businessTraceId, approvalTraceId));
    }

    private List<IObjectData> queryByFilter(String tenantId, List<IFilter> filters, List<String> fields) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        query.setOffset(0);
        query.setLimit(-1);
        query.setFilters(filters);

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_BUDGET_OCCUPATION_DETAIL, query, fields);
    }

    private List<IObjectData> queryByFilter(String tenantId, List<IFilter> filters) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        query.setOffset(0);
        query.setLimit(-1);
        query.setFilters(filters);

        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_BUDGET_OCCUPATION_DETAIL, query);
    }
}
