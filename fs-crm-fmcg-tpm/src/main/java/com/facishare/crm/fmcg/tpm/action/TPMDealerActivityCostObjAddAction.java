package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.business.*;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostService;
import com.facishare.crm.fmcg.tpm.business.dto.CrmAuditLogDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IDealerActivityCostEnterAccountService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/12/16 下午4:01
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class TPMDealerActivityCostObjAddAction extends StandardAddAction implements TransactionService<StandardAddAction.Arg, StandardAddAction.Result> {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);
    private final CrmAuditLogService crmAuditLogService = SpringUtil.getContext().getBean(CrmAuditLogService.class);
    private final UnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(UnifiedActivityCommonLogicBusiness.class);

    private final IActivityService activityService = SpringUtil.getContext().getBean(IActivityService.class);

    private final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private final IDealerActivityCostService dealerActivityCostService = SpringUtil.getContext().getBean(IDealerActivityCostService.class);
    private static final IDealerActivityCostEnterAccountService dealerActivityCostEnterAccountService = SpringUtil.getContext().getBean(IDealerActivityCostEnterAccountService.class);

    private static final String BUDGET_KEY = "BUDGET_KEY:%s";
    private static final String ACTIVITY_KEY = "ACTIVITY_KEY:%s";
    private boolean isOnceWriteOff = false;
    private boolean isForceCloseActivity = false;

    private IObjectData store;

    private ActivityTypeExt activityType;

    @Override
    protected void before(Arg arg) {
        //日志
        this.sendActivityObjAuditLog(arg);
        stopWatch.lap("sendActivityObjAuditLog");

        this.validateDateRange(arg);
        stopWatch.lap("validateDateRange");

        this.setDefaultValue(arg);
        stopWatch.lap("setDefaultValue");

        this.validateActivity(arg);
        stopWatch.lap("validateActivity");

        super.before(arg);
        stopWatch.lap("super.before");

        this.validateIfAllowCreate(arg);
        stopWatch.lap("validateIfAllowCreate");
    }

    private void sendActivityObjAuditLog(Arg arg) {
        crmAuditLogService.sendLog(CrmAuditLogDTO.builder()
                .tenantId(actionContext.getTenantId())
                .userId(String.valueOf(User.systemUser(actionContext.getTenantId())))
                .action("Add")
                .objectApiNames(ApiNames.TPM_DEALER_ACTIVITY_COST)
                .message("新建费用核销")//ignorei18n
                .parameters(JSONObject.toJSONString(arg))
                .build());
    }

    @Override
    protected Result doAct(Arg arg) {
        if (TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId())) {
            try {
                tryLock();
                actionContext.setAttribute("triggerFlow", true);
                return packTransactionProxy.packAct(this, arg);
            } catch (Exception e) {
                budgetService.rmSaveIdempotent(actionContext);
                throw e;
            }
        }
        return super.doAct(arg);
    }

    private void validateActivity(Arg arg) {
        IObjectData data = arg.getObjectData().toObjectData();
        double confirmAmount = Double.parseDouble(data.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT) == null || Strings.isNullOrEmpty(data.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT).toString()) ? "0.0" : data.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT).toString());
        if (!TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId()) && confirmAmount != 0) {
            throw new ValidateException(I18N.text(I18NKeys.CAN_NOT_FILL_AUDIT_AMOUNT_WHEN_CREATING_COST));
        }
        double amount = Double.parseDouble(data.get(TPMDealerActivityCostFields.AUDITED_AMOUNT) == null ? "0.0" : data.get(TPMDealerActivityCostFields.AUDITED_AMOUNT, String.class, "0.0"));

        String activityId = (String) data.get(TPMDealerActivityCostFields.ACTIVITY_ID);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

        String activityBudgetTableId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        Double activityAmount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, Double.class);

        String lifeStatus = activity.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_ADD_ACTION_0));
        }

        String closedStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_COST));
        }

        IObjectData objectData = arg.getObjectData().toObjectData();
        String activityType = objectData.get(TPMDealerActivityCostFields.ACTIVITY_TYPE, String.class);
        if (Strings.isNullOrEmpty(activityType) || !isMongoId(activityType)) {
            objectData.set(TPMDealerActivityCostFields.ACTIVITY_TYPE, activity.get(TPMActivityFields.ACTIVITY_TYPE));
        }

        if (Strings.isNullOrEmpty(objectData.get(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, String.class))) {
            if (store == null || storeBusiness.findDealerRecordType(actionContext.getTenantId()).contains(store.getRecordType())) {
                objectData.set(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, activity.get(TPMActivityFields.DEALER_CASHING_TYPE));
            } else {
                objectData.set(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, activity.get(TPMActivityFields.STORE_CASHING_TYPE));
            }
        }

        if (!TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId()) && !Strings.isNullOrEmpty(activityBudgetTableId) &&
                activityAmount != null &&
                amount > activityAmount) {
            throw new ValidateException(I18N.text(I18NKeys.AMOUNT_SHOULD_LESS_THAN_AUDIT_AMOUNT));
        }

        Long startTime = (Long) data.get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endTime = (Long) data.get(TPMDealerActivityCostFields.END_DATE);
        if (startTime == null || endTime == null) {
            throw new ValidateException("begin time or end time could not be empty.");
        }

        this.isOnceWriteOff = ActivityMaxWriteOffCountEnum.ONCE.value().equals(activity.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class));
        this.isForceCloseActivity = Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AUTO_CLOSE, Boolean.class));

        activityService.ifAllowCreateDataDueToOnceWriteOff(actionContext.getTenantId(), activity);

        dealerActivityCostService.validateOnceWriteOffData(actionContext.getTenantId(), activity, store, startTime, endTime);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result processedResult = super.after(arg, result);
        IObjectData resultObjectData = processedResult.getObjectData().toObjectData();
        try {
            asyncAddTpmLog(arg);
        } catch (Exception e) {
            log.warn("经销商费用核销埋点异常 tenantId：{}", actionContext.getTenantId(), e);
        }

        String budgetId = actionContext.getAttribute(String.format(BUDGET_KEY, actionContext.getPostId()));
        String activityId = actionContext.getAttribute(String.format(ACTIVITY_KEY, actionContext.getPostId()));
        log.info("actionContext:{},budgetId:{},activityId:{}", actionContext, budgetId, activityId);
        if (!Strings.isNullOrEmpty(activityId)) {
            budgetService.calculateActivity(actionContext.getTenantId(), activityId);
            budgetService.calculateBudget(actionContext.getTenantId(), budgetId);
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), result.getObjectData().toObjectData().get(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
        } else if (!Strings.isNullOrEmpty((activityId = resultObjectData.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class)))) {
            budgetService.calculateActivity(actionContext.getTenantId(), activityId);
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), result.getObjectData().toObjectData().get(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
        }
        setWriteOffStatus(processedResult);
        updateWriteOffSourceReferenceFieldValue(processedResult.getObjectData());
        if (!isApprovalFlowStartSuccess(result.getObjectData().getId()) && this.isOnceWriteOff && this.isForceCloseActivity) {
            activityService.triggerCloseTPMActivity(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), activityId, true, false);
        }
        return processedResult;
    }


    private void asyncAddTpmLog(Arg arg) {
        String dealerCashingType = arg.getObjectData().toObjectData().get(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, String.class);

        String buryModule = BuryModule.TPM.TPM_DEALER_ACTIVITY_COST;
        if (Objects.nonNull(dealerCashingType)) {
            buryModule = BuryModule.TPM.TPM_DEALER_ACTIVITY_COST + "_" + BuryModule.CASH_TYPE.CASH;
            if (Objects.equals(dealerCashingType, BuryModule.CASH_TYPE.GOODS)) {
                buryModule = BuryModule.TPM.TPM_DEALER_ACTIVITY_COST + "_" + BuryModule.CASH_TYPE.GOODS;
            }
        }
        if (!Strings.isNullOrEmpty(buryModule)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), buryModule, BuryOperation.CREATE, true);
        }

        BigDecimal confirmedAmount = arg.getObjectData().toObjectData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        BuryService.asyncTpmAmountLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM_AMOUNT.CONFIRMED_AMOUNT, BuryOperation.CREATE, confirmedAmount.doubleValue());

        BigDecimal auditedAmount = arg.getObjectData().toObjectData().get(TPMDealerActivityCostFields.AUDITED_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        BuryService.asyncTpmAmountLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM_AMOUNT.AUDITED_AMOUNT, BuryOperation.CREATE, auditedAmount.doubleValue());
    }

    private void updateWriteOffSourceReferenceFieldValue(ObjectDataDocument data) {

        String id = data.getId();
        String activityId = (String) data.get(TPMDealerActivityCostFields.ACTIVITY_ID);
        Long beginDate = (Long) data.get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endDate = (Long) data.get(TPMDealerActivityCostFields.END_DATE);


        ActivityWriteOffSourceConfigEntity config = activityType.writeOffSourceConfig();
        if (config == null || Strings.isNullOrEmpty(config.getApiName())) {
            return;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setSearchSource("db");

        Filter activityIdFiler = new Filter();
        activityIdFiler.setFieldName(config.getReferenceActivityFieldApiName());
        activityIdFiler.setOperator(Operator.EQ);
        activityIdFiler.setFieldValues(Lists.newArrayList(activityId));

        Filter referenceWriteOffFilter = new Filter();
        referenceWriteOffFilter.setFieldName(config.getReferenceWriteOffFieldApiName());
        referenceWriteOffFilter.setOperator(Operator.IS);
        referenceWriteOffFilter.setFieldValues(Lists.newArrayList());

        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(CommonFields.CREATE_TIME);
        createTimeFilter.setOperator(Operator.BETWEEN);
        createTimeFilter.setFieldValues(Lists.newArrayList(beginDate.toString(), endDate.toString()));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(activityIdFiler, referenceWriteOffFilter, createTimeFilter, lifeStatusFilter));

        //store filter
        setStoreFilter(config, query);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        query.setOrders(Lists.newArrayList(order));

        List<String> fields = Lists.newArrayList(config.getReferenceWriteOffFieldApiName());
        List<String> queryFields = Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, config.getReferenceWriteOffFieldApiName());
        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(actionContext.getTenantId()), config.getApiName(), query, queryFields, 1000, sourceData -> {
            for (IObjectData datum : sourceData) {
                datum.set(config.getReferenceWriteOffFieldApiName(), id);
            }
            serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), sourceData, fields);
        });


    }

    private void validateIfAllowCreate(Arg arg) {
        if (TPMGrayUtils.skiProofCountCheckOnWriteOffAction(actionContext.getTenantId())) {
            return;
        }
        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        Long beginDate = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endDate = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE);

        ActivityWriteOffSourceConfigEntity config = activityType.writeOffSourceConfig();
        if (config == null || Strings.isNullOrEmpty(config.getApiName())) {
            return;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setSearchSource("db");

        Filter activityIdFiler = new Filter();
        activityIdFiler.setFieldName(config.getReferenceActivityFieldApiName());
        activityIdFiler.setOperator(Operator.EQ);
        activityIdFiler.setFieldValues(Lists.newArrayList(activityId));

        Filter referenceWriteOffFilter = new Filter();
        referenceWriteOffFilter.setFieldName(config.getReferenceWriteOffFieldApiName());
        referenceWriteOffFilter.setOperator(Operator.IS);
        referenceWriteOffFilter.setFieldValues(Lists.newArrayList());

        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(CommonFields.CREATE_TIME);
        createTimeFilter.setOperator(Operator.BETWEEN);
        createTimeFilter.setFieldValues(Lists.newArrayList(beginDate.toString(), endDate.toString()));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(activityIdFiler, referenceWriteOffFilter, createTimeFilter, lifeStatusFilter));

        //门店过滤
        setStoreFilter(config, query);

        if (serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), config.getApiName(), query).getTotalNumber() == 0) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_ADD_ACTION_1));
        }
    }

    private void setStoreFilter(ActivityWriteOffSourceConfigEntity config, SearchTemplateQuery query) {
        if (store != null) {
            Filter storeFilter = new Filter();
            storeFilter.setOperator(Operator.EQ);
            if (storeBusiness.findDealerRecordType(actionContext.getTenantId()).contains(store.getRecordType())) {
                storeFilter.setFieldName(config.getDealerFieldApiName());
                if (!Strings.isNullOrEmpty(config.getDealerFieldApiName())) {
                    storeFilter.setFieldValues(Lists.newArrayList(store.getId()));
                    query.getFilters().add(storeFilter);
                }
            } else {
                storeFilter.setFieldName(config.getAccountFieldApiName());
                if (!Strings.isNullOrEmpty(config.getAccountFieldApiName())) {
                    storeFilter.setFieldValues(Lists.newArrayList(store.getId()));
                    query.getFilters().add(storeFilter);
                }
            }
        }
    }

    private void validateDateRange(Arg arg) {
        arg.getObjectData().putIfAbsent(TPMDealerActivityCostFields.BEGIN_DATE, TimeUtils.MIN_DATE);
        arg.getObjectData().putIfAbsent(TPMDealerActivityCostFields.END_DATE, TimeUtils.MAX_DATE);
        if (Objects.equals(arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE), 0)) {
            arg.getObjectData().put(TPMDealerActivityCostFields.BEGIN_DATE, TimeUtils.MIN_DATE);
        }
        long begin = (long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE));
        if (!TPMGrayUtils.notResetEndDate(actionContext.getTenantId())) {
            arg.getObjectData().put(TPMDealerActivityCostFields.END_DATE, end);
        }

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.COST_TIME_RANGE_IS_ERROR));
        }
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {
        Result result = super.doAct(arg);
        String tenantId = actionContext.getTenantId();

        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), (String) result.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID), ApiNames.TPM_ACTIVITY_OBJ);
        String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        if (!Strings.isNullOrEmpty(budgetId)) {
            //budgetService.tryLockBudget(actionContext, budgetId);

            String userId = actionContext.getUser().getUpstreamOwnerIdOrUserId();
            if (TPMGrayUtils.excessDeductionForCost(tenantId)) {
                Map<String, Double> amountMap = budgetService.calculateActivity(tenantId, activity.getId());
                double activityAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_AMOUNT, String.class, "0"));
                double actualAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, String.class, "0"));
                arg.getObjectData().putIfAbsent(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, 0D);
                double calActivityAmount = amountMap.getOrDefault(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, 0D) + Double.parseDouble(arg.getObjectData().getOrDefault(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, "0").toString());

                if (CommonUtils.keepNDecimal(actualAmount - calActivityAmount, 3) != 0 && CommonUtils.keepNDecimal(activityAmount - calActivityAmount, 3) < 0) {
                    double incrementAmount;
                    if (activityAmount >= actualAmount) {
                        incrementAmount = calActivityAmount - activityAmount;
                    } else {
                        incrementAmount = calActivityAmount - actualAmount;
                    }

                    IObjectData lastDetail = budgetService.getLastBudgetDetail(tenantId, budgetId);
                    double beforeAmount = lastDetail.get(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, Double.class, 0D);
                    double afterAmount = beforeAmount - incrementAmount;

                    LogData logData = LogData.builder().data(JSON.toJSONString(arg)).build();
                    String logId = operateInfoService.log(tenantId, LogType.ADD.value(), JSON.toJSONString(logData), userId, ApiNames.TPM_DEALER_ACTIVITY_COST, result.getObjectData().getId(), false);

                    budgetService.addBudgetDetail(tenantId, userId,
                            "1",
                            budgetId,
                            String.format("「%s」活动申请超额核销", activity.getName()),//ignorei18n
                            -incrementAmount,
                            beforeAmount,
                            afterAmount,
                            System.currentTimeMillis(),
                            String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                            activity.getId(),
                            TraceContext.get().getTraceId(),
                            IdempotentArgBase.builder().idempotentKey(actionContext.getPostId()).build());
                }
                actionContext.setAttribute(String.format(BUDGET_KEY, actionContext.getPostId()), budgetId);
                actionContext.setAttribute(String.format(ACTIVITY_KEY, actionContext.getPostId()), activity.getId());
            }
        }

        return result;
    }

    private void setWriteOffStatus(Result result) {
        String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
        if (ObjectLifeStatus.UNDER_REVIEW.getCode().equals(lifeStatus)) {
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMDealerActivityCostFields.WRITE_OFF_STATUS, TPMDealerActivityCostFields.WriteOffStatus.TO_BE_WRITE_OFF);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
        }
    }

    private void setDefaultValue(Arg arg) {
        IObjectData argData = arg.getObjectData().toObjectData();
        arg.getObjectData().put(TPMDealerActivityCostFields.WRITE_OFF_STATUS, TPMDealerActivityCostFields.WriteOffStatus.PASS);
        List<ObjectDataDocument> detailObjectDataDocuments = arg.getDetails().get(ApiNames.TPM_DEALER_ACTIVITY_CASHING_PRODUCT_OBJ);
        if (!CollectionUtils.isEmpty(detailObjectDataDocuments)) {
            for (ObjectDataDocument detailObjectDataDocument : detailObjectDataDocuments) {
                String costCashingQuantity = (String) detailObjectDataDocument.get(TPMDealerActivityCashingProductFields.COST_CASHING_QUANTITY);
                Object activityCashingQuantity = detailObjectDataDocument.get(TPMDealerActivityCashingProductFields.ACTIVITY_CASHING_QUANTITY);
                if (StringUtils.isEmpty(costCashingQuantity)) {
                    detailObjectDataDocument.put(TPMDealerActivityCashingProductFields.COST_CASHING_QUANTITY, activityCashingQuantity);
                }
            }
        }


        //核销金额，默认赋值 0.00
        if (Objects.isNull(argData.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT))) {
            argData.set(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, BigDecimal.ZERO);
        }

        String storeId = argData.get(TPMDealerActivityCostFields.DEALER_ID, String.class);
        if (!Strings.isNullOrEmpty(storeId)) {
            store = serviceFacade.findObjectData(actionContext.getUser(), storeId, ApiNames.ACCOUNT_OBJ);
        }

        String activityType = argData.get(TPMDealerActivityCostFields.ACTIVITY_TYPE, String.class);
        if (StringUtils.isEmpty(activityType) || !isMongoId(activityType)) {
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), argData.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class), ApiNames.TPM_ACTIVITY_OBJ);
            activityType = activity.get(TPMDealerActivityCostFields.ACTIVITY_TYPE, String.class);
        }
        this.activityType = activityTypeManager.find(actionContext.getTenantId(), activityType);
        boolean openEnterAccount = dealerActivityCostEnterAccountService.isOpenEnterAccount(actionContext.getTenantId(), activityType);
        argData.set(TPMDealerActivityCostFields.ENTER_ACCOUNT, openEnterAccount);
        if (Boolean.TRUE.equals(openEnterAccount) && store == null) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_ADD_ACTION_2));
        }

        boolean autoEnterAccount = dealerActivityCostEnterAccountService.isAutoEnterAccount(actionContext.getTenantId(), activityType);
        if (autoEnterAccount) {
            argData.set(TPMDealerActivityCostFields.ENTER_ACCOUNT_WAY, TPMDealerActivityCostFields.ENTER_ACCOUNT_WAY__AUTO);
        } else {
            argData.set(TPMDealerActivityCostFields.ENTER_ACCOUNT_WAY, TPMDealerActivityCostFields.ENTER_ACCOUNT_WAY__ARTIFICIAL);
        }

        String dealerCashingType = argData.get(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, String.class);
        if (Objects.equals(TPMActivityCashingProductFields.GOODS, dealerCashingType)) {
            argData.set(TPMDealerActivityCostFields.CASH_USAGE, null);
        } else {
            argData.set(TPMDealerActivityCostFields.GOODS_PAY_USAGE, null);
        }
    }

    private boolean isMongoId(String activityType) {
        if (activityType == null) {
            return false;
        }
        //判断长度24且范围再0-9或a-f。 如果不满足走兼容逻辑查活动方案
        if (activityType.length() != 24) {
            return false;
        }
        boolean flag = true;
        for (char ch : activityType.toCharArray()) {
            if (!(ch >= '0' && ch <= '9') && !(ch >= 'a' && ch <= 'f')) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    private void tryLock() {
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID), ApiNames.TPM_ACTIVITY_OBJ);
        String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        if (!Strings.isNullOrEmpty(budgetId)) {
            budgetService.tryLockBudget(actionContext, budgetId);
        }
    }
}
