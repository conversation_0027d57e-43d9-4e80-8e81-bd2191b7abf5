package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetBusinessSubjectFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetActivitySubjectService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/7/18 上午11:29
 */
public class TPMBudgetActivitySubjectObjFlowCompletedAction extends StandardFlowCompletedAction {

    @Resource
    private IBudgetActivitySubjectService budgetActivitySubjectService = SpringUtil.getContext().getBean(IBudgetActivitySubjectService.class);


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if ("pass".equalsIgnoreCase(arg.getStatus())) {
            User systemUser = User.systemUser(actionContext.getTenantId());
            IObjectData data = serviceFacade.findObjectDataIgnoreAll(systemUser, arg.getDataId(), ApiNames.TPM_BUDGET_BUSINESS_SUBJECT);
            budgetActivitySubjectService.resetSubjectLevel(User.systemUser(actionContext.getTenantId()), data.getId(), data.get(TPMBudgetBusinessSubjectFields.SUBJECT_LEVEL, Integer.class, 1) + 1, Lists.newArrayList(data.getId()));
        }
    }
}
