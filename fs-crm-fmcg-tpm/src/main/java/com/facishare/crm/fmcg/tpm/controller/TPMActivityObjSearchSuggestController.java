package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.activity.SearchSuggest;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjSearchSuggestController extends PreDefineController<SearchSuggest.Arg, SearchSuggest.Result> {

    @Override
    protected SearchSuggest.Result doService(SearchSuggest.Arg arg) {
        SearchSuggest.Result result = new SearchSuggest.Result();
        if (Strings.isNullOrEmpty(arg.getKeyword()) || Strings.isNullOrEmpty(arg.getApiName())) {
            return result;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(51);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter keywordFilter = new Filter();
        keywordFilter.setFieldName(CommonFields.NAME);
        keywordFilter.setOperator(Operator.LIKE);
        keywordFilter.setFieldValues(Lists.newArrayList(arg.getKeyword()));

        query.setFilters(Lists.newArrayList(keywordFilter));

        if (ApiNames.TPM_ACTIVITY_OBJ.equals(arg.getApiName())) {
            arg.setRecordType("");
          /*  Filter closeFilter = new Filter();
            closeFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
            closeFilter.setOperator(Operator.NEQ);
            closeFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__CLOSED));
            query.getFilters().add(closeFilter);*/
        }

        if (!Strings.isNullOrEmpty(arg.getRecordType())) {
            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setOperator(Operator.EQ);
            recordTypeFilter.setFieldValues(Lists.newArrayList(arg.getRecordType()));
            query.getFilters().add(recordTypeFilter);
        }

        List<IObjectData> objectList = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), arg.getApiName(), query).getData();
        if (objectList.size() <= 50) {
            for (IObjectData datum : objectList) {
                result.getData().add(SearchSuggest.SuggestVO.builder().objectId(datum.getId()).id(datum.getId()).name(datum.getName()).build());
            }
        }
        return result;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
