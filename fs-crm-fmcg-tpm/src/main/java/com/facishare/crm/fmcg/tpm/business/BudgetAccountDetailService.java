package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.BudgetDetailOperateMark;
import com.facishare.crm.fmcg.tpm.business.enums.DetailStatus;
import com.facishare.crm.fmcg.tpm.business.enums.MainType;
import com.facishare.crm.fmcg.tpm.dao.paas.BudgetAccountDetailMapper;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/1 下午3:46
 */
@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class BudgetAccountDetailService implements IBudgetAccountDetailService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private BudgetAccountDetailMapper budgetAccountDetailMapper;

    @Resource
    private BudgetCalculateService budgetCalculateService;


    @Override
    public boolean existBudgetDetail(String tenantId, String budgetAccountId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");
        query.setNeedReturnQuote(false);

        Filter accountIdFilter = new Filter();
        accountIdFilter.setFieldName(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID);
        accountIdFilter.setOperator(Operator.EQ);
        accountIdFilter.setFieldValues(Lists.newArrayList(budgetAccountId));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        query.setFilters(Lists.newArrayList(accountIdFilter, detailStatusFilter));

        return !serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                User.systemUser(tenantId),
                ApiNames.TPM_BUDGET_ACCOUNT_DETAIL,
                query,
                Lists.newArrayList("_id")
        ).getData().isEmpty();
    }

    @Override
    public IObjectData add(User user, String budgetAccountId, MainType mainType, BizType bizType, BudgetDetailOperateMark operateMark, BigDecimal amount, IObjectData relatedObj, String bizTraceId, String approvalId) {
        return add(user, budgetAccountId, mainType, bizType, operateMark, amount, relatedObj, bizTraceId, approvalId, null);
    }

    @Override
    public IObjectData add(
            User user,
            String budgetAccountId,
            MainType mainType,
            BizType bizType,
            BudgetDetailOperateMark operateMark,
            BigDecimal amount,
            IObjectData relatedObj,
            String bizTraceId,
            String approvalId,
            String budgetFreezeDetailId) {

        IObjectData data = serviceFacade.saveObjectData(user, fromDetail(user, budgetAccountId, mainType, bizType, operateMark, amount, relatedObj, bizTraceId, approvalId, budgetFreezeDetailId));
        BuryService.asyncBudgetLog(user.getTenantId(), user.getUserIdInt(), BuryModule.Budget.BUDGET_ACCOUNT_DETAIL, BuryOperation.CREATE);
        return data;
    }

    @Override
    public IObjectData add(
            User user,
            String budgetAccountId,
            MainType mainType,
            BizType bizType,
            BigDecimal amount,
            IObjectData relatedObj,
            String bizTraceId,
            String approvalId) {
        return add(user, budgetAccountId, mainType, bizType, null, amount, relatedObj, bizTraceId, approvalId);
    }

    @Override
    public IObjectData add(
            User user,
            String budgetAccountId,
            MainType mainType,
            BizType bizType,
            BigDecimal amount,
            IObjectData relatedObj,
            String bizTraceId) {
        return add(user, budgetAccountId, mainType, bizType, amount, relatedObj, bizTraceId, null);
    }

    private String formatDescribe(BizType bizType, String relatedObjectName) {
        return String.format("[%s] %s", relatedObjectName, bizType.getI18Label());
    }

    @Override
    public BizType getTransferDetailBusinessType(String recordType, boolean isTransferOutBudget, boolean needFreeze) {

        switch (recordType) {
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_ADD:
                return isTransferOutBudget ? null : BizType.TRANSFER_APPEND;
            case TPMBudgetTransferDetailFields.RECORD_TYPE_BUDGET_DEDUCT:
                return isTransferOutBudget ? BizType.TRANSFER_DEDUCTION : null;
            case TPMBudgetTransferDetailFields.RECORD_TYPE_DEFAULT:
                return isTransferOutBudget ? BizType.TRANSFER_OUT : BizType.TRANSFER_IN;
            default:
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_DETAIL_SERVICE_0));
        }
    }

    @Override
    public boolean existsConsumeRuleDetailByRuleId(String tenantId, String ruleId) {
        return existsConsumeRuleDetailByObjectData(tenantId, ruleId, null, null);
    }

    @Override
    public boolean existsConsumeRuleDetailByObjectData(String tenantId, String ruleId, String objectApiName, String dataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");
        query.setLimit(1);

        Filter bizTraceIdFilter = new Filter();
        bizTraceIdFilter.setFieldName(TPMBudgetAccountDetailFields.BIZ_TRACE_ID);
        bizTraceIdFilter.setOperator(Operator.STARTWITH);
        bizTraceIdFilter.setFieldValues(Lists.newArrayList(String.format(TPMBudgetAccountDetailFields.BizCode.BUDGET_CONSUME_RULE.template(), ruleId, "")));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        query.setFilters(Lists.newArrayList(bizTraceIdFilter, detailStatusFilter));

        Filter objectApiNameFilter = new Filter();
        objectApiNameFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME);
        objectApiNameFilter.setOperator(Operator.EQ);
        objectApiNameFilter.setFieldValues(Lists.newArrayList(objectApiName));

        if (dataId != null) {
            Filter dataIdFilter = new Filter();
            dataIdFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID);
            dataIdFilter.setOperator(Operator.EQ);
            dataIdFilter.setFieldValues(Lists.newArrayList(dataId));
            query.getFilters().add(dataIdFilter);
        }

        return !serviceFacade.findBySearchQueryWithFieldsIgnoreAll(User.systemUser(tenantId), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query, Lists.newArrayList("_id")).getData().isEmpty();
    }

    @Override
    public List<IObjectData> queryDetailsByRelatedData(String tenantId, String apiName, String dataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        List<IFilter> filters = new ArrayList<>();

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(apiName));
        filters.add(apiNameFilter);

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(dataId));
        filters.add(idFilter);

        Filter noStatusFilter = new Filter();
        noStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        noStatusFilter.setOperator(Operator.IS);
        noStatusFilter.setFieldValues(Lists.newArrayList());
        filters.add(noStatusFilter);

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));
        filters.add(statusFilter);

        query.setPattern("1 and 2 and (3 or 4)");

        query.setFilters(filters);
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query);
    }

    @Override
    public List<IObjectData> queryFrozenDetailByRelateObjectWithoutUnfreeze(User user, String apiName, String dataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(apiName));

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(dataId));

        Filter mainTypeFilter = new Filter();
        mainTypeFilter.setFieldName(TPMBudgetAccountDetailFields.MAIN_TYPE);
        mainTypeFilter.setOperator(Operator.EQ);
        mainTypeFilter.setFieldValues(Lists.newArrayList(MainType.FREEZE.value()));

        Filter noStatusFilter = new Filter();
        noStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        noStatusFilter.setOperator(Operator.IS);
        noStatusFilter.setFieldValues(Lists.newArrayList());

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        Filter operateMarkFilter = new Filter();
        operateMarkFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkFilter.setOperator(Operator.NEQ);
        operateMarkFilter.setFieldValues(Lists.newArrayList(BudgetDetailOperateMark.COMPLETED_UNFREEZE.value()));

        Filter operateMarkNullFilter = new Filter();
        operateMarkNullFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkNullFilter.setOperator(Operator.IS);
        operateMarkNullFilter.setFieldValues(Lists.newArrayList());

        query.setPattern("1 and  2 and 3 and (4 or 5) and (6 or 7)");
        query.setFilters(Lists.newArrayList(apiNameFilter, idFilter, mainTypeFilter, noStatusFilter, statusFilter, operateMarkFilter, operateMarkNullFilter));
        return CommonUtils.queryData(serviceFacade, user, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query);
    }

    @Override
    public void updateOperateMark(User user, IObjectData detail, BudgetDetailOperateMark operateMark) {
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL, operateMark.value());
        serviceFacade.updateWithMap(user, detail, updateMap);
    }

    @Override
    public List<IObjectData> queryByBusinessType(User user, String budgetId, BizType bizType) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        Filter bizTypeFilter = new Filter();
        bizTypeFilter.setFieldName(TPMBudgetAccountDetailFields.BUSINESS_TYPE);
        bizTypeFilter.setOperator(Operator.EQ);
        bizTypeFilter.setFieldValues(Lists.newArrayList(bizType.value()));

        Filter budgetIdFilter = new Filter();
        budgetIdFilter.setFieldName(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID);
        budgetIdFilter.setOperator(Operator.EQ);
        budgetIdFilter.setFieldValues(Lists.newArrayList(budgetId));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        query.setFilters(Lists.newArrayList(bizTypeFilter, budgetIdFilter, detailStatusFilter));

        return CommonUtils.queryDataIgnoreAll(serviceFacade, user, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query);
    }

    @Override
    public String queryConsumeDetailBusinessId(User user, String relatedApiName, String relatedDataId, String consumeRuleId, boolean enableWithholding) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(relatedApiName));

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(relatedDataId));

        Filter mainTypeFilter = new Filter();
        mainTypeFilter.setFieldName(TPMBudgetAccountDetailFields.MAIN_TYPE);
        mainTypeFilter.setOperator(Operator.EQ);
        mainTypeFilter.setFieldValues(Lists.newArrayList(MainType.FREEZE.value()));

        Filter noStatusFilter = new Filter();
        noStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        noStatusFilter.setOperator(Operator.IS);
        noStatusFilter.setFieldValues(Lists.newArrayList());

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        String businessHeader = String.format(TPMBudgetAccountDetailFields.BizCode.BUDGET_CONSUME_RULE.template(), consumeRuleId, "");
        Filter businessIdFilter = new Filter();
        businessIdFilter.setFieldName(TPMBudgetAccountDetailFields.BIZ_TRACE_ID);
        businessIdFilter.setOperator(Operator.STARTWITH);
        businessIdFilter.setFieldValues(Lists.newArrayList(businessHeader));

        Filter businessTypeFilter = new Filter();
        businessTypeFilter.setFieldName(TPMBudgetAccountDetailFields.BUSINESS_TYPE);
        businessTypeFilter.setOperator(Operator.EQ);
        businessTypeFilter.setFieldValues(Lists.newArrayList(BizType.CONSUME.value()));

        query.setFilters(Lists.newArrayList(apiNameFilter, idFilter, mainTypeFilter, businessIdFilter, businessTypeFilter, noStatusFilter, statusFilter));
        query.setPattern(" 1 and 2 and 3 and 4 and 5 and (6 or 7)");
        query.setOrders(Lists.newArrayList(new OrderBy(TPMBudgetAccountDetailFields.OPERATE_TIME, false)));

        List<IObjectData> dataList = CommonUtils.queryDataIgnoreAll(serviceFacade, user, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query);
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("can not find frozen detail for queryConsumeDetailBusinessId.apiName:{},id:{},ruleId:{}", relatedApiName, relatedDataId, consumeRuleId);
            if (enableWithholding) {
                SearchTemplateQuery withholdingQuery = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                        SearchQueryUtil.filter(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_API_NAME, Operator.EQ, Lists.newArrayList(relatedApiName)),
                        SearchQueryUtil.filter(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_DATA_ID, Operator.EQ, Lists.newArrayList(relatedDataId)),
                        SearchQueryUtil.filter(TPMBudgetProvisionObjFields.BIZ_TRACE_ID, Operator.STARTWITH, Lists.newArrayList(businessHeader)),
                        SearchQueryUtil.filter(TPMBudgetProvisionObjFields.PROVISION_STATUS, Operator.NEQ, Lists.newArrayList(TPMBudgetProvisionObjFields.ProvisionStatus.INVALID))
                ));
                List<IObjectData> withholdList = CommonUtils.queryDataIgnoreAll(serviceFacade, user, ApiNames.TPM_BUDGET_PROVISION_OBJ, withholdingQuery);
                if (!CollectionUtils.isEmpty(withholdList)) {
                    return withholdList.get(0).get(TPMBudgetProvisionObjFields.BIZ_TRACE_ID, String.class);
                }
            }
            return null;
        }
        return dataList.get(0).get(TPMBudgetAccountDetailFields.BIZ_TRACE_ID, String.class);
    }

    @Override
    public Map<String, BigDecimal> queryFrozenAmountByBusinessId(User user, String businessId) {
        if (Strings.isNullOrEmpty(businessId)) {
            return new HashMap<>();
        }
        Map<String, BigDecimal> moneyMap = budgetAccountDetailMapper.queryAmountByBusinessId(user.getTenantId(), businessId);
        return moneyMap;
    }

    @MetadataTransactional
    @Override
    public List<IObjectData> batchAdd(User user, List<IObjectData> details) {
        if (details.isEmpty()) {
            return new ArrayList<>();
        }
        details.forEach(detail -> {
            if (!ApiNames.TPM_BUDGET_ACCOUNT_DETAIL.equals(detail.getDescribeApiName())) {
                throw new ValidateException("account detail describe is wrong.");
            }
            if (Strings.isNullOrEmpty(detail.get(TPMBudgetAccountDetailFields.BUSINESS_TYPE, String.class))) {
                throw new ValidateException("business type is empty.");
            }
            if (Strings.isNullOrEmpty(detail.get(TPMBudgetAccountDetailFields.MAIN_TYPE, String.class))) {
                throw new ValidateException("main type is empty.");
            }
            if (Strings.isNullOrEmpty(detail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class))) {
                throw new ValidateException("budgetAccountId is empty.");
            }
            if (Strings.isNullOrEmpty(detail.get(TPMBudgetAccountDetailFields.AMOUNT, String.class))) {
                throw new ValidateException("budgetAccountId is empty.");
            }
            if (Strings.isNullOrEmpty(detail.get(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID, String.class))) {
                throw new ValidateException("budgetAccountId is empty.");
            }
        });

        List<IObjectData> result = serviceFacade.bulkSaveObjectData(details, user);

        details.forEach(v -> BuryService.asyncBudgetLog(user.getTenantId(), user.getUserIdInt(), BuryModule.Budget.BUDGET_ACCOUNT_DETAIL, BuryOperation.CREATE));
        return result;
    }

    @Override
    public IObjectData fromDetail(User user, String budgetAccountId, MainType mainType, BizType bizType, BudgetDetailOperateMark operateMark, BigDecimal amount, IObjectData relatedObj, String bizTraceId, String approvalId, String budgetFreezeDetailId) {
        IObjectData detail = new ObjectData();

        // base information
        detail.setTenantId(user.getTenantId());
        detail.setRecordType("default__c");
        detail.setOwner(Lists.newArrayList(user.getUserId()));
        detail.set(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, budgetAccountId);

        detail.set(TPMBudgetAccountDetailFields.MAIN_TYPE, mainType.value());
        detail.set(TPMBudgetAccountDetailFields.BUSINESS_TYPE, bizType.value());
        detail.set(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL, operateMark == null ? null : operateMark.value());

        detail.set(TPMBudgetAccountDetailFields.AMOUNT, amount);

        detail.set(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME, relatedObj.getDescribeApiName());
        detail.set(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID, relatedObj.getId());
        detail.set(TPMBudgetAccountDetailFields.DESCRIBE, formatDescribe(bizType, relatedObj.getName()));

        detail.set(TPMBudgetAccountDetailFields.OPERATE_TIME, System.currentTimeMillis());

        detail.set(TPMBudgetAccountDetailFields.DETAIL_STATUS, DetailStatus.INCLUDE.value());

        detail.set(TPMBudgetAccountDetailFields.BIZ_TRACE_ID, bizTraceId);
        detail.set(TPMBudgetAccountDetailFields.APPROVAL_TRACE_ID, approvalId);
        detail.set(TPMBudgetAccountDetailFields.BUDGET_FREEZE_DETAIL_ID, budgetFreezeDetailId);

        detail.setDescribeApiName(ApiNames.TPM_BUDGET_ACCOUNT_DETAIL);
        return detail;
    }

    @Override
    public Map<String, String> getConsumeFrozenDetailIdByBusinessId(User user, String businessId, List<String> budgetIds) {
        List<Map> dataList = budgetAccountDetailMapper.queryConsumeFrozenDetailByBusinessId(user.getTenantId(), businessId, budgetIds);
        Map<String, String> resultMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(data -> resultMap.put((String) data.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID), (String) data.get("id")));
        }
        return resultMap;
    }

    @Override
    public List<IObjectData> queryDetailsByTraceIdAndBusinessId(User user, String businessId, String traceId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setOffset(0);
        query.setLimit(-1);
        query.setSearchSource("db");

        Filter traceIdFilter = new Filter();
        traceIdFilter.setFieldName(TPMBudgetAccountDetailFields.APPROVAL_TRACE_ID);
        traceIdFilter.setOperator(Operator.EQ);
        traceIdFilter.setFieldValues(Lists.newArrayList(traceId));

        Filter businessFilter = new Filter();
        businessFilter.setFieldName(TPMBudgetAccountDetailFields.BIZ_TRACE_ID);
        businessFilter.setOperator(Operator.EQ);
        businessFilter.setFieldValues(Lists.newArrayList(businessId));
        query.setFilters(Lists.newArrayList(traceIdFilter, businessFilter));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));
        query.getFilters().add(detailStatusFilter);

        return CommonUtils.queryAllDataInFields(serviceFacade, user, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query,
                Lists.newArrayList(CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.OWNER, TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL, TPMBudgetAccountDetailFields.AMOUNT, TPMBudgetAccountDetailFields.MAIN_TYPE, TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, CommonFields.TENANT_ID));
    }

    @Override
    public List<IObjectData> getAdvanceDetailByBusinessId(User user, String businessId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setOffset(0);
        query.setLimit(-1);
        query.setSearchSource("db");

        Filter operateMarkFilter = new Filter();
        operateMarkFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkFilter.setOperator(Operator.EQ);
        operateMarkFilter.setFieldValues(Lists.newArrayList(BudgetDetailOperateMark.OPERATE_BEFORE_APPROVAL.value()));

        Filter businessFilter = new Filter();
        businessFilter.setFieldName(TPMBudgetAccountDetailFields.BIZ_TRACE_ID);
        businessFilter.setOperator(Operator.EQ);
        businessFilter.setFieldValues(Lists.newArrayList(businessId));
        query.setFilters(Lists.newArrayList(operateMarkFilter, businessFilter));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));
        query.getFilters().add(detailStatusFilter);

        return CommonUtils.queryAllDataInFields(serviceFacade, user, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query, Lists.newArrayList(CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.OWNER, TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL, CommonFields.TENANT_ID));
    }

    @Override
    public int countDetailByTraceId(User user, String traceId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);


        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        Filter businessIdFilter = new Filter();
        businessIdFilter.setFieldName(TPMBudgetAccountDetailFields.APPROVAL_TRACE_ID);
        businessIdFilter.setOperator(Operator.EQ);
        businessIdFilter.setFieldValues(Lists.newArrayList(traceId));

        query.setFilters(Lists.newArrayList(businessIdFilter, statusFilter));


        return serviceFacade.countObjectDataFromDB(user.getTenantId(), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query);
    }

    @MetadataTransactional
    @Override
    public void deleteUselessBudgetDetail(String tenantId, List<String> detailIds) {
        List<IObjectData> details = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, detailIds, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL);
        User systemUser = User.systemUser(tenantId);
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL, BudgetDetailOperateMark.SCRIPT_DELETE.value());
        serviceFacade.batchUpdateWithMap(systemUser, details, updateMap);
        serviceFacade.bulkInvalid(details, systemUser);
        Set<String> ids = details.stream().map(v -> v.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class)).collect(Collectors.toSet());
        ids.forEach(id -> budgetCalculateService.recalculateBudgetAmount(systemUser, id));
    }
}
