package com.facishare.crm.fmcg.tpm.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IStoreCostService;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.domain.AddActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.trace.executor.MonitorTaskWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/6/30 下午2:58
 */
@Slf4j
@Service
@ServiceModule("storeWriteOff_button_action")
public class StoreWriteOffPluginService {

    @Resource
    private IStoreCostService storeCostService;

    @ServiceMethod("add_after")
    public AddActionDomainPlugin.Result after(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        log.info("storeWriteOff plugin after:{}", JSON.toJSONString(arg));
        String tenantId = serviceContext.getTenantId();
        IObjectData objectData = arg.getObjectData().toObjectData();
        if (objectData != null) {
            String lifeStatus = objectData.get(CommonFields.LIFE_STATUS, String.class);
            String id = objectData.getId();
            String objectApiName = objectData.getDescribeApiName();
            log.info("add store write off tenantId is {},objectApiName is {}, liftStatus is {}", tenantId, objectApiName, lifeStatus);
            if (CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
                ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() ->
                        storeCostService.addStoreWriteOffV2(tenantId, objectApiName, objectData))).run();
            }
        }
        return new AddActionDomainPlugin.Result();
    }
}
