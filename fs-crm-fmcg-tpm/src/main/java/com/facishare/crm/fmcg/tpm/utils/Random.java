package com.facishare.crm.fmcg.tpm.utils;

import com.google.common.collect.Lists;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class Random {
    public static List<Integer> random(int size, int bound) {
        List<Integer> randomList = Lists.newArrayList();
        Set<Integer> randomSets = new HashSet<>();
        java.util.Random random = new java.util.Random();
        for (int i = 0; i < size; i++) {
            int number = random.nextInt(bound);
            randomSets.add(number);
        }
        if (randomSets.size() < size) {
            int max = 0;
            do {
                ++max;
                for (int i = 0; i < size - randomSets.size(); i++) {
                    int number = random.nextInt(bound);
                    randomSets.add(number);
                }
                if (max > 100) {
                    break;
                }
            } while (randomSets.size() != size);
        }
        randomList.addAll(randomSets);
        return randomList;
    }
}
