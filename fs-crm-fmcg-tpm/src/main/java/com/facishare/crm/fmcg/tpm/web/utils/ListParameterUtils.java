package com.facishare.crm.fmcg.tpm.web.utils;

import com.facishare.crm.fmcg.tpm.web.contract.model.IListParameter;
import lombok.experimental.UtilityClass;

import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/24 17:06
 */
@UtilityClass
public class ListParameterUtils {

    public static final int ZERO = 0;
    public static final int DEFAULT_LIMIT = 20;
    public static final int ALL_DATA_DEFAULT_LIMIT = 10000;
    public static final int MAX_LIMIT = 100;

    public static void correctListParameter(IListParameter parameter) {
        correctListParameter(parameter, DEFAULT_LIMIT, MAX_LIMIT);
    }

    public static void correctListParameter(IListParameter parameter, int defaultLimit, int maxLimit) {
        if (Objects.isNull(parameter.getOffset())) {
            parameter.setOffset(ZERO);
        }
        if (Objects.isNull(parameter.getLimit()) || parameter.getLimit() > maxLimit) {
            parameter.setLimit(defaultLimit);
        }
        if (parameter.getLimit() == -1) {
            parameter.setLimit(ALL_DATA_DEFAULT_LIMIT);
        }
    }
}
