package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.web.contract.*;

/**
 * author: wuyx
 * description:
 * createTime: 2022/5/19 19:04
 */
public interface IActivityTypeDraftBoxService {

    AddActivityType.Result add(AddActivityType.Arg arg);

    EditActivityType.Result edit(EditActivityType.Arg arg);

    DeleteActivityType.Result delete(DeleteActivityType.Arg arg);

    GetActivityType.Result get(GetActivityType.Arg arg);

    ListActivityType.Result list(ListActivityType.Arg arg);
}
