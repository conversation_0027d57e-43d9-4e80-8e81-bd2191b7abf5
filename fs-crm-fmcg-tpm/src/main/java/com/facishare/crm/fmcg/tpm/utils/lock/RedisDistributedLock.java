package com.facishare.crm.fmcg.tpm.utils.lock;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.github.jedis.support.JedisCmd;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Slf4j
@Component("redisDistributedLock")
public class RedisDistributedLock implements DistributedLock {

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;

    @Resource
    private RedissonClient redissonCmd;

    public static final String KEY_PREF = "TPM_DISTRIBUTED_LOCK:";

    private static final int EXPIRE_TIME = 2 * 60 * 1000;
    public static final int ABS_SPAN = 100;

    @Override
    public boolean tryLock(String key, String identifier) {
        int absLimit = 200;
        while (!lock(key, identifier)) {
            try {
                Thread.sleep(ABS_SPAN);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
                throw new ValidateException("sleep fail.");
            }
            absLimit--;

            if (absLimit <= 0) {
                throw new ValidateException("lock fail.");
            }
        }
        return true;
    }

    @Override
    public boolean lock(String key, String identifier) {
        String lockKey = KEY_PREF + key;
        String result = redisCmd.set(lockKey, identifier, "NX", "PX", EXPIRE_TIME);
        return "OK".equalsIgnoreCase(result);
    }

    @Override
    public void unlock(String key, String identifier) {
        String lockKey = KEY_PREF + key;
        redisCmd.eval("if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end",
                Collections.singletonList(lockKey),
                Collections.singletonList(identifier));
    }

    @Override
    public <T, R> R executeByLock(String key, Function<T, R> function, T arg) {
        RLock lock = redissonCmd.getLock(key);

        try {
            if (lock.tryLock(40, 30, TimeUnit.SECONDS)) {
                return function.apply(arg);
            } else {
                throw new ValidateException("lock fail,key:" + key);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void executeByLock(String key, Runnable runnable) {
        RLock lock = redissonCmd.getLock(key);

        try {
            if (lock.tryLock(40, 30, TimeUnit.SECONDS)) {
                runnable.run();
            } else {
                throw new ValidateException("lock fail,key:" + key);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public <T, R> R executeOnlyOneThread(String key, Function<T, R> function, T arg) {
        RLock lock = redissonCmd.getLock(key);
        try {
            if (lock.tryLock()) {
                return function.apply(arg);
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return null;
    }
}
