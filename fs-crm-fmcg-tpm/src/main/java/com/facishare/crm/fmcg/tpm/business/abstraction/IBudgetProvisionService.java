package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

public interface IBudgetProvisionService {

    void occupyBudgetByProvision(String tenantId);

    void reuseOccupyBudget(IObjectData provisionObj);

    void cancelBudgetProvision(IObjectData provisionObj);

    boolean existsBudgetProvisionByRuleId(String tenantId, String ruleId);


}
