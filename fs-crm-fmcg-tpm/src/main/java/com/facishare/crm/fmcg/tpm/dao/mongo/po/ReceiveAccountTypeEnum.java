package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.paas.I18N;
import com.google.common.base.Strings;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author: linmj
 * Date: 2023/9/14 19:21
 */
public enum ReceiveAccountTypeEnum {

    MAIN_ACCOUNT("main_account", "本企业总账户","fmcg.reward_rule.receive_account_type.main_account"),
    STORE_DEALER("store_dealer", "门店销售商银联账户","fmcg.reward_rule.receive_account_type.store_dealer");

    ReceiveAccountTypeEnum(String code, String describe, String i18nKey) {
        this.code = code;
        this.describe = describe;
        this.i18nKey = i18nKey;
    }

    private static final Map<String, ReceiveAccountTypeEnum> CODE_MAP = Stream.of(values()).collect(Collectors.toMap(ReceiveAccountTypeEnum::code, rewardTypeEnum -> rewardTypeEnum));
    private String code;

    private String describe;

    private String i18nKey;

    public String code() {
        return this.code;
    }

    public String describe() {
        return this.describe;
    }

    public String i18nDescribe() {
        String text;
        if (this.i18nKey == null) {
            text = this.describe;
        } else {
            text = I18N.text(this.i18nKey);
            if (Strings.isNullOrEmpty(text)) {
                text = this.describe;
            }
        }
        return text;
    }

    public static ReceiveAccountTypeEnum get(String code) {
        return CODE_MAP.get(code);
    }
}
