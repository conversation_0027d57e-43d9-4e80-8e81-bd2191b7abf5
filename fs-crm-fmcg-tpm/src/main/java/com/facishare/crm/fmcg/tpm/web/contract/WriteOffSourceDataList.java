package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/1/5 18:33
 */
public interface WriteOffSourceDataList {

    @Data
    @ToString
    class Arg implements Serializable {

        private String id;

        private String keyword;

        @JSONField(name = "audit_status")
        @JsonProperty(value = "audit_status")
        @SerializedName("audit_status")
        private List<String> auditStatus;

        private int limit;

        private int offset;

        @JSONField(name = "need_return_describe_and_layout")
        @JsonProperty(value = "need_return_describe_and_layout")
        @SerializedName("need_return_describe_and_layout")
        private boolean needReturnDescribeAndLayout;

    }

    @Data
    @ToString
    class Result implements Serializable {

        @JSONField(name = "object_describe_map")
        @JsonProperty(value = "object_describe_map")
        @SerializedName("object_describe_map")
        private Map<String, ObjectDescribeDocument> objectDescribeMap = new HashMap<>();

        @JSONField(name = "layout_map")
        @JsonProperty(value = "layout_map")
        @SerializedName("layout_map")
        private Map<String, Map<String, LayoutDocument>> layoutMap = new HashMap<>();

        private List<WriteOffSourceDatum> data;

        @JSONField(name = "activity_type")
        @JsonProperty(value = "activity_type")
        @SerializedName("activity_type")
        private ActivityTypeVO activityType;

        private long total = 0;
    }

    @Data
    @ToString
    class WriteOffSourceDatum implements Serializable {

        @JSONField(name = "total_amount")
        @JsonProperty(value = "total_amount")
        @SerializedName("total_amount")
        private BigDecimal totalAmount;

        @JSONField(name = "total_audit_amount")
        @JsonProperty(value = "total_audit_amount")
        @SerializedName("total_audit_amount")
        private BigDecimal totalAuditAmount;

        @JSONField(name = "average_audit_amount")
        @JsonProperty(value = "average_audit_amount")
        @SerializedName("average_audit_amount")
        private BigDecimal averageAuditAmount;

        @JSONField(name = "main_object_data")
        @JsonProperty(value = "main_object_data")
        @SerializedName("main_object_data")
        private ObjectDataDocument mainObjectData;

        @JSONField(name = "data_list")
        @JsonProperty(value = "data_list")
        @SerializedName("data_list")
        private List<ObjectDataDocument> dataList = Lists.newArrayList();
    }
}
