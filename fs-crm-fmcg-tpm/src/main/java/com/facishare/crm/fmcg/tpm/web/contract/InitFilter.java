package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeOptionVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.OptionVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/24 17:39
 */
public interface InitFilter {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "activity_type_id")
        @JsonProperty(value = "activity_type_id")
        @SerializedName("activity_type_id")
        private String activityTypeId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "activity_type_options")
        @JsonProperty(value = "activity_type_options")
        @SerializedName("activity_type_options")
        private List<ActivityTypeOptionVO> activityTypeOptions;

        @JSONField(name = "dealer_options")
        @JsonProperty(value = "dealer_options")
        @SerializedName("dealer_options")
        private List<OptionVO> dealerOptions;

        @JSONField(name = "activity_options")
        @JsonProperty(value = "activity_options")
        @SerializedName("activity_options")
        private List<OptionVO> activityOptions;

        @JSONField(name = "is_show_random_audit_field")
        @JsonProperty(value = "is_show_random_audit_field")
        @SerializedName("is_show_random_audit_field")
        private Boolean isShowRandomAuditField;

        @JSONField(name = "is_advanced_filter_support")
        @JsonProperty(value = "is_advanced_filter_support")
        @SerializedName("is_advanced_filter_support")
        private Boolean isAdvancedFilterSupport;

        @JSONField(name = "default_time_span")
        @JsonProperty(value = "default_time_span")
        @SerializedName("default_time_span")
        private String defaultTimeSpan;
    }
}
