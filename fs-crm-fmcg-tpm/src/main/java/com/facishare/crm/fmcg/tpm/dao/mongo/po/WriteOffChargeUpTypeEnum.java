package com.facishare.crm.fmcg.tpm.dao.mongo.po;

/**
 * Author: linmj
 * Date: 2023/6/20 11:53
 */
public enum WriteOffChargeUpTypeEnum {

    AUTO("自动入账", "AUTO"),
    BY_HAND("手动入账", "BY_HAND");

    WriteOffChargeUpTypeEnum(String name, String code) {
        this.code = code;
        this.name = name;
    }

    private String name;
    private String code;

    public String code() {
        return this.code;
    }
}
