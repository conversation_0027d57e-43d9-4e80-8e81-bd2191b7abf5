package com.facishare.crm.fmcg.tpm.api.withdraw;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

public interface Dashboard {

    @Data
    @ToString
    class Arg implements Serializable {
    }


    @Data
    @Builder
    class Result implements Serializable {

        /**
         * 我的累计获得
         */
        @SerializedName("total_amount")
        @JSONField(name = "total_amount")
        @JsonProperty("total_amount")
        private BigDecimal totalAmount;

        /**
         * 今日获得
         */
        @SerializedName("today_amount")
        @JSONField(name = "today_amount")
        @JsonProperty("today_amount")
        private BigDecimal todayAmount;

        /**
         * 可提现余额
         */
        @SerializedName("available_amount")
        @JSONField(name = "available_amount")
        @JsonProperty("available_amount")
        private BigDecimal availableAmount;

        @SerializedName("is_available")
        @JSONField(name = "is_available")
        @JsonProperty("is_available")
        private Boolean isAvailable;

        @SerializedName("unusual_count")
        @JSONField(name = "unusual_count")
        @JsonProperty("unusual_count")
        private Integer unusualCount;

        @SerializedName("is_idCard_auth")
        @JSONField(name = "is_idCard_auth")
        @JsonProperty("is_idCard_auth")
        private Boolean isIdCredAuth;

        @SerializedName("is_need_confirm_agreement")
        @JSONField(name = "is_need_confirm_agreement")
        @JsonProperty("is_need_confirm_agreement")
        private Boolean isNeedConfirmAgreement;

        @SerializedName("alert_agreement_term")
        @JSONField(name = "alert_agreement_term")
        @JsonProperty("alert_agreement_term")
        private String alertAgreementTerm;

        @SerializedName("count_down_nums")
        @JSONField(name = "count_down_nums")
        @JsonProperty("count_down_nums")
        private Integer countdownNums;
    }
}