package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMActivityProofAuditObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        List<JSONObject> buttons = (List<JSONObject>) result.getLayout().get("buttons");
        List<String> disallowList = Lists.newArrayList("Import");
        if("list".equals(arg.getLayoutType())){
            disallowList.add("Add");
        }
        result.getLayout().put("buttons", buttons.stream().filter(v -> !disallowList.contains(v.getString("action"))).collect(Collectors.toList()));
        return super.after(arg, result);
    }
}
