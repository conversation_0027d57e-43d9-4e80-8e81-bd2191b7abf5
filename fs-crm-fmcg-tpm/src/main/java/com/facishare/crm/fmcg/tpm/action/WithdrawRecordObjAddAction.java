package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:09
 */
public class WithdrawRecordObjAddAction extends StandardAddAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException("add action not allowed!");
    }
}
