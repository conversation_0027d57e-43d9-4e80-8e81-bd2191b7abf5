package com.facishare.crm.fmcg.tpm.web.manager;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.LanguageReplaceWrapper;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetAccrualRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetNewConsumeRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.PaasReferenceService;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetDimensionVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetTypeNodeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ScopeVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.fmcg.framework.http.contract.reference.PaasReferenceBatchDelete;
import com.fmcg.framework.http.contract.reference.PaasReferenceCommonArg;
import com.fmcg.framework.http.contract.reference.PaasReferenceCreate;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/8/10 16:21
 */
//IgnoreI18nFile
@Component
@SuppressWarnings("Duplicates")
@Slf4j
public class BudgetTypeManager implements IBudgetTypeManager {

    public static final BudgetTypeNodeVO EMPTY_NODE = null;
    protected static final Map<String, Predicate<BudgetTypePO>> BUDGET_TYPE_FIELDS = Maps.newHashMap();
    protected static final Map<String, Predicate<BudgetTypePO>> BUDGET_NODE_FIELDS = Maps.newHashMap();
    protected static final Map<String, Integer> BUDGET_TIME_DIMENSION_ORDER_MAP = Maps.newHashMap();
    protected static final Set<String> OBJECT_API_NAME_LIST = Sets.newHashSet();
    protected static final Map<String, List<String>> RELATED_OBJECT_API_NAME_MAP = Maps.newHashMap();
    protected static final Map<String, String> OBJECT_BUDGET_TYPE_FIELD_MAP = Maps.newHashMap();
    protected static final Map<String, List<String>> RELATED_OBJECT_REFERENCE_FIELD_MAP = Maps.newHashMap();

    public static final Map<Integer, String> DEPARTMENT_LEVEL_LABEL_MAP = Maps.newHashMap();

    static {
        DEPARTMENT_LEVEL_LABEL_MAP.put(0, "fmcg_budget_level_全公司");
        DEPARTMENT_LEVEL_LABEL_MAP.put(1, "fmcg_budget_level_一");
        DEPARTMENT_LEVEL_LABEL_MAP.put(2, "fmcg_budget_level_二");
        DEPARTMENT_LEVEL_LABEL_MAP.put(3, "fmcg_budget_level_三");
        DEPARTMENT_LEVEL_LABEL_MAP.put(4, "fmcg_budget_level_四");
        DEPARTMENT_LEVEL_LABEL_MAP.put(5, "fmcg_budget_level_五");
        DEPARTMENT_LEVEL_LABEL_MAP.put(6, "fmcg_budget_level_六");
        DEPARTMENT_LEVEL_LABEL_MAP.put(7, "fmcg_budget_level_七");
        DEPARTMENT_LEVEL_LABEL_MAP.put(8, "fmcg_budget_level_八");
        DEPARTMENT_LEVEL_LABEL_MAP.put(9, "fmcg_budget_level_九");
        DEPARTMENT_LEVEL_LABEL_MAP.put(10, "fmcg_budget_level_十");
        DEPARTMENT_LEVEL_LABEL_MAP.put(11, "fmcg_budget_level_十一");
        DEPARTMENT_LEVEL_LABEL_MAP.put(12, "fmcg_budget_level_十二");
        DEPARTMENT_LEVEL_LABEL_MAP.put(13, "fmcg_budget_level_十三");
        DEPARTMENT_LEVEL_LABEL_MAP.put(14, "fmcg_budget_level_十四");
        DEPARTMENT_LEVEL_LABEL_MAP.put(15, "fmcg_budget_level_十五");

        BUDGET_TIME_DIMENSION_ORDER_MAP.put(BudgetPeriodEnum.MONTH.value(), 0);
        BUDGET_TIME_DIMENSION_ORDER_MAP.put(BudgetPeriodEnum.QUARTER.value(), 1);
        BUDGET_TIME_DIMENSION_ORDER_MAP.put(BudgetPeriodEnum.YEAR.value(), 2);

        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_ACCOUNT);
        OBJECT_BUDGET_TYPE_FIELD_MAP.put(ApiNames.TPM_BUDGET_ACCOUNT, TPMBudgetAccountFields.BUDGET_TYPE_ID);
        RELATED_OBJECT_API_NAME_MAP.put(ApiNames.TPM_BUDGET_ACCOUNT, Lists.newArrayList(
                ApiNames.TPM_BUDGET_ACCOUNT_DETAIL,
                ApiNames.TPM_BUDGET_TRANSFER_DETAIL,
                ApiNames.TPM_BUDGET_OCCUPATION_DETAIL
        ));
        RELATED_OBJECT_REFERENCE_FIELD_MAP.put(ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, Lists.newArrayList(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID));
        RELATED_OBJECT_REFERENCE_FIELD_MAP.put(ApiNames.TPM_BUDGET_TRANSFER_DETAIL, Lists.newArrayList(TPMBudgetTransferDetailFields.TRANSFER_IN_BUDGET_ACCOUNT_ID, TPMBudgetTransferDetailFields.TRANSFER_OUT_BUDGET_ACCOUNT_ID));
        RELATED_OBJECT_REFERENCE_FIELD_MAP.put(ApiNames.TPM_BUDGET_OCCUPATION_DETAIL, Lists.newArrayList(TPMBudgetOccupationDetailFields.BUDGET_ACCOUNT_ID));

        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_STATISTIC_TABLE);
        OBJECT_BUDGET_TYPE_FIELD_MAP.put(ApiNames.TPM_BUDGET_STATISTIC_TABLE, TPMBudgetStatisticTableFields.BUDGET_TYPE_ID);

        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_CARRY_FORWARD);
        OBJECT_BUDGET_TYPE_FIELD_MAP.put(ApiNames.TPM_BUDGET_CARRY_FORWARD, TPMBudgetCarryForwardFields.BUDGET_TYPE_ID);
        RELATED_OBJECT_API_NAME_MAP.put(ApiNames.TPM_BUDGET_CARRY_FORWARD, Lists.newArrayList(
                ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL
        ));
        RELATED_OBJECT_REFERENCE_FIELD_MAP.put(ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL, Lists.newArrayList(TPMBudgetCarryForwardDetailFields.BUDGET_CARRY_FORWARD_ID));

        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
        OBJECT_BUDGET_TYPE_FIELD_MAP.put(ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, TPMBudgetDisassemblyFields.BUDGET_TYPE_ID);
        RELATED_OBJECT_API_NAME_MAP.put(ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, Lists.newArrayList(
                ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ,
                ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ
        ));
        RELATED_OBJECT_REFERENCE_FIELD_MAP.put(ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ, Lists.newArrayList(TPMBudgetDisassemblyExistsDetailsFields.BUDGET_DISASSEMBLY_ID));
        RELATED_OBJECT_REFERENCE_FIELD_MAP.put(ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ, Lists.newArrayList(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DISASSEMBLY_ID));

        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_ACCRUAL_OBJ);
        OBJECT_BUDGET_TYPE_FIELD_MAP.put(ApiNames.TPM_BUDGET_ACCRUAL_OBJ, TPMBudgetAccrualFields.BUDGET_TYPE_ID);
        RELATED_OBJECT_API_NAME_MAP.put(ApiNames.TPM_BUDGET_ACCRUAL_OBJ, Lists.newArrayList(
                ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ
        ));
        RELATED_OBJECT_REFERENCE_FIELD_MAP.put(ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ, Lists.newArrayList(TPMBudgetAccrualDetailFields.ACCRUAL_ID));

        BUDGET_TYPE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_ACCOUNT, TPMBudgetAccountFields.BUDGET_TYPE_ID),
                type -> true);
        BUDGET_TYPE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_CARRY_FORWARD, TPMBudgetCarryForwardFields.BUDGET_TYPE_ID),
                type -> true);
        BUDGET_TYPE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, TPMBudgetDisassemblyFields.BUDGET_TYPE_ID),
                type -> true);
        BUDGET_TYPE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ, TPMBudgetDisassemblyNewDetailsFields.BUDGET_TYPE_ID),
                type -> true);
        BUDGET_TYPE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_STATISTIC_TABLE, TPMBudgetStatisticTableFields.BUDGET_TYPE_ID),
                type -> true);
        BUDGET_TYPE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_ACCRUAL_OBJ, TPMBudgetAccrualFields.BUDGET_TYPE_ID),
                type -> true);
        BUDGET_TYPE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ, TPMBudgetAccrualDetailFields.BUDGET_TYPE_ID),
                type -> true);

        BUDGET_NODE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_ACCOUNT, TPMBudgetAccountFields.BUDGET_NODE_ID),
                type -> true);
        BUDGET_NODE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_CARRY_FORWARD, TPMBudgetCarryForwardFields.BUDGET_NODE_ID),
                type -> true);
        BUDGET_NODE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, TPMBudgetDisassemblyFields.SOURCE_BUDGET_NODE_ID),
                type -> true);
        BUDGET_NODE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID),
                type -> true);
        BUDGET_NODE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ, TPMBudgetDisassemblyNewDetailsFields.BUDGET_NODE_ID),
                type -> true);
        BUDGET_NODE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_STATISTIC_TABLE, TPMBudgetStatisticTableFields.BUDGET_NODE_ID),
                type -> true);
    }

    @Resource
    private BudgetTypeDAO budgetTypeDAO;
    @Resource
    private BudgetNewConsumeRuleDAO budgetNewConsumeRuleDAO;
    @Resource
    private BudgetAccrualRuleDAO budgetAccrualRuleDAO;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource(name = "describeLogicService")
    private DescribeLogicService describeService;
    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;
    @Resource
    private PaasReferenceService paasReferenceService;
    @Resource
    private IObjectDescribeService objectDescribeService;

    @Override
    public void publishSyncBudgetTypeFieldTask(String tenantId) {
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            List<BudgetTypePO> types = budgetTypeDAO.all(tenantId, false);
            if (CollectionUtils.isEmpty(types)) {
                return;
            }
            for (Map.Entry<String, Predicate<BudgetTypePO>> entry : BUDGET_TYPE_FIELDS.entrySet()) {
                types = types.stream().filter(entry.getValue()).collect(Collectors.toList());
                this.updateBudgetTypeFieldDescribe(tenantId, types, entry.getKey());
            }
        })).run();
    }

    @Override
    public void publishSyncBudgetNodeFieldTask(String tenantId) {
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            List<BudgetTypePO> types = budgetTypeDAO.all(tenantId, false);
            List<BudgetTypeNodeEntity> nodes = Lists.newArrayList();
            for (BudgetTypePO type : types) {
                for (BudgetTypeNodeEntity node : type.getNodes()) {
                    node.setName(String.format("%s(%s)", node.getName(), type.getName()));
                    nodes.add(node);
                }
            }
            for (Map.Entry<String, Predicate<BudgetTypePO>> entry : BUDGET_NODE_FIELDS.entrySet()) {
                updateBudgetNodeFieldDescribe(tenantId, nodes, entry.getKey());
            }
        })).run();
    }

    private void updateBudgetNodeFieldDescribe(String tenantId, List<BudgetTypeNodeEntity> nodes, String key) {
        String[] arr = key.split("\\.");

        String apiName = arr[0];
        String fieldApiName = arr[1];

        IObjectDescribe describe = describeService.findObject(tenantId, apiName);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);

        Map<String, String> poMap = nodes.stream().collect(Collectors.toMap(BudgetTypeNodeEntity::getNodeId, BudgetTypeNodeEntity::getName));
        for (SelectOne field : describeExt.getSelectOneFields()) {
            if (field.getApiName().equals(fieldApiName)) {
                List<ISelectOption> options = field.getSelectOptions();
                Map<String, ISelectOption> oldOptionMap = options.stream().collect(Collectors.toMap(ISelectOption::getValue, Function.identity()));


                String optionsIdentity = toSelectOptionsIdentity(options);
                String recordTypesIdentity = toPoMapIdentity(poMap);

                if (!optionsIdentity.equals(recordTypesIdentity)) {
                    options.clear();
                    for (Map.Entry<String, String> entry : poMap.entrySet()) {
                        ISelectOption newOption = new SelectOption();
                        newOption.setLabel(entry.getValue());
                        newOption.setValue(entry.getKey());
                        if (oldOptionMap.containsKey(entry.getKey())) {
                            newOption.setChildOptions(oldOptionMap.get(entry.getKey()).getChildOptions());
                        }
                        options.add(newOption);
                    }
                    field.setSelectOptions(options);
                    try {
                        IObjectDescribe updateResult = objectDescribeService.updateFieldDescribe(describe, Lists.newArrayList(field), this.getActionContext());

                        log.info("update field : {}, update result : {}", field.toJsonString(), updateResult.toJsonString());
                    } catch (Exception ex) {

                        // 非常严重的错误，更新预算模版的 select one 发生了错误
                        log.error("update budget type select one field error : ", ex);
                    }
                }
            }
        }
    }

    @Override
    public boolean isUsed(String tenantId, String id) {
        return isUsedByBudgetAccount(tenantId, id)
                || isUsedByConsumeRule(tenantId, id)
                || isUsedByAccrualRule(tenantId, id);
    }

    private boolean isUsedByBudgetAccount(String tenantId, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        Filter typeIdFilter = new Filter();
        typeIdFilter.setFieldName(TPMBudgetAccountFields.BUDGET_TYPE_ID);
        typeIdFilter.setOperator(Operator.EQ);
        typeIdFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(typeIdFilter));

        IActionContext context = ActionContextUtil.getNewContext(tenantId);
        context.setPrivilegeCheck(false);

        return CollectionUtils.isNotEmpty(serviceFacade.findBySearchTemplateQueryWithFields(context, ApiNames.TPM_BUDGET_ACCOUNT, query, Lists.newArrayList("_id")).getData());
    }

    private boolean isUsedByConsumeRule(String tenantId, String id) {
        //判断预算类型是否被消费规则使用。
        return budgetNewConsumeRuleDAO.isUsedByConsumeBudgetType(tenantId, id).size() > 0;
    }

    private boolean isUsedByAccrualRule(String tenantId, String id) {
        //判断预算类型是否被计提规则使用。
        return budgetAccrualRuleDAO.isUsedByAccrualBudgetType(tenantId, id).size() > 0;
    }

    @Override
    public List<BudgetTypeNodeEntity> convertToNodesWhenUnused(String tenantId, BudgetTypeNodeVO root) {
        List<BudgetTypeNodeEntity> data = Lists.newArrayList();
        searchNodesWhenUnused(tenantId, EMPTY_NODE, Lists.newArrayList(root), data);
        if (data.size() > 5) {
            throw new ValidateException("the budget level can only support 5 levels at most.");
        }
        return data;
    }

    public List<BudgetTypeNodeEntity> convertToNodesWhenUsed(BudgetTypeNodeVO root, List<BudgetTypeNodeEntity> old) {
        Map<String, BudgetTypeNodeEntity> nodeMap = old.stream().collect(Collectors.toMap(BudgetTypeNodeEntity::getNodeId, v -> v));
        searchNodesWhenUsed(EMPTY_NODE, Lists.newArrayList(root), nodeMap);
        return old;
    }

    @Override
    public BudgetTypeNodeVO convertToRootNodeVO(List<BudgetTypeNodeEntity> nodes) {
        Map<String, List<BudgetTypeNodeEntity>> parentMap = nodes.stream().collect(Collectors.groupingBy(BudgetTypeNodeEntity::getParentNodeId));
        List<BudgetTypeNodeEntity> poList = parentMap.get("");
        if (CollectionUtils.isEmpty(poList) || poList.size() > 1) {
            return null;
        }
        BudgetTypeNodeEntity po = poList.get(0);
        BudgetTypeNodeVO root = BudgetTypeNodeEntity.toVO(po);
        buildNodes(Lists.newArrayList(root), parentMap);
        return root;
    }

    @Override
    public BudgetTypePO get(String tenantId, String typeId) {
        return budgetTypeDAO.get(tenantId, typeId);
    }

    @Override
    public void delete(String tenantId, int employeeId, String typeId) {
        BudgetTypePO po = budgetTypeDAO.get(tenantId, typeId);
        budgetTypeDAO.delete(tenantId, employeeId, typeId);

        this.publishSyncBudgetTypeFieldTask(tenantId);
        this.publishSyncBudgetNodeFieldTask(tenantId);
        if (Objects.nonNull(po)) {
            this.publishSyncDeleteBudgetTypeReference(tenantId, po);
        }
    }

    @Override
    public void deleteObjectDataByTypeId(String tenantId, String typeId) {
        for (String apiName : OBJECT_API_NAME_LIST) {
            deleteNormalData(tenantId, apiName, typeId);
        }
        for (String apiName : OBJECT_API_NAME_LIST) {
            deleteInvalidData(tenantId, apiName, typeId);
        }
    }

    private void deleteInvalidData(String tenantId, String apiName, String typeId) {
        List<IObjectData> data = queryInvalidMasterData(tenantId, apiName, typeId);
        deleteInvalidData(tenantId, apiName, data);
    }

    private void deleteNormalData(String tenantId, String apiName, String typeId) {
        List<IObjectData> data = queryNormalMasterData(tenantId, apiName, typeId);
        invalidAndDeleteNormalData(tenantId, apiName, data);
    }

    private void deleteInvalidData(String tenantId, String apiName, List<IObjectData> data) {
        List<List<IObjectData>> partitions = Lists.partition(data, 50);
        User sys = User.systemUser(tenantId);
        for (List<IObjectData> partition : partitions) {
            serviceFacade.bulkDelete(partition, sys);
            if (RELATED_OBJECT_API_NAME_MAP.containsKey(apiName)) {
                List<String> masterIds = partition.stream().map(DBRecord::getId).collect(Collectors.toList());
                for (String relatedObjectApiName : RELATED_OBJECT_API_NAME_MAP.get(apiName)) {
                    for (String referenceFieldApiName : RELATED_OBJECT_REFERENCE_FIELD_MAP.get(relatedObjectApiName)) {
                        List<IObjectData> details = queryInvalidDetailData(tenantId, relatedObjectApiName, referenceFieldApiName, masterIds);
                        deleteInvalidDetailData(tenantId, details);
                    }
                }
            }
        }
    }

    private void invalidAndDeleteNormalData(String tenantId, String apiName, List<IObjectData> data) {
        List<List<IObjectData>> partitions = Lists.partition(data, 50);
        User sys = User.systemUser(tenantId);
        for (List<IObjectData> partition : partitions) {
            serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(partition, sys);
            if (RELATED_OBJECT_API_NAME_MAP.containsKey(apiName)) {
                List<String> masterIds = partition.stream().map(DBRecord::getId).collect(Collectors.toList());
                for (String relatedObjectApiName : RELATED_OBJECT_API_NAME_MAP.get(apiName)) {
                    for (String referenceFieldApiName : RELATED_OBJECT_REFERENCE_FIELD_MAP.get(relatedObjectApiName)) {
                        List<IObjectData> details = queryNormalDetailData(tenantId, relatedObjectApiName, referenceFieldApiName, masterIds);
                        invalidAndDeleteNormalDetailData(tenantId, details);
                    }
                }
            }
        }
    }

    private void deleteInvalidDetailData(String tenantId, List<IObjectData> data) {
        List<List<IObjectData>> partitions = Lists.partition(data, 50);
        User sys = User.systemUser(tenantId);
        for (List<IObjectData> partition : partitions) {
            serviceFacade.bulkDelete(partition, sys);
        }
    }

    private void invalidAndDeleteNormalDetailData(String tenantId, List<IObjectData> data) {
        List<List<IObjectData>> partitions = Lists.partition(data, 50);
        User sys = User.systemUser(tenantId);
        for (List<IObjectData> partition : partitions) {
            serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(partition, sys);
        }
    }

    private List<IObjectData> queryNormalDetailData(String tenantId, String apiName, String referenceFieldApiName, List<String> masterIds) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);

        Filter typeFilter = new Filter();
        typeFilter.setFieldName(referenceFieldApiName);
        typeFilter.setOperator(Operator.IN);
        typeFilter.setFieldValues(masterIds);

        Filter delFilter = new Filter();
        delFilter.setFieldName(CommonFields.IS_DELETED);
        delFilter.setOperator(Operator.EQ);
        delFilter.setFieldValues(Lists.newArrayList("false"));

        stq.setFilters(Lists.newArrayList(delFilter, typeFilter));

        List<String> fields = Lists.newArrayList("_id", "object_describe_api_name", "tenant_id");
        return QueryDataUtil.find(serviceFacade, tenantId, apiName, stq, fields);
    }

    private List<IObjectData> queryInvalidDetailData(String tenantId, String apiName, String referenceFieldApiName, List<String> masterIds) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);

        Filter typeFilter = new Filter();
        typeFilter.setFieldName(referenceFieldApiName);
        typeFilter.setOperator(Operator.IN);
        typeFilter.setFieldValues(masterIds);

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(CommonFields.LIFE_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__INVALID));

        Filter delFilter = new Filter();
        delFilter.setFieldName(CommonFields.IS_DELETED);
        delFilter.setOperator(Operator.EQ);
        delFilter.setFieldValues(Lists.newArrayList("true"));
        stq.setFilters(Lists.newArrayList(statusFilter, delFilter));

        stq.setFilters(Lists.newArrayList(delFilter, statusFilter, typeFilter));

        List<String> fields = Lists.newArrayList("_id", "object_describe_api_name", "tenant_id");
        return QueryDataUtil.find(serviceFacade, tenantId, apiName, stq, fields);
    }

    private List<IObjectData> queryInvalidMasterData(String tenantId, String apiName, String typeId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);

        Filter typeFilter = new Filter();
        typeFilter.setFieldName(OBJECT_BUDGET_TYPE_FIELD_MAP.get(apiName));
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList(typeId));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(CommonFields.LIFE_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__INVALID));

        Filter delFilter = new Filter();
        delFilter.setFieldName(CommonFields.IS_DELETED);
        delFilter.setOperator(Operator.EQ);
        delFilter.setFieldValues(Lists.newArrayList("true"));
        stq.setFilters(Lists.newArrayList(statusFilter, delFilter));

        stq.setFilters(Lists.newArrayList(delFilter, statusFilter, typeFilter));

        List<String> fields = Lists.newArrayList("_id", "object_describe_api_name", "tenant_id");
        return QueryDataUtil.find(serviceFacade, tenantId, apiName, stq, fields);
    }

    private List<IObjectData> queryNormalMasterData(String tenantId, String apiName, String typeId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);

        Filter typeFilter = new Filter();
        typeFilter.setFieldName(OBJECT_BUDGET_TYPE_FIELD_MAP.get(apiName));
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList(typeId));

        Filter delFilter = new Filter();
        delFilter.setFieldName(CommonFields.IS_DELETED);
        delFilter.setOperator(Operator.EQ);
        delFilter.setFieldValues(Lists.newArrayList("false"));

        stq.setFilters(Lists.newArrayList(delFilter, typeFilter));

        List<String> fields = Lists.newArrayList("_id", "object_describe_api_name", "tenant_id");
        return QueryDataUtil.find(serviceFacade, tenantId, apiName, stq, fields);
    }

    @Override
    public BudgetTypeNodeEntity getNode(String tenantId, String typeId, String nodeId) {
        BudgetTypePO type = budgetTypeDAO.get(tenantId, typeId);
        if (Objects.isNull(type)) {
            throw new ValidateException("budget type not found.");
        }
        BudgetTypeNodeEntity node = type.getNodes().stream().filter(f -> f.getNodeId().equals(nodeId)).findFirst().orElse(null);
        if (Objects.isNull(node)) {
            throw new ValidateException("budget node not found");
        }
        return node;
    }

    @Override
    public List<BudgetTypePO> query(String tenantId, List<String> typeIds) {
        return budgetTypeDAO.query(tenantId, typeIds);
    }

    @Override
    public void basicInformationValidation(String tenantId, BudgetTypePO type) {
        statusValidation(type.getStatus());
        nameValidation(type.getName());
        if (Objects.isNull(type.getId())) {
            duplicateNameValidation(tenantId, type.getName());
            if (!TPMGrayUtils.skipBudgetTypeDepartmentCheck(tenantId)) {
                departmentRangeValidation(tenantId, "", type.getDepartmentRange());
            }
        } else {
            duplicateNameValidation(tenantId, type.getId().toString(), type.getName());
            if (!TPMGrayUtils.skipBudgetTypeDepartmentCheck(tenantId)) {
                departmentRangeValidation(tenantId, type.getId().toString(), type.getDepartmentRange());
            }
        }
    }

    @Override
    public void editDepartmentRangeValidate(String tenantId, List<Integer> newRange, List<Integer> oldRange) {
        if (TPMGrayUtils.newBudgetTypeDepartmentRangeValidate(tenantId)) {
            newEditDepartmentRangeValidate(newRange);
        } else {
            oldEditDepartmentRangeValidate(newRange, oldRange);
        }
    }

    private void newEditDepartmentRangeValidate(List<Integer> newRange) {
        if (CollectionUtils.isEmpty(newRange)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_MANAGER_0));
        }
    }

    private void oldEditDepartmentRangeValidate(List<Integer> newRange, List<Integer> oldRange) {
        if (CollectionUtils.isEmpty(newRange)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_MANAGER_1));
        }
        if (oldRange.stream().anyMatch(range -> !newRange.contains(range))) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_MANAGER_2));
        }
    }

    @Override
    public int maximumDepartmentLevel(String tenantId, List<Integer> departmentIds) {
        if (!CollectionUtils.isEmpty(departmentIds)) {
            List<DepartmentDto> departments = organizationService.batchGetDepartment(Integer.parseInt(tenantId), departmentIds);
            return departments.stream().mapToInt(d -> d.getAncestors().size()).min().orElse(0);
        } else {
            return 0;
        }
    }

    @Override
    public void publishSyncCreateBudgetTypeReference(String tenantId, BudgetTypePO po) {
        List<String> apiNames = getIdentityDimensions(po);
        if (CollectionUtils.isEmpty(apiNames)) {
            return;
        }
        createReference(tenantId, po, apiNames);
    }

    @NotNull
    private List<String> getIdentityDimensions(BudgetTypePO po) {
        List<BudgetTypeNodeEntity> nodes = po.getNodes();
        List<String> dimensionsApiNames = Lists.newArrayList();
        Optional.of(nodes).get().forEach(budgetTypeNodePo -> {
            dimensionsApiNames.addAll(budgetTypeNodePo.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList()));
            dimensionsApiNames.addAll(budgetTypeNodePo.getControlDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList()));
        });
        return dimensionsApiNames.stream().distinct().filter(s -> s.endsWith("__c")).collect(Collectors.toList());
    }

    @Override
    public void publishSyncEditBudgetTypeReference(String tenantId, BudgetTypePO oldBudgetType, BudgetTypePO newBudgetType) {
        List<String> oldApiNames = getIdentityDimensions(oldBudgetType);
        List<String> newApiNames = getIdentityDimensions(newBudgetType);

        List<String> needCreateApiNames = newApiNames.stream().filter(s -> !oldApiNames.contains(s)).collect(Collectors.toList());
        createReference(tenantId, newBudgetType, needCreateApiNames);

        List<String> needDeleteApiNames = oldApiNames.stream().filter(s -> !newApiNames.contains(s)).collect(Collectors.toList());
        deleteReference(tenantId, oldBudgetType, needDeleteApiNames);
    }

    @Override
    public void publishSyncDeleteBudgetTypeReference(String tenantId, BudgetTypePO po) {

        List<String> apiNames = getIdentityDimensions(po);
        if (CollectionUtils.isEmpty(apiNames)) {
            return;
        }

        deleteReference(tenantId, po, apiNames);
    }

    private void deleteReference(String tenantId, BudgetTypePO po, List<String> apiNames) {
        List<PaasReferenceBatchDelete.BatchDeleteArg> reference = Lists.newArrayList();
        for (String apiName : apiNames) {
            reference.add(getBatchDeleteReferenceArg(tenantId, po, apiName, ApiNames.TPM_BUDGET_ACCOUNT));
            reference.add(getBatchDeleteReferenceArg(tenantId, po, apiName, ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ));
            reference.add(getBatchDeleteReferenceArg(tenantId, po, apiName, ApiNames.TPM_BUDGET_STATISTIC_TABLE));
        }

        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(reference)) {
            paasReferenceService.deleteReferenceAll(tenantId, reference);
        }
    }

    @NotNull
    private PaasReferenceBatchDelete.BatchDeleteArg getBatchDeleteReferenceArg(String tenantId, BudgetTypePO po, String apiName, String objApiName) {
        PaasReferenceBatchDelete.BatchDeleteArg referenceCommonArg = new PaasReferenceBatchDelete.BatchDeleteArg();
        referenceCommonArg.setSourceType("BudgetDesigner");
        referenceCommonArg.setSourceValue(String.format("%s--%s--%s", tenantId, po.getApiName(), apiName));
        referenceCommonArg.setTargetType("Describe.Field");
        referenceCommonArg.setTargetValue(String.format("%s.%s", objApiName, apiName));
        return referenceCommonArg;
    }

    private void createReference(String tenantId, BudgetTypePO po, List<String> apiNames) {
        List<PaasReferenceCommonArg> reference = Lists.newArrayList();
        for (String apiName : apiNames) {
            reference.add(getPaasReferenceCommonArg(tenantId, po, apiName, ApiNames.TPM_BUDGET_ACCOUNT));
            reference.add(getPaasReferenceCommonArg(tenantId, po, apiName, ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ));
            reference.add(getPaasReferenceCommonArg(tenantId, po, apiName, ApiNames.TPM_BUDGET_STATISTIC_TABLE));
        }

        if (CollectionUtils.isNotEmpty(reference)) {
            PaasReferenceCreate.Arg referenceCreate = new PaasReferenceCreate.Arg();
            referenceCreate.setItems(reference);
            paasReferenceService.createReferenceAll(tenantId, referenceCreate);
        }
    }

    @NotNull
    private PaasReferenceCommonArg getPaasReferenceCommonArg(String tenantId, BudgetTypePO po, String apiName, String objApiName) {
        PaasReferenceCommonArg referenceCommonArg = new PaasReferenceCommonArg();
        referenceCommonArg.setSourceType("BudgetDesigner");
        referenceCommonArg.setSourceLabel(String.format("预算管理.预算类型[%s]", po.getName().replace(",", "").trim()));
        referenceCommonArg.setSourceValue(String.format("%s--%s--%s", tenantId, po.getApiName(), apiName));
        referenceCommonArg.setTargetType("Describe.Field");
        referenceCommonArg.setTargetValue(String.format("%s.%s", objApiName, apiName));
        return referenceCommonArg;
    }


    private void statusValidation(String status) {
        if (Arrays.stream(StatusType.values()).noneMatch(n -> n.value().equals(status))) {
            throw new ValidateException("status value not exists.");
        }
    }

    private void nameValidation(String name) {
        if (Strings.isNullOrEmpty(name) ||
                name.length() < 2 ||
                name.length() > 200) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_NAME_FORMAT_ERROR));
        }
    }

    private void departmentRangeValidation(String tenantId, String id, List<Integer> currentDepartmentIds) {
        if (CollectionUtils.isEmpty(currentDepartmentIds)) {
            throw new ValidateException("department range can not be null or empty.");
        }

        List<BudgetTypePO> types = budgetTypeDAO.all(tenantId, false).stream().filter(f -> !id.equals(f.getId().toString())).collect(Collectors.toList());
        Map<String, String> typeNameMap = types.stream().collect(Collectors.toMap(k -> k.getId().toString(), BudgetTypePO::getName));

        Set<Integer> allDepartmentIds = Sets.newHashSet();
        Map<String, Map<Integer, Set<Integer>>> typeRelatedDepartmentIdsMap = Maps.newHashMap();

        for (BudgetTypePO type : types) {
            Map<Integer, Set<Integer>> typeRelatedDepartmentIds = Maps.newHashMap();
            for (int departmentId : type.getDepartmentRange()) {
                typeRelatedDepartmentIds.put(departmentId, queryLowerDepartmentIds(tenantId, departmentId));
                allDepartmentIds.add(departmentId);
            }
            typeRelatedDepartmentIdsMap.put(type.getId().toString(), typeRelatedDepartmentIds);
        }

        Map<Integer, Set<Integer>> currentRelatedDepartmentIdsMap = Maps.newHashMap();
        for (int currentDepartmentId : currentDepartmentIds) {
            currentRelatedDepartmentIdsMap.put(currentDepartmentId, queryLowerDepartmentIds(tenantId, currentDepartmentId));
            allDepartmentIds.add(currentDepartmentId);
        }

        List<DepartmentDto> departments = organizationService.batchGetDepartment(Integer.parseInt(tenantId), new ArrayList<>(allDepartmentIds));
        Map<Integer, String> departmentNameMap = departments.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::getName));

        for (int departmentId : currentDepartmentIds) {
            Set<Integer> currentRelatedDepartmentIds = currentRelatedDepartmentIdsMap.get(departmentId);

            for (Map.Entry<String, Map<Integer, Set<Integer>>> typeEntry : typeRelatedDepartmentIdsMap.entrySet()) {
                for (Map.Entry<Integer, Set<Integer>> departmentEntry : typeEntry.getValue().entrySet()) {
                    if (departmentEntry.getValue().contains(departmentId) || currentRelatedDepartmentIds.contains(departmentEntry.getKey())) {
                        throw new ValidateException(String.format("所选适用部门组织[%s]与预算类型[%s]下适用部门组织[%s]存在冲突，请重新选择后再次提交。",
                                departmentNameMap.getOrDefault(departmentId, "--"),
                                typeNameMap.getOrDefault(typeEntry.getKey(), "--"),
                                departmentNameMap.getOrDefault(departmentEntry.getKey(), "--")
                        ));
                    }
                }
            }

            for (Map.Entry<Integer, Set<Integer>> departmentEntry : currentRelatedDepartmentIdsMap.entrySet()) {
                if (departmentId != departmentEntry.getKey() && (departmentEntry.getValue().contains(departmentId) || currentRelatedDepartmentIds.contains(departmentEntry.getKey()))) {
                    throw new ValidateException(String.format("所选适用部门组织[%s]与[%s]存在冲突，请重新选择后再次提交。",
                            departmentNameMap.getOrDefault(departmentId, "--"),
                            departmentNameMap.getOrDefault(departmentEntry.getKey(), "--")
                    ));
                }
            }
        }
    }

    private Set<Integer> queryLowerDepartmentIds(String tenantId, int departmentId) {
        List<DepartmentDto> departments = organizationService.getChildrenDepartment(Integer.parseInt(tenantId), departmentId);
        Set<Integer> ids = departments.stream().map(DepartmentDto::getDepartmentId).collect(Collectors.toSet());
        ids.add(departmentId);
        return ids;
    }

    private void duplicateNameValidation(String tenantId, String name) {
        if (budgetTypeDAO.isDuplicateName(tenantId, name)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_NAME_DUPLICATE_ERROR));
        }
    }

    private void duplicateNameValidation(String tenantId, String id, String name) {
        if (budgetTypeDAO.isDuplicateName(tenantId, id, name)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_NAME_DUPLICATE_ERROR));
        }
    }

    private void updateBudgetTypeFieldDescribe(String tenantId, List<BudgetTypePO> types, String key) {
        LanguageReplaceWrapper.doInChinese(() -> {
            try {
                String[] arr = key.split("\\.");

                String apiName = arr[0];
                String fieldApiName = arr[1];

                IObjectDescribe describe = describeService.findObject(tenantId, apiName);
                ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);

                Map<String, String> poMap = types.stream().collect(Collectors.toMap(k -> k.getId().toString(), BudgetTypePO::getName));
                for (SelectOne field : describeExt.getSelectOneFields()) {
                    if (field.getApiName().equals(fieldApiName)) {
                        List<ISelectOption> options = field.getSelectOptions();

                        String optionsIdentity = toSelectOptionsIdentity(options);
                        String recordTypesIdentity = toPoMapIdentity(poMap);

                        if (!optionsIdentity.equals(recordTypesIdentity)) {
                            options.clear();
                            for (Map.Entry<String, String> entry : poMap.entrySet()) {
                                ISelectOption newOption = new SelectOption();
                                newOption.setLabel(entry.getValue());
                                newOption.setValue(entry.getKey());
                                options.add(newOption);
                            }
                            field.setSelectOptions(options);

                            IObjectDescribe updateResult = objectDescribeService.updateFieldDescribe(describe, Lists.newArrayList(field), this.getActionContext());
                            clearDescribeCache(tenantId, apiName);
                            log.info("update field : {}, update result : {}", field.toJsonString(), updateResult.toJsonString());
                        }
                    }
                }
            } catch (ObjectDefNotFoundError objectDefNotFoundError) {
                log.info("describe not found.", objectDefNotFoundError);
            } catch (Exception ex) {

                // 非常严重的错误，更新预算模版的 select one 发生了错误
                log.error("update budget type select one field error : ", ex);
            }
        });
    }

    private void clearDescribeCache(String tenantId, String apiName) {
        NotifierClient.send("describe-extra-clear-room", String.format("%s_%s", tenantId, apiName));
    }

    private String toSelectOptionsIdentity(List<ISelectOption> selectOptions) {
        return selectOptions
                .stream()
                .sorted(Comparator.comparing(ISelectOption::getValue))
                .map(m -> String.format("%s.%s", m.getValue(), m.getLabel()))
                .collect(Collectors.joining(","));
    }

    private String toPoMapIdentity(Map<String, String> poMap) {
        return poMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> String.format("%s.%s", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining(","));
    }

    private boolean isChildNode(BudgetTypeNodeVO parent, BudgetTypeNodeVO child) {
        int theSameCount = 0;

        if (parent.getDepartmentDimensionLevel() > child.getDepartmentDimensionLevel()) {
            return false;
        } else if (parent.getDepartmentDimensionLevel() == child.getDepartmentDimensionLevel()) {
            theSameCount++;
        }

        int parentTimeOrder = BUDGET_TIME_DIMENSION_ORDER_MAP.get(parent.getTimeDimension());
        int childTimeOrder = BUDGET_TIME_DIMENSION_ORDER_MAP.get(child.getTimeDimension());
        if (parentTimeOrder < childTimeOrder) {
            return false;
        } else if (parentTimeOrder == childTimeOrder) {
            theSameCount++;
        }

        Map<String, BudgetDimensionVO> childDimensionMap = CollectionUtils.isEmpty(child.getDimensions()) ? new HashMap<>() : child.getDimensions().stream().collect(Collectors.toMap(BudgetDimensionVO::getApiName, v -> v));
        if (!CollectionUtils.isEmpty(parent.getDimensions())) {
            for (BudgetDimensionVO dimensionEntity : parent.getDimensions()) {
                BudgetDimensionVO childDimension = childDimensionMap.get(dimensionEntity.getApiName());
                //不存在或则层级小于父
                if (childDimension == null || childDimension.getLevel() < dimensionEntity.getLevel()) {
                    return false;
                }
                if (childDimension.getLevel() == dimensionEntity.getLevel()) {
                    theSameCount++;
                }
            }
            //不完全相等
            return theSameCount < childDimensionMap.values().size() + 2;
        }
        //时间、部门是子集 或则 时间、部门相等 但是子维度不为空
        return theSameCount < 2 || !CollectionUtils.isEmpty(child.getDimensions());
    }

    private void searchNodesWhenUsed(
            BudgetTypeNodeVO parentNode,
            List<BudgetTypeNodeVO> nodes,
            Map<String, BudgetTypeNodeEntity> nodeMap) {
        for (BudgetTypeNodeVO node : nodes) {
            BudgetTypeNodeEntity old = nodeMap.get(node.getNodeId());
            if (Objects.nonNull(parentNode) && !node.getParentNodeId().equals(parentNode.getNodeId())) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_MANAGER_3));
            }
            if (Objects.isNull(old)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_MANAGER_4));
            }
            old.setName(node.getName());
            old.setDescription(node.getDescription());
            old.setAutomaticRecoveryAfterDeduct(node.getAutomaticRecoveryAfterDeduct());
            old.setAutomaticEnableAfterDisassembly(node.getAutomaticEnableAfterDisassembly());
            if (MapUtils.isNotEmpty(node.getPermissions())) {
                old.setPermissions(Maps.newHashMap());
                for (Map.Entry<String, ScopeVO> entry : node.getPermissions().entrySet()) {
                    old.getPermissions().put(entry.getKey(), ScopeEntity.fromVO(entry.getValue()));
                }
            }
            if (CollectionUtils.isNotEmpty(node.getNodes())) {
                searchNodesWhenUsed(node, node.getNodes(), nodeMap);
            }
        }
    }

    private void searchNodesWhenUnused(
            String tenantId,
            BudgetTypeNodeVO parentNode,
            List<BudgetTypeNodeVO> nodes,
            List<BudgetTypeNodeEntity> data) {

        for (BudgetTypeNodeVO node : nodes) {

            if (Objects.nonNull(parentNode) && !isChildNode(parentNode, node)) {
                throw new ValidateException(String.format("node [%s] can not be the parent node of [%s].", parentNode.getName(), node.getName()));
            }
            node.setNodeId(IdGenerator.get());

            BudgetTypeNodeEntity po = new BudgetTypeNodeEntity();

            po.setNodeId(node.getNodeId());
            po.setName(node.getName());

            Function<String, Boolean> isDuplicateFunc = (String code) -> budgetTypeDAO.isDuplicateNodeApiName(tenantId, code);
            if (Strings.isNullOrEmpty(node.getApiName())) {
                po.setApiName(ApiNameUtil.getBudgetNodeApiName(isDuplicateFunc));
            } else {
                boolean isDuplicate = isDuplicateFunc.apply(node.getApiName());
                if (isDuplicate) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_TYPE_MANAGER_5), node.getName()));
                }
            }

            po.setParentNodeId(parentNode == null ? "" : parentNode.getNodeId());
            po.setDescription(node.getDescription());

            po.setAutomaticEnableAfterDisassembly(Objects.isNull(node.getAutomaticEnableAfterDisassembly()) ? Boolean.TRUE : node.getAutomaticEnableAfterDisassembly());
            po.setAutomaticRecoveryAfterDeduct(Objects.isNull(node.getAutomaticRecoveryAfterDeduct()) ? Boolean.FALSE : node.getAutomaticRecoveryAfterDeduct());
            po.setRecordType(node.getRecordType());
            po.setDepartmentDimensionLevel(node.getDepartmentDimensionLevel());
            po.setTimeDimension(node.getTimeDimension());
            if (CollectionUtils.isEmpty(node.getDimensions())) {
                po.setDimensions(Lists.newArrayList());
            } else {
                po.setDimensions(node.getDimensions().stream().map(BudgetDimensionEntity::fromVO).collect(Collectors.toList()));
            }
            po.setControlStrategy(node.getControlStrategy());
            po.setEnableCarryForward(node.isEnableCarryForward());

            switch (node.getControlStrategy()) {
                default:
                case "unlimited":
                    if (node.isEnableCarryForward()) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_MANAGER_6));
                    }
                    po.setControlTimeDimension("");
                    po.setControlDimensions(Lists.newArrayList());
                    break;
                case "custom_dimension_limit":
                    if (node.isEnableCarryForward()) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_MANAGER_7));
                    }
                    po.setControlTimeDimension(node.getControlTimeDimension());
                    if (CollectionUtils.isEmpty(node.getControlDimensions())) {
                        po.setControlDimensions(Lists.newArrayList());
                    } else {
                        po.setControlDimensions(node.getControlDimensions().stream().map(BudgetDimensionEntity::fromVO).collect(Collectors.toList()));
                    }
                    break;
                case "full_limit":
                    po.setControlTimeDimension(node.getTimeDimension());
                    if (CollectionUtils.isEmpty(node.getDimensions())) {
                        po.setControlDimensions(Lists.newArrayList());
                    } else {
                        po.setControlDimensions(node.getDimensions().stream().map(BudgetDimensionEntity::fromVO).collect(Collectors.toList()));
                    }
                    break;
            }

            po.setPermissions(Maps.newHashMap());
            if (MapUtils.isNotEmpty(node.getPermissions())) {
                for (Map.Entry<String, ScopeVO> entry : node.getPermissions().entrySet()) {
                    po.getPermissions().put(entry.getKey(), ScopeEntity.fromVO(entry.getValue()));
                }
            }

            data.add(po);

            if (CollectionUtils.isNotEmpty(node.getNodes())) {
                searchNodesWhenUnused(tenantId, node, node.getNodes(), data);
            }
        }
    }

    private void buildNodes(List<BudgetTypeNodeVO> nodes, Map<String, List<BudgetTypeNodeEntity>> parentMap) {
        for (BudgetTypeNodeVO node : nodes) {
            if (parentMap.containsKey(node.getNodeId())) {
                node.setNodes(Lists.newArrayList());
                for (BudgetTypeNodeEntity po : parentMap.get(node.getNodeId())) {
                    node.getNodes().add(BudgetTypeNodeEntity.toVO(po));
                }
                buildNodes(node.getNodes(), parentMap);
            }
        }
    }

    private IActionContext getActionContext() {
        RequestContext requestContext = RequestContextManager.getContext();
        IActionContext actionContext = ActionContextExt.of(new User((String) null, (String) null)).getContext();
        boolean direct = false;
        boolean notNeedDeepCopy = false;
        Boolean upstreamCopyDescribe = null;
        if (Objects.nonNull(requestContext)) {
            if (Objects.nonNull(requestContext.getAttribute("direct"))) {
                direct = (Boolean) requestContext.getAttribute("direct");
            }

            if (Objects.nonNull(requestContext.getAttribute("not_need_deep_copy"))) {
                notNeedDeepCopy = (Boolean) requestContext.getAttribute("not_need_deep_copy");
            }

            if (Objects.nonNull(requestContext.getAttribute("upstream_copy_describe"))) {
                upstreamCopyDescribe = (Boolean) requestContext.getAttribute("upstream_copy_describe");
            }
        }

        actionContext.put("direct", direct);
        actionContext.put("not_need_deep_copy", notNeedDeepCopy);
        actionContext.put("upstream_copy_describe", upstreamCopyDescribe);
        actionContext.put("not_check_option_has_data", true);
        return actionContext;
    }
}
