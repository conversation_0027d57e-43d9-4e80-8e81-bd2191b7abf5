package com.facishare.crm.fmcg.tpm.dao.pg;

import com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityTypeStatisticsDatumPO;
import com.facishare.crm.fmcg.tpm.dao.pg.po.WriteOffSummaryGroupByAccountPO;
import com.facishare.paas.metadata.ratelimit.DBLimit;
import com.facishare.paas.metadata.ratelimit.MethodType;
import com.github.mybatis.handler.postgresql.StringArrayTypeHandler;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/9 19:00
 */
public interface ActivityWriteOffMapper extends ITenant<ActivityWriteOffMapper> {

    @Results({
            @Result(property = "status", column = "status"),
            @Result(property = "count", column = "count")
    })
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<ActivityTypeStatisticsDatumPO> queryActivityTypeStatisticsData(
            @Param("tenant_id") String tenantId,
            @Param("activity_type_id") String activityTypeId
    );

    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    long countByActivity(@Param("tenant_id") String tenantId, @Param("activity_id") String activityPlanId);

    @Results({
            @Result(property = "mainObjectId", column = "main_object_id"),
            @Result(property = "dataIdList", column = "data_id_list", typeHandler = StringArrayTypeHandler.class),
            @Result(property = "totalRowCount", column = "total_row_count")
    })
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<WriteOffSummaryGroupByAccountPO> sourceDataList(@Param("tenant_id") String tenantId,
                                                         @Param("table_name") String tableName,
                                                         @Param("api_name") String apiName,
                                                         @Param("has_extend_field") Boolean hasExtendField,
                                                         @Param("reference_account_field") String referenceAccountField,
                                                         @Param("reference_account_field_is_extend") Boolean referenceAccountFieldIsExtend,
                                                         @Param("reference_write_off_field") String referenceWriteOffField,
                                                         @Param("reference_write_off_field_is_extend") Boolean referenceWriteOffFieldIsExtend,
                                                         @Param("write_off_id") String writeOffId,
                                                         @Param("audit_status_list") List<String> auditStatusList,
                                                         @Param("store_name_filter") String key,
                                                         @Param("limit") long limit,
                                                         @Param("offset") long offset);

}
