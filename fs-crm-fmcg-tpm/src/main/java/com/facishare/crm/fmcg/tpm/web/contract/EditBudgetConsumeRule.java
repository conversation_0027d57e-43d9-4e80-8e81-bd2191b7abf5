package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetConsumeRuleVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetNewConsumeRuleVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/7/27 下午5:47
 */
public interface EditBudgetConsumeRule {

    @Data
    @ToString
    class Arg implements Serializable {

        //老消费规则参数
        @JSONField(name = "budget_old_consume_rule")
        @JsonProperty(value = "budget_old_consume_rule")
        @SerializedName("budget_old_consume_rule")
        private BudgetConsumeRuleVO budgetConsumeRuleVO;

        @JSONField(name = "budget_consume_rule")
        @JsonProperty(value = "budget_consume_rule")
        @SerializedName("budget_consume_rule")
        private BudgetNewConsumeRuleVO budgetNewConsumeRuleVO;
    }


    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        //老消费规则参数
        @JSONField(name = "budget_old_consume_rule")
        @JsonProperty(value = "budget_old_consume_rule")
        @SerializedName("budget_old_consume_rule")
        private BudgetConsumeRuleVO budgetConsumeRuleVO;

        @JSONField(name = "budget_consume_rule")
        @JsonProperty(value = "budget_consume_rule")
        @SerializedName("budget_consume_rule")
        private BudgetNewConsumeRuleVO budgetNewConsumeRuleVO;
    }
}
