package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2022/6/30 11:41
 */
@Slf4j
public class TPMBudgetTransferDetailObjUpdateImportDataAction extends StandardUpdateImportDataAction {

    @Override
    protected void convertFields(List<ImportData> dataList) {
        super.convertFields(dataList);
        log.info("data:{}", JSON.toJSONString(dataList));
        List<ImportError> errorList = new ArrayList<>();

        dataList.forEach(data -> {
            ImportError error = new ImportError();
            error.setRowNo(data.getRowNo());
            error.setErrorMessage(I18N.text(I18NKeys.ERRORMESSAGE_BUDGET_TRANSFER_DETAIL_OBJ_UPDATE_IMPORT_DATA_ACTION_0));
            errorList.add(error);
        });
        mergeErrorList(errorList);
    }
}
