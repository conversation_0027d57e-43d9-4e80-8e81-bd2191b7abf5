package com.facishare.crm.fmcg.tpm.web.service;

import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFiscalTimeService;
import com.facishare.crm.fmcg.tpm.business.dto.FiscalMonth;
import com.facishare.crm.fmcg.tpm.business.dto.FiscalQuarter;
import com.facishare.crm.fmcg.tpm.business.dto.FiscalYear;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.TimeDimensionType;
import com.facishare.crm.fmcg.tpm.web.contract.CalculateTimeSpan;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IBudgetTimeService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/22 11:13
 */
@Component
@SuppressWarnings("Duplicates")
public class BudgetTimeService implements IBudgetTimeService {

    @Resource
    private IFiscalTimeService fiscalTimeService;

    @Override
    public CalculateTimeSpan.Result calculateTimeSpan(CalculateTimeSpan.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        long time = fiscalTimeService.correctPeriodTime(context.getTenantId(), arg.getSourceTimeDimension(), arg.getTime());
        FiscalYear year = fiscalTimeService.fiscalYear(context.getTenantId(), arg.getSourceTimeDimension(), time);

        TimeDimensionType source = TimeDimensionType.valueOf(arg.getSourceTimeDimension().toUpperCase());
        TimeDimensionType target = TimeDimensionType.valueOf(arg.getTargetTimeDimension().toUpperCase());

        if (source.index() < target.index()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TIME_SERVICE_0));
        }
        if (source == TimeDimensionType.YEAR && target == TimeDimensionType.QUARTER) {
            int[] quarter = {1, 2, 3, 4};
            List<Integer> quarterList = Lists.newArrayList();
            for (int i : quarter) {
                quarterList.add(year.getYear() * 100 + i);
            }
            return CalculateTimeSpan.Result
                    .builder()
                    .targetTimeDimension(target.value())
                    .values(quarterList)
                    .from(year.getStart())
                    .to(year.getEnd())
                    .build();
        } else if (source == TimeDimensionType.YEAR && target == TimeDimensionType.MONTH) {
            List<Integer> monthList = Lists.newArrayList();
            for (FiscalMonth value : year.getMonth().values()) {
                monthList.add(value.getYear() * 100 + value.getMonth());
            }
            return CalculateTimeSpan.Result
                    .builder()
                    .targetTimeDimension(target.value())
                    .values(monthList)
                    .from(year.getStart())
                    .to(year.getEnd())
                    .build();
        } else if (source == TimeDimensionType.QUARTER && target == TimeDimensionType.MONTH) {
            FiscalQuarter quarter = year.getQuarter().values().stream().filter(f -> f.getStart() == time).findFirst().orElse(null);
            if (quarter == null) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_TIME_SERVICE_1));
            }
            List<Integer> monthList = Lists.newArrayList();
            for (FiscalMonth value : quarter.getMonth()) {
                monthList.add(value.getYear() * 100 + value.getMonth());
            }
            return CalculateTimeSpan.Result
                    .builder()
                    .targetTimeDimension(target.value())
                    .values(monthList)
                    .from(quarter.getStart())
                    .to(quarter.getEnd())
                    .build();
        } else {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TIME_SERVICE_2));
        }
    }
}
