package com.facishare.crm.fmcg.tpm.retry.setter;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandlerEnum;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2023/8/9 17:52
 */
//IgnoreI18nFile
@Component
public class YqslDeductSetter extends BaseSetter {

    public void setUpdateStatusTask(String tenantId, String userId, String describeApiName, String dataId, String action, long nextExecuteTime) {
        setUpdateStatusTask(tenantId, userId, describeApiName, dataId, action, nextExecuteTime, false, null);
    }

    public void setUpdateStatusTask(String tenantId, String userId, String describeApiName, String dataId,String action, long nextExecuteTime, boolean judgeRepeat, Integer maxRetryCount) {
        String name = String.format("元气森林扣减_%s_%s", describeApiName, dataId);
        if (judgeRepeat) {
            List<RetryTaskPO> retryTaskPOList = retryTaskDAO.queryProcessingTaskByName(name);
            if (CollectionUtils.isNotEmpty(retryTaskPOList)) {
                throw new ValidateException(I18N.text(I18NKeys.REWARD_RULE_RED_PACKET_SETTER_0));
            }
        }
        Map<String, Object> params = new HashMap<>();
        params.put("tenant_id", tenantId);
        params.put("user_id", userId);
        params.put("describe_api_name", describeApiName);
        params.put("data_id", dataId);
        params.put("action", action);
        maxRetryCount = maxRetryCount == null ? 5 : maxRetryCount;
        setInit(tenantId, name, RetryHandlerEnum.YQSL_DEDUCT_HANDLER.code(), JSON.toJSONString(params), maxRetryCount, nextExecuteTime);
    }
}
