package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.api.rule.AddActivityRewardRule;
import com.facishare.crm.fmcg.tpm.api.rule.RewardDetailDTO;
import com.facishare.crm.fmcg.tpm.api.rule.RewardRuleDTO;
import com.facishare.crm.fmcg.tpm.business.*;
import com.facishare.crm.fmcg.tpm.business.abstraction.*;
import com.facishare.crm.fmcg.tpm.business.dto.CrmAuditLogDTO;
import com.facishare.crm.fmcg.tpm.business.enums.UseRangeEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.PersonRewardRuleWhereConditionVO;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IRewardRuleManager;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityRewardRuleService;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPMDisplayReportService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.QueryDeptByName;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.fmcg.tpm.action.TPMActivityObjEditAction.TEMPLATE_TENANT_ID;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/6 2:01 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjAddAction extends StandardAddAction implements TransactionService<BaseObjectSaveAction.Arg, BaseObjectSaveAction.Result> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TPMActivityObjAddAction.class);

    private static final String SEND_BUDGET_IDS = "SEND_BUDGET_IDS:%s";

    public final IActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);
    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private final CrmAuditLogService crmAuditLogService = SpringUtil.getContext().getBean(CrmAuditLogService.class);
    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    private final UnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(UnifiedActivityCommonLogicBusiness.class);

    private final IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);

    private final IEnableCacheService enableCacheService = SpringUtil.getContext().getBean(IEnableCacheService.class);
    private final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);

    private final IActivityRewardRuleService activityRewardRuleService = SpringUtil.getContext().getBean(IActivityRewardRuleService.class);

    private final IRewardRuleManager rewardRuleManager = SpringUtil.getContext().getBean(IRewardRuleManager.class);
    private final IPersonnelRewardRuleService personnelRewardRuleService = SpringUtil.getContext().getBean(IPersonnelRewardRuleService.class);

    private final IMengNiuAICalculateService mengNiuAICalculateService = SpringUtil.getContext().getBean(IMengNiuAICalculateService.class);

    private final DescribeCacheService describeCacheService = SpringUtil.getContext().getBean(DescribeCacheService.class);

    private final ITPMDisplayReportService tpmDisplayReportService = SpringUtil.getContext().getBean(ITPMDisplayReportService.class);

    private boolean isTpm2Tenant = false;
    private boolean enableActivityCycleControl = true;
    private IObjectData unifiedActivity = null;
    private Set<String> activityDepartments = new HashSet<>();
    private String ACTIVITY_TYPE_TEMPLATE_ID = "";
    private String PRODUCT_GIFT_DATA = "";

    private RewardRuleDTO rewardRuleData = null;
    private ActivityTypeExt activityTypeExt;

    @Override
    protected void before(Arg arg) {
        initData();
        setDefaultValue(arg);
        validateRewardRule();
        validateDateRange(arg);
        validateBrand();
        validateUnifiedActivity();
        sendActivityObjAuditLog(arg);
        validateMaxWriteOffCount(arg);
        validateActivityStatus(arg);
        validateCashingProduct(arg);
        validateAI();
        validateOrderProductRule();
        if (actionContext.getAttribute("importTriggerFlow") != null) {
            actionContext.setAttribute("triggerFlow", actionContext.getAttribute("importTriggerFlow"));
        } else {
            actionContext.setAttribute("triggerFlow", true);
        }
        super.before(arg);

        validateProductRange();
        validateActivityDetail();
        budgetService.buildCallbackKey(actionContext);

        validatePricePolicy(arg);

        validateDisplayReport(arg);
    }

    private void validateDisplayReport(Arg arg) {
        try {
            tpmDisplayReportService.validateDisplayReport(arg);
        } catch (ValidateException ex) {
            throw ex;
        } catch (Exception e) {
            throw new ValidateException(e.getMessage() != null ? e.getMessage() : "System Validate Error.");
        }
    }

    private void validateActivityDetail() {
        if (!MapUtils.isEmpty(this.arg.getDetails())) {
            if (this.arg.getDetails().containsKey(ApiNames.TPM_ACTIVITY_PRIZES_OBJ)) {
                List<IObjectData> details = arg.getDetails().get(ApiNames.TPM_ACTIVITY_PRIZES_OBJ).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
                details.forEach(detail -> {
                    String goodType = detail.get(TPMActivityPrizesFields.PRIZE_TYPE + "__v", String.class);
                    if (Strings.isNullOrEmpty(goodType)) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PRIZE_TYPE_CAN_NOT_NULL));
                    }
                    if (PointsGoodsFields.CommodityType.ONE_MORE_GOODS.equals(goodType)
                            && CollectionUtils.isEmpty(detail.get(TPMActivityPrizesFields.PRIZE_PRODUCT_IDS, List.class))) {
                        throw new ValidateException(I18N.text(I18NKeys.ONE_MORE_BOX_REWARD_HAS_NO_PRODUCT));
                    }
                    if (Objects.isNull(detail.get(TPMActivityPrizesFields.PRIZE_AMOUNT))) {
                        if (PointsGoodsFields.CommodityType.RED_PACKET.equals(goodType)) {
                            throw new ValidateException(I18N.text(I18NKeys.PRIZE_DETAIL_RED_PACKET_NEED_PRIZE_AMOUNT));
                        } else if (Boolean.TRUE.equals(detail.get(TPMActivityPrizesFields.PRIZE_AMOUNT_VERIFIED_BY_STORE))) {
                            throw new ValidateException(I18N.text(I18NKeys.PRIZE_AMOUNT_CAN_NOT_EMPTY_DUE_TO_WRITE_OF));
                        }
                    }
                });
            }
        }
    }

    private void validateBrand() {
        IObjectData data = arg.getObjectData().toObjectData();
        if (activityTypeExt != null) {
            String customerType = data.get(TPMActivityFields.CUSTOMER_TYPE, String.class);
            if (Boolean.TRUE.equals(activityTypeExt.get().getForbidRelateCustomer())) {
                if (!ActivityCustomerTypeEnum.BRAND.value().equals(customerType)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_0));
                }
                String storeRange = data.get(TPMActivityFields.STORE_RANGE, String.class);
                if (Strings.isNullOrEmpty(storeRange) || !storeRange.contains("ALL")) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_1));
                }
            } else {
                if (ActivityCustomerTypeEnum.BRAND.value().equals(customerType)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_2));
                }
            }

        }
    }

    private void validatePricePolicy(Arg arg) {
        ActivityTypePO activityTypePO = activityTypeExt.get();
        String templateId = activityTypePO.getTemplateId();
        if (templateId == null || !templateId.startsWith("promotion")) {
            return;
        }

        //判断web端请求 WEB.chrome
        String clientInfo = actionContext.getRequestContext().getClientInfo();
        if (!clientInfo.startsWith("WEB")) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_3));
        }

        IObjectData activity = arg.getObjectData().toObjectData();
        BigDecimal activityAmount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        if (activityAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_4));
        }

        JSONObject storeRange = JSON.parseObject(activity.get(TPMActivityFields.STORE_RANGE, String.class));
        String storeRangeType = storeRange.getString("type");
        String limitObjType = activity.get(TPMActivityFields.LIMIT_OBJ_TYPE, String.class);

        String rioActivityId = activity.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class);
        if (StringUtils.isNotEmpty(rioActivityId)) {
            if (!TPMActivityFields.LIMIT_OBJ_TYPE_ACTIVITY_AMOUNT_TOTAL.equals(limitObjType)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_5));
            }
        }
        switch (limitObjType) {
            case TPMActivityFields.LIMIT_OBJ_TYPE_ACTIVITY_AMOUNT_TOTAL:
                break;
            case TPMActivityFields.LIMIT_OBJ_TYPE_ACCOUNT_COST_EQUAL:
                BigDecimal amountUnifyLimitAmount = activity.get(TPMActivityFields.ACCOUNT_UNIFY_LIMIT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
                if (amountUnifyLimitAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_6));
                }
                if (activityAmount.compareTo(amountUnifyLimitAmount) < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_7));
                }
                break;
            case TPMActivityFields.LIMIT_OBJ_TYPE_ACCOUNT_COST_DIFFERENCE:
                if (!UseRangeEnum.FIXED.value().equals(storeRangeType)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_8));
                }
                List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_ACTIVITY_STORE_OBJ);
                if (org.springframework.util.CollectionUtils.isEmpty(details)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_9));
                }
                //统计参与活动客户促销总额
                BigDecimal totalPolicyLimitAmount = BigDecimal.ZERO;
                for (ObjectDataDocument document : details) {
                    BigDecimal policyLimitAmount = document.toObjectData().get(TPMActivityStoreFields.POLICY_LIMIT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
                    if (policyLimitAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_10));
                    }
                    totalPolicyLimitAmount = totalPolicyLimitAmount.add(policyLimitAmount);
                }
                if (activityAmount.compareTo(totalPolicyLimitAmount) < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_11));
                }
                break;
            default:
        }

        PRODUCT_GIFT_DATA = (String) CommonUtils.getOrDefault(arg.getObjectData().get("product_gift_data_json"), "");
    }

    private void initData() {
        this.isTpm2Tenant = tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()));
        String unifiedActivityId = this.arg.getObjectData().toObjectData().get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
        this.unifiedActivity = Strings.isNullOrEmpty(unifiedActivityId) ? null : serviceFacade.findObjectData(actionContext.getUser(), unifiedActivityId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);

        String activityTypeId = this.arg.getObjectData().toObjectData().get(TPMActivityFields.ACTIVITY_TYPE, String.class);

        if (!Strings.isNullOrEmpty(activityTypeId)) {
            LOGGER.info("activity type id is {}", activityTypeId);
            this.activityTypeExt = activityTypeManager.find(actionContext.getTenantId(), activityTypeId);
            ACTIVITY_TYPE_TEMPLATE_ID = activityTypeExt.get().getTemplateId();
            if (!Objects.isNull(activityTypeExt.node(NodeType.PLAN_TEMPLATE)) && Strings.isNullOrEmpty(unifiedActivityId)) {
                if (Objects.nonNull(activityTypeExt.activityPlanConfig())) {
                    Boolean enableRelationPreNodeRequired = activityTypeExt.activityPlanConfig().getEnableRelationPreNodeRequired();
                    if (!Boolean.FALSE.equals(enableRelationPreNodeRequired)) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_12));
                    }
                }
            }
            ActivityPlanConfigEntity activityPlanConfig = activityTypeExt.activityPlanConfig();
            if (activityPlanConfig != null && activityPlanConfig.getEnableActivityCycleControl() != null) {
                this.enableActivityCycleControl = activityPlanConfig.getEnableActivityCycleControl();
                LOGGER.info("activity enableActivityCycleControl is {}", this.enableActivityCycleControl);
            }
        }
        String jsonData = this.arg.getObjectData().toObjectData().get(TPMActivityFields.REWARD_RULE_JSON, String.class);
        if (!Strings.isNullOrEmpty(jsonData)) {
            rewardRuleData = JSON.parseObject(jsonData, RewardRuleDTO.class);
            rewardRuleData.setRuleType(activityTypeExt.get().getTemplateId());
            rewardRuleData.setTenantId(actionContext.getTenantId());
        }
    }


    private void validateMaxWriteOffCount(Arg arg) {
        IObjectData data = arg.getObjectData().toObjectData();
        String maxWriteOffCount = data.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class);
        String dealerId = data.get(TPMActivityFields.DEALER_ID, String.class);
        if (ActivityMaxWriteOffCountEnum.ONCE.value().equals(maxWriteOffCount)
                && Strings.isNullOrEmpty(dealerId)
                && !TPMGrayUtils.skipActivityOnceWriteOffValidate(actionContext.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_13));
        }
    }

    private void validateUnifiedActivity() {
        if (this.unifiedActivity != null) {
            String unifiedActivityCloseStatus = this.unifiedActivity.get(TPMActivityUnifiedCaseFields.CLOSE_STATUS, String.class);
            if (TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED.equals(unifiedActivityCloseStatus)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_14));
            }
            IObjectData data = this.arg.getObjectData().toObjectData();
            unifiedActivityCommonLogicBusiness.checkIfUnifiedActivityContainsActivityType(this.unifiedActivity, data.get(TPMActivityFields.ACTIVITY_TYPE, String.class));
            String dealerId = data.get(TPMActivityFields.DEALER_ID, String.class);
            if (!Strings.isNullOrEmpty(dealerId)) {
                IObjectData dealer = serviceFacade.findObjectData(actionContext.getUser(), dealerId, ApiNames.ACCOUNT_OBJ);
                unifiedActivityCommonLogicBusiness.checkDealerIsInTheRangeOfUnifiedActivityLimits(actionContext.getTenantId(), this.unifiedActivity, dealer);
            }
            unifiedActivityCommonLogicBusiness.checkDepartmentIsUnderTheRangeOfUnifiedActivityLimits(actionContext.getTenantId(), unifiedActivity, new ArrayList<>(activityDepartments));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result localResult = super.after(arg, result);

        LOGGER.info("need trigger master approval : {}, need trigger approval : {}, is start success : {}, is start success asynchronous : {}, biz info : {}",
                this.needTriggerMasterApproval(),
                this.needTriggerApprovalFlow(),
                this.isApprovalFlowStartSuccess(result.getObjectData().getId()),
                this.isApprovalFlowStartSuccessOrAsynchronous(result.getObjectData().getId()),
                RequestContextManager.getContext() == null ? "" : JSON.toJSONString(RequestContextManager.getContext().getBizInfo())
        );

        List<String> updateBudgetIds = actionContext.getAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()));

        String key = String.format(SEND_BUDGET_IDS, actionContext.getPostId());

        LOGGER.info("key : {}, update budget id list : {}, action context : {}.", key, updateBudgetIds, actionContext);

        if (!CollectionUtils.isEmpty(updateBudgetIds)) {
            updateBudgetIds.forEach(id -> budgetService.calculateBudget(actionContext.getTenantId(), id));
        }

        String type = (String) arg.getObjectData().get("record_type");
        if ("default__c".equals(type)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.CREATE_CUSTOM, false);
        }

        if ("dealer_activity__c".equals(type)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.CREATE_PERSONAL, false);
        }

        if (!Strings.isNullOrEmpty(ACTIVITY_TYPE_TEMPLATE_ID) && ACTIVITY_TYPE_TEMPLATE_ID.startsWith("reward")) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_REWARD_RULE, String.format(BuryOperation.USE_ACTIVITY_TYPE_TEMPLATE, ACTIVITY_TYPE_TEMPLATE_ID), false);
        }

        try {
            String unifiedCaseId = arg.getObjectData().toObjectData().get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
            if (Objects.nonNull(unifiedCaseId)) {
                String buryModule = BuryModule.TPM.TPM_ACTIVITY + "_" + TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID;
                BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), buryModule, BuryOperation.CREATE, isTpm2Tenant);
            }

            asyncAddTpmLog(arg);
        } catch (Exception e) {
            LOGGER.warn("活动申请埋点异常 tenantId：{}", actionContext.getTenantId(), e);//ignorei18n
        }

        resetActivityStatus(result);
        recalculateAmount(result.getObjectData().toObjectData());
        releaseProofEnableCacheOfActivityObj(result);
        // 促销类 新增促销规则
        addPromotionPolicy(localResult.getObjectData().toObjectData(), PRODUCT_GIFT_DATA);
        return localResult;
    }

    private void recalculateAmount(IObjectData data) {
        if (!this.isApprovalFlowStartSuccess(data.getId())) {
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), data.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
        }
    }

    private void asyncAddTpmLog(Arg arg) {
        String storeCashingType = arg.getObjectData().toObjectData().get(TPMActivityFields.STORE_CASHING_TYPE, String.class);
        String dealerCashingType = arg.getObjectData().toObjectData().get(TPMActivityFields.DEALER_CASHING_TYPE, String.class);

        String buryModule = BuryModule.TPM.TPM_ACTIVITY;
        if (Objects.nonNull(storeCashingType)) {
            buryModule = BuryModule.TPM.TPM_ACTIVITY + "_store_" + BuryModule.CASH_TYPE.CASH;
            if (Objects.equals(storeCashingType, BuryModule.CASH_TYPE.GOODS)) {
                buryModule = BuryModule.TPM.TPM_ACTIVITY + "_store_" + BuryModule.CASH_TYPE.GOODS;
            }
        }
        if (Objects.nonNull(dealerCashingType)) {
            buryModule = BuryModule.TPM.TPM_ACTIVITY + "_dealer_" + BuryModule.CASH_TYPE.CASH;
            if (Objects.equals(dealerCashingType, BuryModule.CASH_TYPE.GOODS)) {
                buryModule = BuryModule.TPM.TPM_ACTIVITY + "_dealer_" + BuryModule.CASH_TYPE.GOODS;
            }
        }
        if (!Strings.isNullOrEmpty(buryModule)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), buryModule, BuryOperation.CREATE, isTpm2Tenant);
        }

        BigDecimal activityAmount = arg.getObjectData().toObjectData().get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        BuryService.asyncTpmAmountLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM_AMOUNT.ACTIVITY_AMOUNT, BuryOperation.CREATE, activityAmount.doubleValue());

        BigDecimal activityActualAmount = arg.getObjectData().toObjectData().get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        BuryService.asyncTpmAmountLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM_AMOUNT.ACTIVITY_ACTUAL_AMOUNT, BuryOperation.CREATE, activityActualAmount.doubleValue());
    }

    private void resetActivityStatus(Result result) {
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
            if (!ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__APPROVAL);
                serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
            }
        }
    }

    private void releaseProofEnableCacheOfActivityObj(Result result) {
        if (!tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            return;
        }
        if (TPMGrayUtils.skipEnableCheck(actionContext.getTenantId())) {
            return;
        }
        try {
            if (!isApprovalFlowStartSuccess(result.getObjectData().getId())) {
                ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> enableCacheService.resetCacheByActivity(actionContext.getTenantId(), result.getObjectData().toObjectData()))).run();
            }
        } catch (Exception e) {
            LOGGER.info("releaseProofEnableCacheOfActivityObj error: ", e);
        }
    }


    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }

    private void sendActivityObjAuditLog(Arg arg) {
        crmAuditLogService.sendLog(CrmAuditLogDTO.builder()
                .tenantId(actionContext.getTenantId())
                .userId(String.valueOf(User.systemUser(actionContext.getTenantId())))
                .action("Add")
                .objectApiNames(ApiNames.TPM_ACTIVITY_OBJ)
                .message("新建活动申请")//ignorei18n
                .parameters(JSONObject.toJSONString(arg))
                .build());
    }

    private void validateDateRange(Arg arg) {
        if (TPMGrayUtils.isAllowNotFillActivityTimeRange(actionContext.getTenantId()) && (arg.getObjectData().get(TPMActivityFields.BEGIN_DATE) == null || arg.getObjectData().get(TPMActivityFields.END_DATE) == null)) {
            arg.getObjectData().put(TPMActivityFields.BEGIN_DATE, 0L);
            arg.getObjectData().put(TPMActivityFields.END_DATE, 4480934400000L);
        }

        // 不启用，则默认值
        if (!this.enableActivityCycleControl) {
            if (arg.getObjectData().get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID) != null) {
                arg.getObjectData().putIfAbsent(TPMActivityFields.BEGIN_DATE, unifiedActivity.get(TPMActivityUnifiedCaseFields.START_DATE, Long.class));
                arg.getObjectData().putIfAbsent(TPMActivityFields.END_DATE, unifiedActivity.get(TPMActivityUnifiedCaseFields.END_DATE, Long.class));
            } else {
                arg.getObjectData().putIfAbsent(TPMActivityFields.BEGIN_DATE, TimeUtils.MIN_DATE);
                arg.getObjectData().putIfAbsent(TPMActivityFields.END_DATE, TimeUtils.MAX_DATE);
            }
            if (Objects.equals(arg.getObjectData().get(TPMActivityFields.BEGIN_DATE), 0)) {
                arg.getObjectData().put(TPMActivityFields.BEGIN_DATE, TimeUtils.MIN_DATE);
            }
        }

        IObjectData data = arg.getObjectData().toObjectData();
        if (data.get(TPMActivityFields.BEGIN_DATE) == null || data.get(TPMActivityFields.END_DATE) == null) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_15));
        }

        long begin = data.get(TPMActivityFields.BEGIN_DATE, Long.class);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin(data.get(TPMActivityFields.END_DATE, Long.class));
        if (!TPMGrayUtils.notResetEndDate(actionContext.getTenantId())) {
            arg.getObjectData().put(TPMActivityFields.END_DATE, end);
        }

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_DATE_ERROR));
        }

        unifiedActivityCommonLogicBusiness.checkTimeRangeIsInTheRangeOfUnifiedActivityLimits(unifiedActivity, begin, end);

        LOGGER.info("end date : {}", end);
    }

    private void setDefaultValue(Arg arg) {
        if (Strings.isNullOrEmpty((String) arg.getObjectData().get(TPMActivityFields.STORE_RANGE))) {
            arg.getObjectData().put(TPMActivityFields.STORE_RANGE, "{\"type\":\"ALL\",\"value\":\"ALL\"}");
        }

        List<String> departmentIds = new ArrayList<>();
        if (arg.getObjectData().toObjectData().get(TPMActivityFields.DEPARTMENT_RANGE) != null) {
            departmentIds.addAll((List<String>) arg.getObjectData().toObjectData().get(TPMActivityFields.DEPARTMENT_RANGE));
        }
        // 部门默认值
        suppleActivityDepartmentRange();
        // 参与部门处理。
        List<String> departmentRangeList = CommonUtils.castIgnore(arg.getObjectData().toObjectData().get(TPMActivityFields.MULTI_DEPARTMENT_RANGE), String.class);
        if (!CollectionUtils.isEmpty(departmentRangeList)) {
            departmentIds.addAll(departmentRangeList);
        }

        if (departmentIds.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_16));
        }
        IObjectData argData = arg.getObjectData().toObjectData();
        argData.set(TPMActivityFields.CLOSE_STATUS, TPMActivityFields.CLOSE_STATUS__UNCLOSED);
        argData.set(TPMActivityFields.CLOSE_TIME, null);
        if (Strings.isNullOrEmpty(argData.get(TPMActivityFields.CUSTOMER_TYPE, String.class))) {
            argData.set(TPMActivityFields.CUSTOMER_TYPE, ActivityCustomerTypeEnum.DEALER_STORE.value());
        }
        arg.getObjectData().put(TPMActivityFields.STORE_RANGE, rangeFieldBusiness.newActivityStoreRangeCondition(actionContext.getTenantId(), argData.get(TPMActivityFields.CUSTOMER_TYPE, String.class), argData.get(TPMActivityFields.STORE_RANGE, String.class), argData.get(TPMActivityFields.DEALER_ID, String.class)));

        if (Strings.isNullOrEmpty(argData.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class))) {
            argData.set(TPMActivityFields.MAX_WRITE_OFF_COUNT, ActivityMaxWriteOffCountEnum.MULTI.value());
        } else if (argData.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class).equals(ActivityMaxWriteOffCountEnum.ONCE.value())
                && Strings.isNullOrEmpty(argData.get(TPMActivityFields.IS_AUTO_CLOSE, String.class))) {
            argData.set(TPMActivityFields.IS_AUTO_CLOSE, true);
        }
        serviceFacade.getSubDeptsByDeptIds(actionContext.getTenantId(), User.SUPPER_ADMIN_USER_ID, departmentIds, 0).values().forEach(v ->
                activityDepartments.addAll(v.stream().map(QueryDeptByName.DeptInfo::getId).collect(Collectors.toList())));

    }

    private void suppleActivityDepartmentRange() {
        IObjectData objectData = arg.getObjectData().toObjectData();
        List<String> departmentRangeList = CommonUtils.castIgnore(objectData.get(TPMActivityFields.MULTI_DEPARTMENT_RANGE), String.class);
        if (CollectionUtils.isEmpty(departmentRangeList) || departmentRangeList.contains("999998")) {
            //当申请关联方案时，参与部门为空，默认为方案上的参与部门范围。
            if (objectData.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID) != null && unifiedActivity != null) {
                List<String> unifiedDepartmentIds = CommonUtils.castIgnore(unifiedActivity.get(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE), String.class);
                objectData.set(TPMActivityFields.MULTI_DEPARTMENT_RANGE, unifiedDepartmentIds);
            } else {
                objectData.set(TPMActivityFields.MULTI_DEPARTMENT_RANGE, Lists.newArrayList("999999"));
            }
        }
    }

    private void validateActivityStatus(Arg arg) {

        long begin = arg.getObjectData().toObjectData().get(TPMActivityFields.BEGIN_DATE, Long.class);
        long end = arg.getObjectData().toObjectData().get(TPMActivityFields.END_DATE, Long.class);
        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityFields.ACTIVITY_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityFields.ACTIVITY_STATUS__END;
        }
        arg.getObjectData().put(TPMActivityFields.ACTIVITY_STATUS, status);
    }

    private void validateCashingProduct(Arg arg) {
        if (!TPMGrayUtils.activityCashingProductIsRequired(actionContext.getTenantId())) {
            return;
        }
        String dealerCashType = (String) arg.getObjectData().get(TPMActivityFields.DEALER_CASHING_TYPE);
        String storeCashType = (String) arg.getObjectData().get(TPMActivityFields.STORE_CASHING_TYPE);
        if (Objects.equals(dealerCashType, TPMActivityCashingProductFields.GOODS) || Objects.equals(storeCashType, TPMActivityCashingProductFields.GOODS)) {
            List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_OBJ);
            if (CollectionUtils.isEmpty(details)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_17));
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        try {
            tryLock();
            return packTransactionProxy.packAct(this, arg);
        } catch (Exception e) {
            budgetService.rmSaveIdempotent(actionContext);
            throw e;
        }
    }

    private void tryLock() {
        if (budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            String nowBudgetId = (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE);

            //关联预算表校验
            if (!Strings.isNullOrEmpty(nowBudgetId)) {

                //todo:try lock
                budgetService.tryLockBudget(actionContext, nowBudgetId);
            }
        }
    }

    @Override
    protected Map<String, Map<String, Object>> buildCallbackDataForAddAction(IObjectData objectData, Long detailCreateTime) {
        Map<String, Map<String, Object>> callbackData = super.buildCallbackDataForAddAction(objectData, detailCreateTime);
        String relationValue = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
        if (!Strings.isNullOrEmpty(relationValue)) {
            Map<String, Object> instanceMap = callbackData.getOrDefault(objectData.getId(), new HashMap<>());
            callbackData.put(objectData.getId(), instanceMap);
            Map<String, Object> callbackDatum = new HashMap<>();
            callbackDatum.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, relationValue);
            instanceMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
        return callbackData;
    }

    @Override
    public Result doActTransaction(Arg arg) {
        Result result = super.doAct(arg);
        validateBudget(arg, result);
        updateApprovalIdToDetail();
        saveRewardRule();
        handlerPersonnelRewardRule(result);
        addProofPeriodTime(result);
        return result;
    }

    private void addProofPeriodTime(Result result) {
        tpmDisplayReportService.addProofPeriodTime(actionContext.getUser().getUpstreamOwnerIdOrUserId(), result);
    }

    private void handlerPersonnelRewardRule(Result result) {
        IObjectData data = result.getObjectData().toObjectData();
        String where = data.get(TPMActivityFields.PERSON_REWARD_RULE_WHERE, String.class);
        String apiName = data.get("action_rule_object__c", String.class);
        if (StringUtils.isEmpty(apiName)) {
            return;
        }
        String ruleTemplateTenant = data.get("action_rule_template_tenant__c", String.class);
        String ruleTemplateTenantId = TEMPLATE_TENANT_ID.get(ruleTemplateTenant);
        if (StringUtils.isEmpty(ruleTemplateTenantId)) {
            throw new ValidateException("action_rule_template_tenant__c not found");
        }
        String conditionCode = personnelRewardRuleService.buildConditionCode(ruleTemplateTenantId, -10000, apiName, data.get(TPMActivityFields.PERSON_REWARD_RULE_CODE, String.class), JSON.parseArray(where, PersonRewardRuleWhereConditionVO.class));
        if (StringUtils.isEmpty(conditionCode)) {
            return;
        }
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMActivityFields.PERSON_REWARD_RULE_CODE, conditionCode);
        serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
    }

    private void addPromotionPolicy(IObjectData objectData, String productGiftData) {
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID) && ACTIVITY_TYPE_TEMPLATE_ID.startsWith("promotion")) {
            if (Objects.isNull(this.unifiedActivity)) {
                saveOrUpdatePromotionPolicy(objectData, productGiftData);
            }
            // 生成状态为正常
            LOGGER.info("activity life status is {}", objectData.get(CommonFields.LIFE_STATUS));
            if (Objects.equals(objectData.get(CommonFields.LIFE_STATUS, String.class), CommonFields.LIFE_STATUS__NORMAL)) {
                createSFAPromotionPolicy(objectData);
            }
        }
    }

    private void createSFAPromotionPolicy(IObjectData objectData) {
        promotionPolicyService.createSFAPromotionPolicy(actionContext.getTenantId(),
                actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                objectData);

    }

    private void saveOrUpdatePromotionPolicy(IObjectData objectData, String productGiftData) {
        promotionPolicyService.saveOrUpdate(actionContext.getTenantId(),
                actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                objectData,
                productGiftData,
                false);
    }

    private void validateBudget(Arg arg, Result result) {
        String type = (String) arg.getObjectData().get("record_type");
        int tenantId = Integer.parseInt(actionContext.getTenantId());

        //开启预算表
        LOGGER.info("tpm budget open : {}", arg);

        if (budgetService.isOpenBudge(tenantId)) {
            String nowBudgetId = (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE);

            //关联预算表校验
            if (!Strings.isNullOrEmpty(nowBudgetId)) {

                //todo:try lock
                //budgetService.tryLockBudget(actionContext, nowBudgetId);
                IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), nowBudgetId, ApiNames.TPM_ACTIVITY_BUDGET);
                LOGGER.info("budget:{}", budget);
                if (!ObjectLifeStatus.NORMAL.getCode().equals(budget.get(CommonFields.LIFE_STATUS))) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_18));
                }

                //判断时间
                int startMonth = budgetService.getBudgetStartMonth(tenantId);
                Long activityBeginTime = arg.getObjectData().toObjectData().get(TPMActivityFields.BEGIN_DATE, Long.class);
                int budgetYear = Integer.parseInt((String) budget.get(TPMActivityBudgetFields.PERIOD_YEAR));
                LocalDate budgetStartDate = LocalDate.of(budgetYear, startMonth, 1);
                LocalDate budgetEndDate = budgetStartDate.plusYears(1);
                LocalDate activityBeginDate = Instant.ofEpochMilli(activityBeginTime).atZone(ZoneOffset.ofHours(8)).toLocalDate();
                if (budgetStartDate.isAfter(activityBeginDate) || budgetEndDate.isBefore(activityBeginDate) || budgetEndDate.equals(activityBeginDate)) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_TIME_IS_NOT_FIT_BUDGET));
                }

                //department
                String budgetDepartment = ((List<String>) budget.get(TPMActivityBudgetFields.BUDGET_DEPARTMENT)).get(0);
                List<String> subDeptIds = serviceFacade.getSubDeptByDeptId(actionContext.getTenantId(), User.SUPPER_ADMIN_USER_ID, budgetDepartment, true);


                if (!CommonUtils.isIntersection(subDeptIds, activityDepartments)) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_DEPARTMENT_NOT_FIT));
                }

                double activityAmountInObj = arg.getObjectData().get(TPMActivityFields.ACTIVITY_AMOUNT) == null ? 0D : Double.parseDouble(arg.getObjectData().get(TPMActivityFields.ACTIVITY_AMOUNT).toString());
                if (activityAmountInObj < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AMOUNT_CAN_NOT_BE_ZERO));
                }

                //预算
                Map<String, Double> amountMap = new HashMap<>();
                double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), budget, amountMap);
                double activityAmount = arg.getObjectData().get(TPMActivityFields.ACTIVITY_AMOUNT) == null ? 0D : Double.parseDouble((String) arg.getObjectData().getOrDefault(TPMActivityFields.ACTIVITY_AMOUNT, "0"));
                availableAmount = CommonUtils.keepNDecimal(availableAmount, 3);
                activityAmount = CommonUtils.keepNDecimal(activityAmount, 3);
                if (!TPMGrayUtils.disableBudgetAmountJudge(actionContext.getTenantId()) && CommonUtils.keepNDecimal(availableAmount - activityAmount, 3) < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_INSUFFICIENT));
                }
                boolean needTriggerApproval = budgetService.needApproval(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, "Create");
                actionContext.setAttribute("needTriggerApproval", needTriggerApproval);

                LogData logData = LogData.builder().data(JSON.toJSONString(arg)).build();
                logData.setAttribute("budget", budget);
                logData.setAttribute("amountMap", amountMap);
                String logId = operateInfoService.log(actionContext.getTenantId(), LogType.ADD.value(), JSON.toJSONString(logData), actionContext.getUser().getUpstreamOwnerIdOrUserId(), ApiNames.TPM_ACTIVITY_OBJ, result.getObjectData().getId(), needTriggerApproval);

                double beforeAmount = needTriggerApprovalFlow() ? availableAmount : availableAmount + activityAmount;
                double afterAmount = needTriggerApprovalFlow() ? availableAmount - activityAmount : activityAmount;

                IObjectData detail = budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                        "1",
                        budget.getId(),
                        String.format("活动新建：「%s」申请", arg.getObjectData().get("name")),//ignorei18n
                        -activityAmount,
                        beforeAmount,
                        afterAmount,
                        System.currentTimeMillis(),
                        String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                        result.getObjectData().getId(),
                        TraceContext.get().getTraceId(),
                        IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + result.getObjectData().getId()).build());

                //calculate budget
                actionContext.setAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()), Lists.newArrayList(budget.getId()));
                actionContext.setAttribute("operate_log_id", logId);
                actionContext.setAttribute("detail_id", detail.getId());
                LOGGER.info("actionContext:{}", JSON.toJSONString(actionContext));
            }
        }
    }

    private void updateApprovalIdToDetail() {
        boolean needTriggerApproval = Boolean.TRUE.equals(actionContext.getAttribute("needTriggerApproval"));
        if (needTriggerApproval) {
            if (actionContext.getAttribute("operate_log_id") != null) {
                String approvalId = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
                operateInfoService.updateLog(actionContext.getAttribute("operate_log_id"), actionContext.getTenantId(), approvalId);
                if (actionContext.getAttribute("detail_id") != null) {
                    budgetService.updateApprovalIdForDetail(actionContext.getTenantId(), actionContext.getAttribute("detail_id"), approvalId);
                }
            }
        }
    }

    private void validateRewardRule() {
        if (rewardRuleData != null) {
            try {
                rewardRuleManager.validateRule(rewardRuleData);
                RewardDetailDTO consumerRewardDetail = rewardRuleData.getRewardDetails().get(rewardRuleData.getRewardDetails().size() - 1);
                if (consumerRewardDetail.getRewardNode().getRewardDimension().equals(RewardDimensionEnum.CONSUMER.code()) && Objects.nonNull(consumerRewardDetail.getRewardStrategy())) {
                    if (consumerRewardDetail.getRewardStrategy().getRewardMethod().equals(RewardMethodEnum.PHYSICAL_ITEM.code())) {
                        String accountOption = arg.getObjectData().toObjectData().get(TPMActivityFields.PHYSICAL_ITEM_WRITE_OFF_CLOUD_ACCOUNT, String.class);
                        if (Strings.isNullOrEmpty(accountOption) && describeCacheService.isExistField(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.PHYSICAL_ITEM_WRITE_OFF_CLOUD_ACCOUNT)) {
                            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_ACTIVITY_OBJ_ADD_ACTION_0));
                        }
                        if (CollectionUtils.isEmpty(this.arg.getDetails().get(ApiNames.TPM_ACTIVITY_PRIZES_OBJ))) {
                            throw new ValidateException(I18N.text(I18NEnums.PLEASE_FILL_ACTIVITY_PRIZE_DETAIL.getCode()));
                        }
                    } else if (consumerRewardDetail.getRewardStrategy().getRewardMethod().equals(RewardMethodEnum.RED_PACKET.code())) {
                        Map<String, Object> redPacketMap = rewardRuleManager.getConsumerRedPacketSettingInfo(consumerRewardDetail);
                        this.arg.getObjectData().putAll(redPacketMap);
                    }
                }
            } catch (BizException exception) {
                throw new ValidateException(exception.getMessage());
            }
        }
    }

    private void validateAI() {
        mengNiuAICalculateService.validateAIRewardRule((String) arg.getObjectData().get("mn_ai_rule__c"));
    }

    private void validateOrderProductRule() {
        mengNiuAICalculateService.validateOrderProductRule(arg.getObjectData().toObjectData().get("mn_order_product_rule__c", String.class));
    }

    private void saveRewardRule() {
        if (rewardRuleData != null) {
            rewardRuleData.setRelatedObjectApiName(ApiNames.TPM_ACTIVITY_OBJ);
            rewardRuleData.setRelatedObjectId(this.objectData.getId());
            rewardRuleData.setRuleType(activityTypeExt.get().getTemplateId());
            try {
                activityRewardRuleService.add(AddActivityRewardRule.Arg.builder().tenantId(actionContext.getTenantId()).rewardRule(rewardRuleData).build());
            } catch (BizException e) {
                throw new ValidateException(e.getFailureMessage());
            }
        }
    }

    private void validateProductRange() {
        // 是否囤货激励业务类型 客开 sign_in_goods_rewards__c
        String recordType = objectData.getRecordType();
        // 囤货激励添加产品保险度判断
        if (rewardRuleData != null || TPMActivityFields.ActivityRecordType.SIGN_IN_GOODS_REWARDS.equals(recordType)
                && TPMGrayUtils.isMengNiuSignInGoodsFreshStandard(actionContext.getTenantId())) {
            String rangOp = objectData.get(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, String.class);
            if (Strings.isNullOrEmpty(rangOp)) {
                throw new ValidateException(I18N.text(I18NEnums.FRESHNESS_MATCHING_3.getCode()));
            }
            boolean isBigDate = ScanCodeActionConstants.BIG_DATE_ACTIVITY_TYPE_TEMPLATE_ID.equals(ACTIVITY_TYPE_TEMPLATE_ID);
            //非”不限制“和大日期都得有从对象
            if ((!TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT.equals(rangOp) || isBigDate) &&
                    (!this.detailObjectData.containsKey(ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ) || CollectionUtils.isEmpty(this.detailObjectData.get(ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ)))) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_19));
            }
            if (TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT.equals(rangOp)) {
                return;
            }
            Set<String> productIdSet = new HashSet<>();
            Map<String, BigDecimal> repeatMap = new HashMap<>();
            this.detailObjectData.get(ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ).forEach(product -> {
                String productId = product.get(TPMActivityProductRangeFields.PRODUCT_ID, String.class);
                BigDecimal consumerRetailPrice = product.get(TPMActivityProductRangeFields.CONSUMER_RETAIL_PRICE, BigDecimal.class, BigDecimal.ZERO).setScale(2, RoundingMode.DOWN);
                BigDecimal activityDeductAmount = product.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO).setScale(2, RoundingMode.DOWN);
                BigDecimal toBeExpiredDays = product.get(TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS, BigDecimal.class, BigDecimal.ZERO).setScale(2, RoundingMode.DOWN);
                Long manufactureBeginTime = product.get(TPMActivityProductRangeFields.MANUFACTURE_DATE_START, Long.class, 0L);
                Long manufactureEndTime = product.get(TPMActivityProductRangeFields.MANUFACTURE_DATE_END, Long.class, 0L);
                String matchType = product.get(TPMActivityProductRangeFields.MATCH_METHOD, String.class);
                if (manufactureEndTime != 0) {
                    manufactureEndTime = TimeUtils.convertToDayEnd(manufactureEndTime);
                    product.set(TPMActivityProductRangeFields.MANUFACTURE_DATE_END, manufactureEndTime);
                }

                if (TPMGrayUtils.disallowDuplicateProductRange(actionContext.getTenantId())) {
                    if (productIdSet.contains(productId)) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_20));
                    }
                    productIdSet.add(productId);
                } else if (isBigDate) {
                    BigDecimal consumerPrice = repeatMap.get(productId);
                    if (consumerPrice == null) {
                        repeatMap.put(productId, consumerRetailPrice);
                    } else {
                        if (consumerPrice.compareTo(consumerRetailPrice) != 0) {
                            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_21));
                        }
                        String key = String.format("%s.%s.%s", consumerRetailPrice, activityDeductAmount, toBeExpiredDays);
                        if (repeatMap.containsKey(key)) {
                            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_22));
                        }
                        repeatMap.put(key, BigDecimal.ZERO);
                    }
                }
                if (isBigDate) {
                    if (consumerRetailPrice.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_23));
                    }
                    product.set(TPMActivityProductRangeFields.CONSUMER_RETAIL_PRICE, consumerRetailPrice.setScale(2, RoundingMode.DOWN));
                    if (activityDeductAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_24));
                    }
                    product.set(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, activityDeductAmount.setScale(2, RoundingMode.DOWN));
                    if (consumerRetailPrice.compareTo(activityDeductAmount) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_26));
                    }
                }

                if (TPMActivityFields.ProductRangeFreshStandard.BY_REMAINING_DAYS.equals(rangOp)) {
                    if (toBeExpiredDays.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_25));
                    } else if (manufactureBeginTime > 0 || manufactureEndTime > 0) {
                        throw new ValidateException(I18N.text(I18NEnums.FRESHNESS_MATCHING_1.getCode()));
                    } else if (Strings.isNullOrEmpty(matchType)) {
                        throw new ValidateException(I18N.text("fmcg.crm.fmcg.tpm.ACTIVITY_OBJ_reward_match_method_is_empty"));
                    }
                }

                if (TPMActivityFields.ProductRangeFreshStandard.BY_DATE_RANGE.equals(rangOp)) {
                    if (manufactureBeginTime == 0 || manufactureEndTime == 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_BIG_DATE_MANUFACTURE_RANGE_IS_EMPTY));
                    } else if (manufactureBeginTime > manufactureEndTime) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_BIG_DATE_MANUFACTURE_RANGE_START_TIME_BIGGER_THAN_END_TIME));
                    } else if (toBeExpiredDays.compareTo(BigDecimal.ZERO) > 0) {
                        throw new ValidateException(I18N.text(I18NEnums.FRESHNESS_MATCHING_2.getCode()));
                    }
                }

            });
        }
    }
}
