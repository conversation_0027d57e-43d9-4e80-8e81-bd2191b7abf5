package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.predef.action.StandardFlowStartCallbackAction;
import lombok.extern.slf4j.Slf4j;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/30 11:23
 */
@Slf4j
public class TPMBudgetBusinessSubjectObjFlowStartCallbackAction extends StandardFlowStartCallbackAction {

    @Override
    protected void before(Arg arg) {
        log.info("TPMActivityObj flow start call back arg : {}", JSON.toJSONString(arg));
        super.before(arg);
    }
}
