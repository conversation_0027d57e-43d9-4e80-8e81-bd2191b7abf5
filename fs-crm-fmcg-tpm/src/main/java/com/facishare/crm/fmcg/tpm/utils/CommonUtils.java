package com.facishare.crm.fmcg.tpm.utils;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SearchTemplateQueryUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.*;

@SuppressWarnings("Duplicates")
public class CommonUtils {
    private static final long[] DECIMAL_BASE = new long[]{0, 10, 100, 1000, 10000, 100000, 1000000, 10000000, 100000000};

    private static final Logger log = LoggerFactory.getLogger(CommonUtils.class);

    private CommonUtils() {
    }

    public static final int DEFAULT_LIMIT = 250;

    public static <T> List<T> cast(Object obj, Class<T> clazz) {
        if (Objects.isNull(obj)) {
            return Lists.newArrayList();
        }
        List<T> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        throw new ClassCastException();
    }

    public static <T> List<T> castIgnore(Object obj, Class<T> clazz) {
        if (Objects.isNull(obj)) {
            return Lists.newArrayList();
        }
        List<T> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return Lists.newArrayList();
    }

    public static Object getOrDefault(Object value, Object defaultValue) {
        if (value instanceof String) {
            return Strings.isNullOrEmpty((String) value) ? defaultValue : value;
        }
        return value == null ? defaultValue : value;
    }

    public static List<IObjectData> queryData(ServiceFacade serviceFacade, User user, RequestContext context, String apiName, SearchTemplateQuery query, List<String> fields) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();

        int limit = Math.min(500, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (data.size() < max && !(result = serviceFacade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user, context).getContext(), apiName, innerQuery, fields)).getData().isEmpty()) {

            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
        if (offset > 500) {
            log.info("this query offset:{},apiName:{}", offset, apiName);
        }
        return data;
    }

    public static List<IObjectData> queryData(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query, List<String> fields) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();

        int limit = Math.min(500, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (data.size() < max && !(result = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, apiName, innerQuery, fields)).getData().isEmpty()) {

            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
        if (offset > 500) {
            log.info("this query offset:{},apiName:{}", offset, apiName);
        }
        return data;
    }

    public static List<IObjectData> queryData(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query) {
        int maxSize = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();
        int limit = Math.min(500, maxSize);
        int offset = 0;
        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> dataList = Lists.newArrayList();
        QueryResult<IObjectData> result;
        SearchTemplateQuery templateQuery = copy(query);
        while (dataList.size() < maxSize && !(result = serviceFacade.findBySearchQuery(user, apiName, templateQuery)).getData().isEmpty()) {
            dataList.addAll(result.getData());
            offset += result.getData().size();
            templateQuery = copy(query);
            templateQuery.setOffset(offset);
        }
        if (offset > 500) {
            log.info("this query offset:{},apiName:{}", offset, apiName);
        }
        return dataList;
    }

    public static List<IObjectData> queryDataIgnoreAll(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query) {
        int maxSize = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();
        int limit = Math.min(500, maxSize);
        int offset = 0;
        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> dataList = Lists.newArrayList();
        QueryResult<IObjectData> result;
        SearchTemplateQuery templateQuery = copy(query);
        while (dataList.size() < maxSize && !(result = serviceFacade.findBySearchQueryIgnoreAll(user, apiName, templateQuery)).getData().isEmpty()) {
            dataList.addAll(result.getData());
            offset += result.getData().size();
            templateQuery = copy(query);
            templateQuery.setOffset(offset);
        }
        if (offset > 500) {
            log.info("this query offset:{},apiName:{}", offset, apiName);
        }
        return dataList;
    }

    public static void queryData(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query, DataExecutor executor) {
        int maxSize = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();
        int limit = Math.min(500, maxSize);
        int offset = 0;
        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        QueryResult<IObjectData> result;
        SearchTemplateQuery templateQuery = copy(query);
        while (offset < maxSize && !(result = serviceFacade.findBySearchQuery(user, apiName, templateQuery)).getData().isEmpty()) {
            executor.execute(result.getData());
            templateQuery = copy(query);
            offset += result.getData().size();
            templateQuery.setOffset(offset);
        }
        if (offset > 500) {
            log.info("this query offset:{},apiName:{}", offset, apiName);
        }
    }

    /**
     * 该查询仅循环查询前250条数据，如果executor执行不会影响查询条件 不要使用 避免造成死循环
     *
     * @param serviceFacade
     * @param user
     * @param apiName
     * @param query
     * @param resultFields
     * @param maxTimes
     * @param executor
     */
    public static void queryDataWithoutOffset(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query, List<String> resultFields, int maxTimes, DataExecutor executor) {
        int maxSize = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();
        int limit = Math.min(500, maxSize);
        query.setLimit(limit);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setOffset(0);


        if (!resultFields.contains(CommonFields.ID)) {
            resultFields.add(CommonFields.ID);
        }

        QueryResult<IObjectData> result;
        SearchTemplateQuery templateQuery = copy(query);
        int count = 0;
        while (!(result = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, apiName, templateQuery, resultFields)).getData().isEmpty()) {
            executor.execute(result.getData());
            templateQuery = copy(query);
            count++;
            if (count > maxTimes) {
                log.info("reach max times,maxTimes:{},query:{}", maxTimes, query);
                break;
            }
        }
        if (count > 500) {
            log.info("this query count:{},apiName:{}", count, apiName);
        }
    }

    public static Long getEndDateTimeStamp(Long timeStamp) {
        return timeStamp == null ? null : timeStamp + 24 * 60 * 60 * 1000 - 1000;
    }

    public interface DataExecutor {
        void execute(List<IObjectData> partialData);
    }


    @SneakyThrows
    private static SearchTemplateQuery copy(SearchTemplateQuery query) {

        return SearchTemplateQueryUtil.copy(query, true);
    }


    public static boolean isIntersection(Collection<? extends Object> source, Collection<? extends Object> candidates) {
        if (!(source instanceof HashSet)) {
            source = new HashSet<>(source);
        }
        return CollectionUtils.findFirstMatch(source, candidates) != null;
    }

    public static double keepNDecimal(double value, int n) {
        long base = 10;
        if (n >= DECIMAL_BASE.length) {
            for (int i = 0; i < n; i++) base *= 10;
        } else {
            base = DECIMAL_BASE[n];
        }
        return Math.round(value * base) * 1.0 / base;
    }

    /**
     * the sort order will be empty and filled with id asc.
     * the offset will be reset by zero.
     *
     * @param serviceFacade
     * @param user
     * @param apiName
     * @param query
     * @param fields
     * @return
     */

    public static List<IObjectData> queryAllDataInFields(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query, List<String> fields) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();

        int limit = Math.min(500, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        if (!fields.contains(CommonFields.ID)) {
            fields.add(CommonFields.ID);
        }

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = overrideQuery(query, null);
        innerQuery.setOffset(offset);
        while (data.size() < max && !(result = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, apiName, innerQuery, fields)).getData().isEmpty()) {

            data.addAll(result.getData());
            innerQuery = overrideQuery(query, data.get(data.size() - 1).getId());
            innerQuery.setOffset(offset);
        }
        if (data.size() > 500) {
            log.info("this query count:{},apiName:{}", data.size(), apiName);
        }
        return data;
    }


    public static void executeInAllDataWithFields(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query, List<String> fields, DataExecutor dataExecutor) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();

        int limit = Math.min(500, max);

        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        if (!fields.contains(CommonFields.ID)) {
            fields.add(CommonFields.ID);
        }

        IObjectData lastData = null;
        QueryResult<IObjectData> result;

        int count = 0;
        SearchTemplateQuery innerQuery = overrideQuery(query, null);
        innerQuery.setOffset(offset);
        try {
            while (count < max && !(result = serviceFacade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user).getContext(), apiName, innerQuery, fields)).getData().isEmpty()) {

                dataExecutor.execute(result.getData());
                count += result.getData().size();
                lastData = result.getData().get(result.getData().size() - 1);
                innerQuery = overrideQuery(query, lastData.getId());
            }
        } catch (Exception e) {
            log.info("executeInAllDataWithFields error,apiName:{},query:{},fields:{}", apiName, JSON.toJSONString(query), fields);
            throw e;
        }
        if (count > 500) {
            log.info("this query count:{},apiName:{}", count, apiName);
        }
    }


    private static SearchTemplateQuery overrideQuery(SearchTemplateQuery query, String id) {
        SearchTemplateQuery copiedQuery = copy(query);
        copiedQuery.getOrders().clear();
        copiedQuery.getOrders().add(new OrderBy(CommonFields.ID, true));
        if (!Strings.isNullOrEmpty(id)) {
            Filter idFilter = new Filter();
            idFilter.setFieldName(CommonFields.ID);
            idFilter.setOperator(Operator.GT);
            idFilter.setFieldValues(Lists.newArrayList(id));
            copiedQuery.getFilters().add(idFilter);
            if (!Strings.isNullOrEmpty(copiedQuery.getPattern())) {
                int nextNumber = findPatternNextNumber(copiedQuery.getPattern());
                copiedQuery.setPattern("(" + copiedQuery.getPattern() + ") and " + nextNumber);
            }
        }
        return copiedQuery;
    }

    private static int findPatternNextNumber(String pattern) {
        int max = 0;
        int tmp = 0;
        for (char c : pattern.toCharArray()) {
            if (Character.isDigit(c)) {
                tmp = tmp * 10 + c - '0';
            } else {
                max = Math.max(max, tmp);
                tmp = 0;
            }
        }
        max = Math.max(max, tmp);
        return max + 1;
    }
}