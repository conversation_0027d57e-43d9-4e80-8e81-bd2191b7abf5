package com.facishare.crm.fmcg.tpm.api.withdraw;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public interface PreviewWithdraw {

    @Data
    @ToString
    class Arg implements Serializable {

        @SerializedName("red_packet_record_id_list")
        @JSONField(name = "red_packet_record_id_list")
        @JsonProperty("red_packet_record_id_list")
        private List<String> redPacketRecordIdList;

        @SerializedName("white_id_list")
        @JSONField(name = "white_id_list")
        @JsonProperty("white_id_list")
        private List<String> whiteIdList;

        @SerializedName("black_id_list")
        @JSONField(name = "black_id_list")
        @JsonProperty("black_id_list")
        private List<String> blackIdList;

        @SerializedName("amount")
        @JSONField(name = "amount")
        @JsonProperty("amount")
        private String amount;

        private String environment;
    }


    @Data
    @ToString
    class Result implements Serializable {

        @SerializedName("amount")
        @JSONField(name = "amount")
        @JsonProperty("amount")
        private BigDecimal amount;

        @SerializedName("received_account")
        @JSONField(name = "received_account")
        @JsonProperty("received_account")
        private String receivedAccount;

    }
}