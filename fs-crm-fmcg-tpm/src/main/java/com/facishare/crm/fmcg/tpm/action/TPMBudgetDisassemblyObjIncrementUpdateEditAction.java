package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;


@SuppressWarnings("all")
public class TPMBudgetDisassemblyObjIncrementUpdateEditAction extends StandardIncrementUpdateAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException("increment update not allowed!");
    }
}