package com.facishare.crm.fmcg.tpm.action;

import com.google.common.collect.Lists;
import com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction;
import com.facishare.paas.appframework.core.predef.action.BaseInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;


/**
 * author: wuyx
 * description:
 * createTime: 2022/6/29 18:37
 */
@Slf4j
public class TPMBudgetTransferDetailObjInsertImportTemplateAction extends BaseInsertImportTemplateAction<BaseImportTemplateAction.Arg> {

    @Override
    protected List<IFieldDescribe> getImportTemplateField(Arg arg) {
        log.info("init getImportTemplateField arg={}", arg);
        List<IFieldDescribe> templateFields = super.getImportTemplateField(arg);
        List<String> disableFields = Lists.newArrayList("amount_before_transfer_in", "amount_before_transfer_out");
        return templateFields.stream().filter(iFieldDescribe -> !disableFields.contains(iFieldDescribe.getApiName())).collect(Collectors.toList());
    }

}
