package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IRedisLockService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.business.dto.BudgetConsumeSession;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/6/21 上午10:46
 */
@Slf4j
@Component
public class RedisLockService implements IRedisLockService {

    public static final String DEL_TEMPLATE = "if redis.call('get',KEYS[1]) == ARGV[1] then return redis.call('del',KEYS[1]) else return 0 end";

    public static final long MAX_WAIT_SECONDS = 60 * 5;

    public static final long MAX_WAIT_MILLISECONDS = 1000 * MAX_WAIT_SECONDS;

    public static final long ROTATION_INTERVAL = 150;

    private static final ThreadLocal<Map<String, String>> THREAD_LOCAL_MAP = new ThreadLocal<>();

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;


    @Override
    public Boolean tryLock(RequestContext requestContext, String key, String val) {
        return tryLock(requestContext, key, val, -1);
    }

    @Override
    public Boolean tryLock(RequestContext requestContext, String key, String val, long waitTime) {
        requestContext.setAttribute(REDIS_SESSION_PREFIX + UUID.randomUUID().toString(), key + "#" + val);
        return tryLock(key, val, waitTime);
    }

    @Override
    public void unLock(RequestContext requestContext) {
        for (Object sessionKey : requestContext.getAttributes().keySet()) {
            if (sessionKey != null && sessionKey.toString().startsWith(REDIS_SESSION_PREFIX)) {
                String sessionVal = requestContext.getAttribute(sessionKey.toString());
                try {
                    if (!Strings.isNullOrEmpty(sessionVal)) {
                        String[] args = sessionVal.split("#");
                        unLock(args[0], args[1]);
                    }
                } catch (Exception e) {
                    log.error("unlock err.", e);
                }
            }
        }
        THREAD_LOCAL_MAP.remove();
    }

    @Override
    public Boolean tryLock(BudgetConsumeSession session, String key, String val, long waitTime) {
        session.setAttribute(REDIS_SESSION_PREFIX + UUID.randomUUID().toString(), key + "#" + val);
        return tryLock(key, val, waitTime);
    }

    @Override
    public void unLock(BudgetConsumeSession session) {
        Map<String, String> allData = session.all();
        for (String sessionKey : allData.keySet()) {
            if (sessionKey != null && sessionKey.startsWith(REDIS_SESSION_PREFIX)) {
                String sessionVal = session.getAttribute(sessionKey);
                try {
                    if (!Strings.isNullOrEmpty(sessionVal)) {
                        String[] args = sessionVal.split("#");
                        unLock(args[0], args[1]);
                    }
                } catch (Exception e) {
                    log.error("unlock err.", e);
                }
            }
        }
        THREAD_LOCAL_MAP.remove();
    }

    @Override
    public Boolean tryLock(String key, String val) {
        return tryLock(key, val, -1);
    }

    @Override
    public Boolean tryLock(String key, String val, long waitTime) {
        if (getLocalMap().containsKey(key)) {
            return true;
        }
        long cumulativeTime = 0;
        SetParams params = new SetParams().nx().px(MAX_WAIT_MILLISECONDS);
        while (!"ok".equalsIgnoreCase(redisCmd.set(key, val, params))) {
            try {
                Thread.sleep(ROTATION_INTERVAL);
                cumulativeTime += ROTATION_INTERVAL;
            } catch (InterruptedException e) {
                log.info("redis rotation fail.", e);
                throw new ValidateException(I18N.text(I18NKeys.REDIS_LOCK_SERVICE_0));
            }
            if (cumulativeTime > 0 && cumulativeTime >= waitTime) {
                return false;
            }
        }
        getLocalMap().putIfAbsent(key, val);
        return true;
    }

    @Override
    public void unLock(String key, String val) {
        getLocalMap().remove(key);
        redisCmd.eval(DEL_TEMPLATE, Lists.newArrayList(key), Lists.newArrayList(val));
    }

    @Override
    public Boolean uniqueValidate(BudgetConsumeSession session, String key, String val, long waitTime) {
        session.setAttribute(REDIS_SESSION_PREFIX + UUID.randomUUID().toString(), key + "#" + val);
        String res = redisCmd.set(key, val, SetParams.setParams().px(waitTime).nx());
        return "OK".equalsIgnoreCase(res);
    }


    private static synchronized Map<String, String> getLocalMap() {
        Map<String, String> map = THREAD_LOCAL_MAP.get();
        if (map == null) {
            map = new HashMap<>();
            THREAD_LOCAL_MAP.set(map);
        }
        return map;
    }
}
