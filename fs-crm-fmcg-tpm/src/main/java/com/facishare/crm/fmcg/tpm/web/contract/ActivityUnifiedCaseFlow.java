package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/14 16:14
 */
public interface ActivityUnifiedCaseFlow {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "activity_unified_case_id")
        @JsonProperty(value = "activity_unified_case_id")
        @SerializedName("activity_unified_case_id")
        private String activityUnifiedCaseId;

    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        // true 有关联，false 无关联
        @JSONField(name = "activity_flow")
        @JsonProperty(value = "activity_flow")
        @SerializedName("activity_flow")
        private Boolean activityFlow;

        @JSONField(name = "dealer_record_type")
        @JsonProperty(value = "dealer_record_type")
        @SerializedName("dealer_record_type")
        private List<String> dealerRecordType;

    }

}
