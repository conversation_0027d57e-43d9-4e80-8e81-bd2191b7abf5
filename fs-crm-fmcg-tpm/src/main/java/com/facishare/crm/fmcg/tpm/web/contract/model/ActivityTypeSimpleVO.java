package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 16:24
 */
@Data
@ToString
@SuppressWarnings("Duplicates")
public class ActivityTypeSimpleVO implements Serializable {

    @JSONField(name = "_id")
    @JsonProperty(value = "_id")
    @SerializedName("_id")
    private String id;

    private String name;

    @JSONField(name = "activity_plan_report_data")
    @JsonProperty(value = "activity_plan_report_data")
    @SerializedName("activity_plan_report_data")
    private ActivityPlanReportDatumVO activityPlanReportData;

    public static ActivityTypeSimpleVO fromPO(ActivityTypePO po) {
        if (po == null) {
            return null;
        }
        ActivityTypeSimpleVO vo = new ActivityTypeSimpleVO();
        vo.setId(po.getId().toString());
        vo.setName(po.getName());
        return vo;
    }
}
