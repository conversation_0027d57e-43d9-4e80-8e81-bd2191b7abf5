package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.paas.metadata.api.IObjectData;
import groovy.lang.Tuple2;

import java.util.Set;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/9/15 14:14
 */
public interface IBudgetCompareService {

    boolean notSameDimension(Set<String> fields, IObjectData sourceData, IObjectData targetData);

    boolean notTheSpecifiedDepartmentLevel(String tenantId, int level, int departmentId);

    void childAccountValidate(
            String tenantId,
            BudgetTypeNodeEntity sourceNode,
            IObjectData sourceAccount,
            BudgetTypeNodeEntity targetNode,
            IObjectData targetAccount
    );
}
