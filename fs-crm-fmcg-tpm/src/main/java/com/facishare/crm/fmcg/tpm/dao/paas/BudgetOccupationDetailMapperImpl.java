package com.facishare.crm.fmcg.tpm.dao.paas;

import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetOccupationDetailFields;
import com.facishare.crm.fmcg.tpm.utils.FormatUtil;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.fxiaoke.common.SqlEscaper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/7/23 14:02
 */
@Component
public class BudgetOccupationDetailMapperImpl implements BudgetOccupationDetailMapper {


    @Resource
    private SpecialTableMapper specialTableMapper;

    @Override
    public int updateOccupationStatus(String tenantId, List<Map<String, Object>> data) {
        StringBuilder finalSql = new StringBuilder();
        for (Map<String, Object> map : data) {
            StringBuilder sqlBuilder = new StringBuilder("update fmcg_tpm_budget_occupation_detail set  ");
            Object tmp;
            if ((tmp = map.get(TPMBudgetOccupationDetailFields.OCCUPIED_STATUS)) != null) {
                sqlBuilder.append(TPMBudgetOccupationDetailFields.OCCUPIED_STATUS).append(" = '").append(SqlEscaper.pg_escape(tmp.toString())).append("',");
            }
            if ((tmp = map.get(CommonFields.LAST_MODIFY_TIME)) != null) {
                sqlBuilder.append(CommonFields.LAST_MODIFY_TIME).append(" = ").append(SqlEscaper.pg_escape(tmp.toString())).append(",");
            }
            sqlBuilder.setCharAt(sqlBuilder.length() - 1, ' ');
            sqlBuilder.append("   where id = '#{_id}' and tenant_id ='#{tenant_id}';");
            finalSql.append(sqlBuilder.toString().replace("#{_id}", SqlEscaper.pg_escape((String) map.get("_id"))).replace("#{tenant_id}", SqlEscaper.pg_escape(tenantId))).append('\n');
        }
        return specialTableMapper.setTenantId(tenantId).batchUpdateBySql(finalSql.toString());
    }

    @Override
    public BigDecimal statisticOccupiedMoney(String tenantId, String budgetAccountId) {
        String sql = "select sum(amount) sum_amount from fmcg_tpm_budget_occupation_detail where budget_account_id = '#{budget_account_id}' and  tenant_id = '#{tenant_id}' and occupied_status = '0' and is_deleted = 0 and amount is not null;";
        sql = sql.replaceAll("#\\{budget_account_id}", SqlEscaper.pg_escape(budgetAccountId)).replaceAll("#\\{tenant_id}", SqlEscaper.pg_escape(tenantId));
        List<Map> result = specialTableMapper.setTenantId(tenantId).findBySql(sql);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        Map data = result.get(0);
        if (data == null) {
            return null;
        }

        return new BigDecimal(data.get("sum_amount").toString());
    }

    @Override
    public Map<String, BigDecimal> statisticOccupiedMoney(String tenantId, List<String> budgetAccountIds) {
        Map<String, BigDecimal> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(budgetAccountIds)) {
            return resultMap;
        }
        String sql = "select budget_account_id,sum(amount) amount from fmcg_tpm_budget_occupation_detail where budget_account_id in #{budgetAccountIds} and  tenant_id = '#{tenant_id}' and occupied_status = '0' and is_deleted = 0 and amount is not null group by budget_account_id;";
        sql = sql.replaceAll("#\\{budgetAccountIds}", FormatUtil.formSqlArray(budgetAccountIds)).replaceAll("#\\{tenant_id}", tenantId);
        List<Map> result = specialTableMapper.setTenantId(tenantId).findBySql(sql);
        if (CollectionUtils.isEmpty(result)) {
            return resultMap;
        }
        result.forEach(map -> resultMap.put((String) map.get("budget_account_id"), new BigDecimal(map.get("amount").toString())));
        return resultMap;
    }
}
