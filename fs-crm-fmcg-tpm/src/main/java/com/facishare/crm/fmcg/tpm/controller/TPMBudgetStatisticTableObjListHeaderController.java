package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.ListHeaderBusiness;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/5 16:02
 */
public class TPMBudgetStatisticTableObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {

        List<String> hiddenActions = Lists.newArrayList("Import", "Add", "AsyncBulkInvalid");
        ListHeaderBusiness.listHeaderFilter(controllerContext.getTenantId(), ApiNames.TPM_BUDGET_STATISTIC_TABLE, result);
        List<JSONObject> buttons = CommonUtils.cast(result.getLayout().get("buttons"), JSONObject.class);
        result.getLayout().put("buttons", buttons.stream()
                .filter(button -> !hiddenActions.contains(String.valueOf(button.getString("action"))))
                .collect(Collectors.toList()));
        return super.after(arg, result);
    }
}
