package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetFieldRelationEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTableNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IBudgetAccountService {

    void validate(
            String tenantId,
            BudgetTypePO type,
            BudgetTypeNodeEntity node,
            IObjectData budget);

    ILayout overrideLayout(
            ILayout layout,
            BudgetTypeNodeEntity node);

    List<IObjectData> queryRelatedBudgetByControlDimension(User user, String budgetType, IObjectData baseBudget);

    List<IObjectData> querySonBudgets(User user, String parentId);

    IObjectData getBudgetByConsumeRuleTemplateEntity(User user, String budgetType, BudgetTableNodeEntity budgetTableNodeEntity, IObjectData relatedData);

    IObjectData buildDefaultValForCreate();

    IObjectData createBudgetAccount(User user, IObjectData account, boolean isTriggerApproval, boolean isTriggerWorkFlow, boolean isSonBudget, boolean skipBudgetStatistic);

    void preValidateAccount(User user, IObjectData account, boolean isSonBudget);

    void enableBudget(User user, IObjectData budget);

    String formControlDimensionCode(IObjectData budget, BudgetTypeNodeEntity budgetTypeNodeEntity);

    IObjectData getBudgetByFieldRelation(User user, String budgetType, String nodeId, List<BudgetFieldRelationEntity> fieldRelationEntities, IObjectData relatedData);


}
