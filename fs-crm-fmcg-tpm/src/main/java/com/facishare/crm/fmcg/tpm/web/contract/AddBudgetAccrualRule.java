package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetAccrualRuleVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/9 下午2:39
 */
public interface AddBudgetAccrualRule {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "budget_accrual_rule")
        @JsonProperty(value = "budget_accrual_rule")
        @SerializedName("budget_accrual_rule")
        private BudgetAccrualRuleVO budgetAccrualRuleVO;
    }


    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "budget_accrual_rule")
        @JsonProperty(value = "budget_accrual_rule")
        @SerializedName("budget_accrual_rule")
        private BudgetAccrualRuleVO budgetAccrualRuleVO;
    }
}
