package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.api.SimpleDTO;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ConfigDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigPO;
import com.facishare.crm.fmcg.tpm.web.contract.AddConfig;
import com.facishare.crm.fmcg.tpm.web.contract.CheckRewardTagUsage;
import com.facishare.crm.fmcg.tpm.web.contract.GetConfig;
import com.facishare.crm.fmcg.tpm.web.contract.GetConfigDescribe;
import com.facishare.crm.fmcg.tpm.web.contract.ListConfig;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.facishare.crm.fmcg.tpm.web.designer.IConfigManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wuyx
 * description:
 * createTime: 2023/3/20 14:28
 */
//IgnoreI18nFile
@Slf4j
@Service("tpmConfigService")
@SuppressWarnings("Duplicates")
public class ConfigService extends BaseService implements IConfigService {

    @Resource
    private ConfigDAO configDAO;

    @Resource
    private ActivityRewardRuleDAO activityRewardRuleDAO;

    @Resource
    private IConfigManager configManager;

    @Resource
    private TPM2Service tpm2Service;


    @Override
    public AddConfig.Result save(AddConfig.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        List<ConfigVO> vos = arg.getConfigs();
        if (Objects.isNull(vos)) {
            throw new ValidateException("arg is empty");
        }
        configManager.basicValidation(context.getTenantId(), vos);
        configManager.validation(context.getTenantId(), vos);

        List<ConfigPO> pos = vos.stream().map(ConfigPO::toPO).collect(Collectors.toList());
        filterNoChangePO(context.getTenantId(), pos);
        if (CollectionUtils.isNotEmpty(pos)) {
            configDAO.saveOrUpdate(context.getTenantId(), context.getEmployeeId(), pos);

            configManager.after(context.getTenantId(), vos);
        }

        return AddConfig.Result.builder().build();
    }

    private void filterNoChangePO(String tenantId, List<ConfigPO> pos) {
        List<ConfigPO> dbPos = configDAO.queryByKeys(tenantId, pos.stream().map(ConfigPO::getKey).collect(Collectors.toList()));
        pos.removeIf(po -> {
            ConfigPO dbPo = dbPos.stream().filter(p -> p.getKey().equals(po.getKey())).findFirst().orElse(null);
            return dbPo != null && dbPo.getValue().equals(po.getValue());
        });
    }

    @Override
    public ListConfig.Result list(ListConfig.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        List<ConfigPO> pos = configDAO.queryAll(context.getTenantId());
        changeType(pos);
        addDefaultConfig(context.getTenantId(), pos);

        List<ConfigVO> vos = pos.stream().map(ConfigPO::toVO).collect(Collectors.toList());
        return ListConfig.Result.builder().configs(vos).build();
    }

    private void changeType(List<ConfigPO> pos) {
        for (ConfigPO po : pos) {
            if (po.getKey().equals(ConfigEnum.DEALER_RECORD_TYPE.key())) {
                if (po.getValue() instanceof String) {
                    po.setValue(Lists.newArrayList(po.getValue()));
                    configDAO.saveOrUpdate(po.getTenantId(), -10000, Lists.newArrayList(po));
                }
            }
        }
    }

    private void addDefaultConfig(String tenantId, List<ConfigPO> pos) {
        Map<String, Object> alreadyCfg = pos.stream().collect(Collectors.toMap(ConfigPO::getKey, ConfigPO::getValue));
        for (Map.Entry<String, Object> def : ConfigEnum.getDefault().entrySet()) {
            if (!alreadyCfg.containsKey(def.getKey())) {
                ConfigPO config = new ConfigPO();
                config.setKey(def.getKey());
                config.setValue(def.getValue());
                config.setTenantId(tenantId);
                pos.add(config);
                if (def.getKey().equals(ConfigEnum.REWARD_TAG.key())) {
                    config.setValue(JSON.parseArray(JSON.toJSONString(config.getValue())));
                    configDAO.add(tenantId, -10000, config);
                }
            }
        }
    }

    private ConfigPO getDefaultConfigPO(String key, ConfigPO po) {
        if (po == null) {
            Object value = ConfigEnum.getDefault().get(key);
            if (Objects.nonNull(value) || (value instanceof List && CollectionUtils.isNotEmpty((List) value))) {
                ConfigPO config = new ConfigPO();
                config.setKey(key);
                config.setValue(value);
                return config;
            }
        }
        return po;
    }


    @Override
    public GetConfig.Result get(GetConfig.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        ConfigPO po = configDAO.queryByKey(context.getTenantId(), arg.getKey());
        return GetConfig.Result.builder().config(ConfigPO.toVO(getDefaultConfigPO(arg.getKey(), po))).build();
    }

    @Override
    public GetConfigDescribe.Result getDescribe(GetConfigDescribe.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        GetConfigDescribe.Result result = new GetConfigDescribe.Result();
        Map<String, GetConfigDescribe.ConfigField> fieldMap = new HashMap<>();
        result.setFields(fieldMap);
        fieldMap.put(ConfigEnum.ACTIVATED_SCAN_CODE_ACTION.key(),
                GetConfigDescribe.ConfigField.builder().isShow(TPMGrayUtils.isSupportStockCheckReward(context.getTenantId())).key(ConfigEnum.ACTIVATED_SCAN_CODE_ACTION.key()).type("select_many").options(getActivatedActionList(context.getTenantId())).build());
        fieldMap.put(ConfigEnum.REWARD_TAG.key(),
                GetConfigDescribe.ConfigField.builder().isShow(TPMGrayUtils.isSupportRewardTag(context.getTenantId()) || tpm2Service.existTPMCodeLicenseTenant(Integer.valueOf(context.getTenantId()))).key(ConfigEnum.REWARD_TAG.key()).type("select_many").options(Lists.newArrayList()).build());
        return result;
    }


    @Override
    public CheckRewardTagUsage.Result checkRewardTagUsage(CheckRewardTagUsage.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String rewardTag = arg.getRewardTag();

        if (rewardTag == null || rewardTag.trim().isEmpty()) {
            return CheckRewardTagUsage.Result.builder()
                    .isUsed(false)
                    .build();
        }

        boolean isUsed = activityRewardRuleDAO.existsByRewardTag(context.getTenantId(), rewardTag);

        return CheckRewardTagUsage.Result.builder()
                .isUsed(isUsed)
                .build();
    }

    private List<SimpleDTO> getActivatedActionList(String tenantId) {

        return Lists.newArrayList(SimpleDTO.builder().name("门店库存盘点").value("STORE_STOCK_CHECK").build(),
                SimpleDTO.builder().name("门店销量出库").value("STORE_SALES").build(),
                SimpleDTO.builder().name("门店签收").value("STORE_SIGN").build(),
                SimpleDTO.builder().name("销售出库").value("SALES_OUT_OF_WAREHOUSE").build());
    }

}
