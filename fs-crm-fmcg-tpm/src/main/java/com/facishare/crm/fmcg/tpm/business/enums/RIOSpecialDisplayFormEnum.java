package com.facishare.crm.fmcg.tpm.business.enums;

/**
 * RIO专用陈列形式枚举
 */
public enum RIOSpecialDisplayFormEnum {
    /**
     * 端架
     */
    GE("端架", "GE"),
    
    /**
     * 货架
     */
    SHELF("货架", "shelf"),
    
    /**
     * 侧货架
     */
    SIDE_SHELF("侧货架", "side_shelf"),
    
    /**
     * 冰柜
     */
    FREEZER("冰柜", "freezer"),
    
    /**
     * 品牌冰柜
     */
    BRAND_FREEZER("品牌冰柜", "brand_freezer"),
    
    /**
     * 地堆
     */
    GROUND_PILE("地堆", "TG"),
    
    /**
     * TG货架
     */
    TG_SHELF("TG", "TG-shelf"),
    
    /**
     * 割箱
     */
    CUT_BOX("割箱", "cutbox"),
    
    /**
     * 地龙
     */
    GROUND_DRAGON("地龙", "DL"),
    
    /**
     * 挂钩货架
     */
    HOOK_SHELF("挂钩货架", "HS"),
    
    /**
     * 斜口篮货架
     */
    OBS_SHELF("斜口篮货架", "OBS"),
    
    /**
     * 小端架
     */
    SMALL_END_SHELF("小端架", "SGE"),
    
    /**
     * 吧台
     */
    BAR_COUNTER("吧台", "barcounter"),
    
    /**
     * 卧式冰柜
     */
    HORIZONTAL_FREEZER("卧式冰柜", "HF"),
    
    /**
     * 冷风柜
     */
    COOLING_CABINET("冷风柜", "OACR"),
    
    /**
     * 挂钩端架
     */
    HOOK_END_SHELF("挂钩端架", "HGE"),
    
    /**
     * 未知场景
     */
    UNKNOWN("未知场景", "unknown");

    /**
     * [
     *   {"label":"端架","value":"GE"},
     *   {"label":"货架","value":"shelf"},
     *   {"label":"侧货架","value":"side_shelf"},
     *   {"label":"冰柜","value":"freezer"},
     *   {"label":"品牌冰柜","value":"brand_freezer"},
     *   {"label":"地堆","value":"TG"},
     *   {"label":"TG","value":"TG-shelf"},
     *   {"label":"割箱","value":"cutbox"},
     *   {"label":"地龙","value":"DL"},
     *   {"label":"挂钩货架","value":"HS"},
     *   {"label":"斜口篮货架","value":"OBS"},
     *   {"label":"小端架","value":"SGE"},
     *   {"label":"吧台","value":"barcounter"},
     *   {"label":"卧式冰柜","value":"HF"},
     *   {"label":"冷风柜","value":"OACR"},
     *   {"label":"挂钩端架","value":"HGE"},
     *   {"label":"未知场景","value":"unknown"}
     * ]
     */
    private final String displayName;
    private final String fieldKey;
    
    RIOSpecialDisplayFormEnum(String displayName, String fieldKey) {
        this.displayName = displayName;
        this.fieldKey = fieldKey;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getFieldKey() {
        return fieldKey;
    }
    
    /**
     * 根据显示名称获取枚举值
     * @param displayName 显示名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static RIOSpecialDisplayFormEnum getByDisplayName(String displayName) {
        if (displayName == null) {
            return null;
        }
        
        for (RIOSpecialDisplayFormEnum field : values()) {
            if (field.displayName.equals(displayName)) {
                return field;
            }
        }
        return null;
    }
    
    /**
     * 根据字段键获取枚举值
     * @param fieldKey 字段键
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static RIOSpecialDisplayFormEnum getByFieldKey(String fieldKey) {
        if (fieldKey == null) {
            return null;
        }
        
        for (RIOSpecialDisplayFormEnum field : values()) {
            if (field.fieldKey.equals(fieldKey)) {
                return field;
            }
        }
        return null;
    }
} 