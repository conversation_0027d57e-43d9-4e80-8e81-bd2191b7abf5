package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.common.constant.CallBackDataKeys;
import com.facishare.crm.fmcg.tpm.utils.FormatUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/22 下午3:54
 */
@Slf4j
public class TPMBudgetAccrualObjEditAction extends StandardEditAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String lifeStatus = this.objectData.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__INEFFECTIVE.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_EDIT_ACTION_0));
        }
        checkAndFill(arg);
        setCallBackData();
    }

    private void setCallBackData() {
        this.customCallbackData.put(CallBackDataKeys.ACCRUAL_DATA_NEED_BUDGET_ACCRUAL_FLAG, true);
    }

    private void checkAndFill(Arg arg) {
        IObjectData masterData = arg.getObjectData().toObjectData();
        String masterBudgetTypeId = masterData.get(TPMBudgetAccrualFields.BUDGET_TYPE_ID, String.class);

        if (!CollectionUtils.isEmpty(arg.getDetails().get(ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ))) {
            List<IObjectData> details = arg.getDetails().get(ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
            Set<String> detailBudgetIds = details.stream().map(v -> v.get(TPMBudgetAccrualDetailFields.BUDGET_ACCOUNT_ID, String.class)).collect(Collectors.toSet());
            if (detailBudgetIds.size() != details.size()) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_EDIT_ACTION_1));
            }
            Map<String, IObjectData> budgetMap = serviceFacade.findObjectDataByIdsIgnoreAll(actionContext.getTenantId(), new ArrayList<>(detailBudgetIds), ApiNames.TPM_BUDGET_ACCOUNT).stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (before, after) -> before));
            List<String> notTheSameBudgetNames = Lists.newArrayList();
            details.forEach(detail -> {
                String budgetId = detail.get(TPMBudgetAccrualDetailFields.BUDGET_ACCOUNT_ID, String.class);
                IObjectData budget = budgetMap.get(budgetId);
                if (budget == null) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_EDIT_ACTION_2), budgetId));
                }
                String detailBudgetType = budget.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
                String detailBudgetTypeFromData = detail.get(TPMBudgetAccrualDetailFields.BUDGET_TYPE_ID, String.class);
                if (!Strings.isNullOrEmpty(masterBudgetTypeId) && !masterBudgetTypeId.equals(detailBudgetType)) {
                    notTheSameBudgetNames.add(budget.getName());
                }
                if (!Strings.isNullOrEmpty(detailBudgetTypeFromData) && !detailBudgetTypeFromData.equals(detailBudgetType)) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_EDIT_ACTION_3), budget.getName()));
                }
                detail.set(TPMBudgetAccrualDetailFields.STATUS, TPMBudgetAccrualDetailFields.Status.EXCLUDE);
            });
            if (!CollectionUtils.isEmpty(notTheSameBudgetNames)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_OBJ_EDIT_ACTION_4) + FormatUtil.formSqlArray(notTheSameBudgetNames));
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result finalResult = super.after(arg, result);
        if (!this.isApprovalFlowStartSuccess(arg.getObjectData().getId())) {
            budgetAccrual(arg.getObjectData().getId());
        }
        return finalResult;
    }

    public void budgetAccrual(String dataId) {
        try {
            ActionContext newActionContext = new ActionContext(actionContext.getRequestContext(), actionContext.getObjectApiName(), "BudgetAccrual");
            newActionContext.setAttribute("skipBaseValidate", true);
            TPMBudgetAccrualObjBudgetAccrualAction.Result accrualResult = serviceFacade.triggerAction(newActionContext, TPMBudgetAccrualObjBudgetAccrualAction.Arg.of(null, dataId), TPMBudgetAccrualObjBudgetAccrualAction.Result.class);
            log.info("budget accrual action result:{}", accrualResult);
        } catch (Exception e) {
            log.info("trigger budget accrual err.", e);
        }
    }

}
