package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2022/5/19 19:54
 */
@Entity(value = "fmcg_tpm_activity_type_draft_box", noClassnameStored = true)
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class ActivityTypeDraftBoxPO extends MongoPO {

    // basic information
    public static final String F_NAME = "name";
    public static final String F_TEMPLATE_ID = "template_id";
    public static final String F_API_NAME = "api_name";
    public static final String F_DESCRIPTION = "description";
    public static final String F_VERSION = "version";
    public static final String F_PACKAGE = "package";
    public static final String F_STATUS = "status";
    public static final String F_EXCEPTION_STATUS = "exception_status";

    // use scope
    public static final String F_EMPLOYEE_IDS = "employee_ids";
    public static final String F_DEPARTMENT_IDS = "department_ids";
    public static final String F_ROLE_IDS = "role_ids";
    public static final String F_ALL_EMPLOYEE_IDS = "all_employee_ids";
    public static final String F_SCOPE_DESCRIPTION = "scope_description";
    public static final String F_ACTIVITY_NODES = "activity_nodes";
    public static final String F_MUST_RELATED_CUSTOMER = "must_related_customer";

    public static final String F_TEMPLATE_NAME = "template_name";


    @Property(F_NAME)
    private String name;

    @Property(F_TEMPLATE_ID)
    private String templateId;

    @Property(F_API_NAME)
    private String apiName;

    @Property(F_DESCRIPTION)
    private String description;

    @Property(F_VERSION)
    private long version;

    @Property(F_PACKAGE)
    private String packageType;

    @Property(F_STATUS)
    private String status;

    @Property(F_EXCEPTION_STATUS)
    private String exceptionStatus;

    @Property(F_EMPLOYEE_IDS)
    private List<Integer> employeeIds;

    @Property(F_DEPARTMENT_IDS)
    private List<Integer> departmentIds;

    @Property(F_ROLE_IDS)
    private List<String> roleIds;

    @Property(F_ALL_EMPLOYEE_IDS)
    private List<Integer> allEmployeeIds;

    @Property(F_SCOPE_DESCRIPTION)
    private String scopeDescription;

    @Embedded(F_ACTIVITY_NODES)
    private List<ActivityNodeEntity> activityNodes;

    @Property(F_MUST_RELATED_CUSTOMER)
    private Boolean mustRelatedCustomer;

    @Property(F_TEMPLATE_NAME)
    private String templateName;
}
