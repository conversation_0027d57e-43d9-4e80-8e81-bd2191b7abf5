package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/14 下午7:37
 */
@Data
@ToString
public class BudgetTableManualNodeVO implements Serializable {

    @JSONField(name = "node_id")
    @JsonProperty(value = "node_id")
    @SerializedName("node_id")
    private String nodeId;

    @JSONField(name = "budget_table_frozen")
    @JsonProperty(value = "budget_table_frozen")
    @SerializedName("budget_table_frozen")
    private BudgetTableFrozenVO budgetTableFrozen;

    @JSONField(name = "budget_table_release")
    @JsonProperty(value = "budget_table_release")
    @SerializedName("budget_table_release")
    private List<BudgetTableReleaseVO> budgetTableRelease;

    @JSONField(name = "budget_table_deduct")
    @JsonProperty(value = "budget_table_deduct")
    @SerializedName("budget_table_deduct")
    private BudgetTableDeductVO budgetTableDeDuct;


}
