package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.web.contract.BudgetStatisticTableRefresh;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/5 14:43
 */
public interface IBudgetStatisticTableService {

    void asyncDoStatistic(String tenantId, IObjectData account);

    void asyncDoStatistic(String tenantId, List<IObjectData> accounts);

    BudgetStatisticTableRefresh.Result refresh(String tenantId, User user, String id);
}
