package com.facishare.crm.fmcg.others.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.others.i18n.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


@SuppressWarnings("Duplicates")
public class PointsRewardDetailIncrementUpdateAction extends StandardIncrementUpdateAction {


    private static List<String> ALLOW_LIST = Lists.newArrayList("account_id");


    static {
        ConfigFactory.getConfig("gray-rel-fmcg", iConfig -> {
            List<String> allowList = new ArrayList<>();
            List<String> list = JSON.parseArray(iConfig.get("POINTS_ALLOW_EDIT_LIST"), String.class);
            if (!CollectionUtils.isEmpty(list)) {
                allowList.addAll(list);
                allowList.addAll(Lists.newArrayList("account_id"));
                ALLOW_LIST = allowList;
            }
        });

    }

    @Override
    protected void before(Arg arg) {
        if (actionContext.isFromFunction() || actionContext.isFromOpenAPI()) {
            arg.getData().keySet().forEach(key -> {
                if (!Strings.isNullOrEmpty(key) && !key.endsWith("__c") && !ALLOW_LIST.contains(key))
                    throw new ValidateException(I18N.text(I18NKeys.VALIDATE_POINTS_REWARD_DETAIL_INCREMENT_UPDATE_ACTION_0));
            });
        }
        super.before(arg);
    }

}
