package com.facishare.crm.fmcg.yqsl.api;

import com.alibaba.fastjson.JSONObject;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface AplFunctionNearbyAccounts {

    @Data
    @ToString
    class Arg implements Serializable {

        private String latitude;

        private String longitude;

        private Double distance;

        private List<String> fields;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private List<JSONObject> data;
    }
}