package com.facishare.crm.fmcg.mengniu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.file.FileCenterAdapter;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.StoreAuditDataList;
import com.facishare.crm.fmcg.tpm.web.contract.StoreAuditSummaryList;
import com.facishare.crm.fmcg.tpm.web.contract.StoreOperateAudit;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Image;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.common.Pair;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class StoreAuditService implements IStoreAuditService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource
    private ActivityTypeManager activityTypeManager;

    @Resource(name = "tpmRoleService")
    private IRoleService roleService;

    @Resource
    private TransactionProxy transactionProxy;

    @Resource
    private RedissonClient redissonCmd;

    @Resource
    private FileCenterAdapter fileCenterAdapter;

    @Resource
    protected InfraServiceFacade infraServiceFacade;

    @Resource
    private EIEAConverter eieaConverter;

    public static final String AUDIT_DATA_KEY = "audit_data";
    public static final String APPLICATION_DATA_KEY = "application_data";
    public static final String VISIT_DATA_KEY = "visit_data";

    private static String AREA_USER_ROLE;
    private static String MARKETING_USER_ROLE;

    private static final List<String> PENETRATION_AUDIT_STORE_TYPES = Arrays.asList("5", "7", "8", "9", "10");

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", config -> {
            AREA_USER_ROLE = config.get("AREA_USER_ROLE");
            MARKETING_USER_ROLE = config.get("MARKETING_USER_ROLE");
        });
    }


    @Override
    public StoreAuditSummaryList.Result auditSummaryList(StoreAuditSummaryList.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String dataId = arg.getDataId();

        // 1. 参数校验：活动门店ID不能为空
        if (Strings.isNullOrEmpty(dataId)) {
            log.info("auditSummaryList: dataId is empty, arg={}", arg);
            return buildEmptyResult();
        }

        // 2. 获取下游租户ID
        String downstreamTenantId = getDownStreamTenantId(arg.getOutTenantId(), context.getTenantId(), arg.getDealerId());
        if (Strings.isNullOrEmpty(downstreamTenantId)) {
            log.info("auditSummaryList: downstreamTenantId is empty, arg={}", arg);
            return buildEmptyResult();
        }

        // 3. 查询下游企业的自定义参与门店数据
        String activityStoreType = getActivityStore(downstreamTenantId, arg.getDataId());
        if (!PENETRATION_AUDIT_STORE_TYPES.contains(activityStoreType)) {
            log.info("auditSummaryList: activityStoreType {} not allowed for penetration audit, arg={}", activityStoreType, arg);
            return buildEmptyResult();
        }

        // 4. 查询活动证明ID列表
        List<String> proofIdList = queryProofIdList(downstreamTenantId, arg.getDataId());
        if (proofIdList.isEmpty()) {
            log.info("auditSummaryList: proofIdList is empty, arg={}", arg);
            return buildEmptyResult();
        }

        // 5. 判断当前用户是否有审核角色
        boolean useAuditRole = hasAuditRole(context.getTenantId(), context.getEmployeeId(), arg.getApiName(), dataId);

        // 6. 构造返回结果
        StoreAuditSummaryList.StoreAuditSummaryDatumVO datumVO = new StoreAuditSummaryList.StoreAuditSummaryDatumVO();
        datumVO.setDataIdList(proofIdList);

        return StoreAuditSummaryList.Result.builder()
                .data(Collections.singletonList(datumVO))
                .useAuditRole(useAuditRole)
                .total(1)
                .build();
    }

    /**
     * 构建空结果对象
     */
    private StoreAuditSummaryList.Result buildEmptyResult() {
        return StoreAuditSummaryList.Result.builder()
                .data(Collections.emptyList())
                .useAuditRole(false)
                .total(1)
                .build();
    }

    /**
     * 查询活动证明ID列表
     */
    private List<String> queryProofIdList(String tenantId, String dataId) {
        SearchTemplateQuery query = getSearchTemplateQuery(dataId);
        List<String> proofIdList = new ArrayList<>();
        User user = User.systemUser(tenantId);
        List<String> fieldList = Lists.newArrayList(CommonFields.ID, CommonFields.NAME);

        QueryDataUtil.findAndConsume(serviceFacade, user, ApiNames.TPM_ACTIVITY_PROOF_OBJ, query, fieldList, dataList -> {
            if (CollectionUtils.isNotEmpty(dataList)) {
                proofIdList.addAll(dataList.stream().map(DBRecord::getId).collect(Collectors.toList()));
            }
        });
        return proofIdList;
    }

    /**
     * 判断当前用户是否有审核角色
     */
    private boolean hasAuditRole(String tenantId, Integer employeeId, String apiName, String dataId) {
        List<String> roles = roleService.queryRoleByEmployeeId(Integer.parseInt(tenantId), employeeId);
        if (CollectionUtils.isEmpty(roles)) {
            return false;
        }
        List<String> useRoles = Lists.newArrayList(AREA_USER_ROLE, MARKETING_USER_ROLE);
        boolean hasRole = roles.stream().anyMatch(useRoles::contains);
        if (hasRole) {
            // 判断当前对象的 检核状态，，如果角色是 行销经理，需要检核状态是 省区经理检核通过[7]才显示 可检核通过/不通过。
            String upApiName = Strings.isNullOrEmpty(apiName) ? "participating_store__c" : apiName;
            IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, upApiName);
            String storeAuditStatus = objectData.get("review_status__c", String.class);
            if (roles.contains(MARKETING_USER_ROLE) && !storeAuditStatus.equals("7")){
                log.info("hasAuditRole is marketing and storeAuditStatus is {}", storeAuditStatus);
                hasRole = false;
            }
        }
        return hasRole;
    }

    private SearchTemplateQuery getSearchTemplateQuery(String dataId) {
        SearchTemplateQuery queryActivity = new SearchTemplateQuery();
        queryActivity.setOffset(0);
        queryActivity.setLimit(-1);

        IFilter agreementFilter = new Filter();
        agreementFilter.setFieldName("participating_store__c");
        agreementFilter.setOperator(Operator.EQ);
        agreementFilter.setFieldValues(Lists.newArrayList(dataId));

        IFilter statusFilter = new Filter();
        statusFilter.setFieldName("audit_status");
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList("pass"));

        queryActivity.setFilters(Lists.newArrayList(agreementFilter, statusFilter));
        return queryActivity;
    }

    private String getActivityStore(String downStreamTenantId, String dataId) {
        // 自定义字段  review_status__c  协议状态
        IObjectData activityStoreData = serviceFacade.findObjectData(User.systemUser(downStreamTenantId), dataId, ApiNames.TPM_ACTIVITY_STORE_OBJ);
        return activityStoreData != null ? activityStoreData.get("review_status__c", String.class) : "";

    }

    @Override
    public StoreAuditDataList.Result dataList(StoreAuditDataList.Arg arg) {

        ApiContext context = ApiContextManager.getContext();
        // 一端协议的id
        String dataId = arg.getDataId();
        String storeId = arg.getId();
        if (Strings.isNullOrEmpty(dataId) || CollectionUtils.isEmpty(arg.getDataIdList())) {
            return StoreAuditDataList.Result.builder().build();
        }
        String downStreamTenantId = getDownStreamTenantId(arg.getOutTenantId(), context.getTenantId(), arg.getDealerId());
        if (Strings.isNullOrEmpty(downStreamTenantId)) {
            return StoreAuditDataList.Result.builder().build();
        }

        String activityId = arg.getActivityId();
        if (Strings.isNullOrEmpty(activityId)) {
            activityId = findActivityIdByDataId(downStreamTenantId, arg.getDataId());
        }

        ActivityTypeExt activityType = activityTypeManager.findByActivityId(downStreamTenantId, activityId);

        Map<String, ObjectDescribeDocument> describeMap = Maps.newHashMap();
        Map<String, Map<String, LayoutDocument>> layoutMap = Maps.newHashMap();

        fillDescribeAndLayout(downStreamTenantId, -10000, activityType.auditSourceConfig(), describeMap, layoutMap);

        List<StoreAuditDataList.AuditDatumVO> data = loadAuditDatum(
                downStreamTenantId,
                arg.getDataIdList(),
                activityType,
                arg.getAuditObjectRecordType()
        );
        return StoreAuditDataList.Result.builder()
                .objectDescribeMap(describeMap)
                .layoutMap(layoutMap)
                .fieldShowNameMap(new HashMap<>())
                .mainObjectData(getCoreData(downStreamTenantId, storeId))
                .activityType(ActivityTypeVO.fromPO(activityType.get()))
                .data(data)
                .build();
    }

    private String findActivityIdByDataId(String downStreamTenantId, String dataId) {

        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(downStreamTenantId), dataId, ApiNames.TPM_ACTIVITY_STORE_OBJ);
        return objectData.get(TPMActivityStoreFields.ACTIVITY_ID, String.class);
    }

    @Nullable
    private ObjectDataDocument getCoreData(String downstreamTenantId, String storeId) {
        ObjectDataDocument coreData = null;
        if (!Strings.isNullOrEmpty(storeId)) {
            IObjectDescribe describe = serviceFacade.findObject(downstreamTenantId, ApiNames.ACCOUNT_OBJ);
            IObjectData coreObjectData = serviceFacade.findObjectData(User.systemUser(downstreamTenantId), storeId, ApiNames.ACCOUNT_OBJ);
            coreData = ObjectDataDocument.of(fillWebDetailAuditData(downstreamTenantId, describe, coreObjectData));
        }
        return coreData;
    }

    private String getDownStreamTenantId(String outTenantId, String tenantId, String dealerId) {
        String downStreamTenantId;
        if (Strings.isNullOrEmpty(outTenantId)) {
            downStreamTenantId = findDownStreamTenantIdByAccountId(tenantId, dealerId);
        } else {
            downStreamTenantId = findDownStreamTenantIdByOutId(tenantId, outTenantId);
        }
        return downStreamTenantId;
    }

    @Override
    public StoreOperateAudit.Result operateAudit(StoreOperateAudit.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String tenantId = context.getTenantId();
        String dataId = arg.getDataId();

        log.info("operateAudit arg is {}", arg);

        // 1. 参数校验
        if (Strings.isNullOrEmpty(dataId)) {
            log.warn("operateAudit: dataId is empty, arg={}", arg);
            return StoreOperateAudit.Result.builder().build();
        }

        String accountRecordType = findAccountRecordType(tenantId, arg.getDealerId());
        String downStreamTenantId = getDownStreamTenantId(arg.getOutTenantId(), tenantId, arg.getDealerId());
        if (Strings.isNullOrEmpty(downStreamTenantId)) {
            log.warn("operateAudit: downStreamTenantId is empty, arg={}", arg);
            return StoreOperateAudit.Result.builder().build();
        }

        // 2. 获取操作人角色
        List<String> roles = roleService.queryRoleByEmployeeId(Integer.parseInt(tenantId), context.getEmployeeId());
        if (CollectionUtils.isEmpty(roles)) {
            log.warn("operateAudit: roles is empty, employeeId={}", context.getEmployeeId());
            return StoreOperateAudit.Result.builder().build();
        }

        // 3. 获取审核状态
        String status = getAuditStatusOption(tenantId, arg.getAuditStatus(), roles, accountRecordType);
        if (Strings.isNullOrEmpty(status)) {
            log.warn("operateAudit: status is empty, arg={}", arg);
            return StoreOperateAudit.Result.builder().build();
        }

        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, arg.getApiName());
        BigDecimal activityAmount = objectData.get("field_34Vjr__c", BigDecimal.class);
        log.info("operateAudit: activityAmount is {}", activityAmount);
        BigDecimal modifyAmount = arg.getModifyAmount();
        if (modifyAmount != null && activityAmount != null && modifyAmount.compareTo(activityAmount) > 0) {
            throw new ValidateException("modifyAmount exceeds!");
        }

        // 4. 构建锁 key
        String lockKey = String.format("FMCG_UP_ACTIVITY_STORE_AUDIT_LOCK_%s_%s", tenantId, dataId);
        tryLock(lockKey);
        try {
            // 5. 更新一端数据
            String auditStatus = updateUpActivityStoreData(
                    tenantId, objectData, context.getEmployeeId(), status,
                    arg.getAuditOpinion(), arg.getApiName(), arg.getAuditAdvice(),
                    arg.getAmount(), arg.getModifyAmount()
            );

            // 6. 若一端更新成功，则同步更新N端
            if (!Strings.isNullOrEmpty(auditStatus)) {
                updateDownActivityStoreData(
                        downStreamTenantId, dataId, auditStatus,
                        arg.getAuditOpinion(), arg.getAuditAdvice(),
                        arg.getAmount(), arg.getModifyAmount()
                );
            }
        } catch (Exception ex) {
            // 7. 异常处理，记录上下文信息
            String upStatus = getStoreAuditStatusForTenant(tenantId, dataId, "participating_store__c");
            String downStatus = getStoreAuditStatusForTenant(downStreamTenantId, dataId, ApiNames.TPM_ACTIVITY_STORE_OBJ);
            log.error("operateAudit: 上级检核操作异常, tenantId={}, dataId={}, upStatus={}, downStatus={}", tenantId, dataId, upStatus, downStatus, ex);
            throw new ValidateException(String.format(I18N.text(I18NKeys.AGREEMENT_AUDIT_SERVICE_0), upStatus, downStatus));
        } finally {
            unlock(lockKey);
        }

        // 8. 返回标准结果对象
        return StoreOperateAudit.Result.builder().build();
    }

    private String getAuditStatusOption(String tenantId, String auditStatus, List<String> roles, String accountRecordType) {
        //省区经理：624d2f9ebe74513ca1def918  行销经理： 663b5afb88baac6e0f985a27
        if (roles.contains(AREA_USER_ROLE)) {
            return "reject".equals(auditStatus) ? "8" : "7";
        }

        if (roles.contains(MARKETING_USER_ROLE)) {
            return "reject".equals(auditStatus) ? "10" : "9";
        }

        return "";
    }

    private String getStoreAuditStatusForTenant(String tenantId, String id, String apiName) {
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), id, apiName);
        return objectData.get("review_status__c", String.class);
    }

    private String updateUpActivityStoreData(String tenantId,
                                             IObjectData objectData,
                                             Integer employeeId,
                                             String status,
                                             String auditOpinion,
                                             String upApiName,
                                             String auditAdvice, BigDecimal amount, BigDecimal modifyAmount) {


        String storeAuditStatus = objectData.get("review_status__c", String.class);
        log.info("storeAuditStatus review status is {}" , storeAuditStatus);
        // 如果参与门店上的状态已经是 9 行销经理通过，10 行销经理驳回，8 省区经理驳回 则不再检核，
        if (Lists.newArrayList("8","9","10").contains(storeAuditStatus)) {
            return "";
        }

        Map<String, Object> updateFieldMap = new HashMap<>(8);
        updateFieldMap.put("review_status__c", status);

        // 核销金额，改动更新
        if (validateAmount(modifyAmount, amount)) {
            updateFieldMap.put("field_8x502__c", modifyAmount);
        }

        // 检核人员。
        updateFieldMap.put("final_inspector__c", Lists.newArrayList(String.valueOf(employeeId)));
        if (!Strings.isNullOrEmpty(auditOpinion)) {
            updateFieldMap.put("inspection_opinion__c", auditOpinion);
        }

        // 扣减原因
        if (!Strings.isNullOrEmpty(auditAdvice)) {
            updateFieldMap.put("field_5flnX__c", auditAdvice);
        }

        User user = User.builder().tenantId(tenantId).userId(String.valueOf(employeeId)).build();
        IObjectData iObjectData = serviceFacade.updateWithMap(user, objectData, updateFieldMap);
        IObjectDescribe describe = serviceFacade.findObject(tenantId, upApiName);
        serviceFacade.logWithCustomMessage(
                user,
                EventType.MODIFY,
                ActionType.MODIFY,
                describe,
                objectData,
                getMessage(status, auditOpinion)
        );
        return iObjectData.get("review_status__c", String.class);
    }

    private void updateDownActivityStoreData(String downstreamTenantId,
                                             String id,
                                             String status,
                                             String auditOpinion,
                                             String auditAdvice, BigDecimal amount, BigDecimal modifyAmount) {

        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(downstreamTenantId), id, ApiNames.TPM_ACTIVITY_STORE_OBJ);
        Map<String, Object> updateFieldMap = new HashMap<>(4);
        updateFieldMap.put("review_status__c", status);
        // 核销金额，改动更新
        if (validateAmount(modifyAmount, amount)) {
            updateFieldMap.put("field_8x502__c", modifyAmount);
        }

        if (!Strings.isNullOrEmpty(auditOpinion)) {
            updateFieldMap.put("inspection_opinion__c", auditOpinion);
        }
        // 扣减原因
        if (!Strings.isNullOrEmpty(auditAdvice)) {
            updateFieldMap.put("field_5flnX__c", auditAdvice);
        }
        serviceFacade.updateWithMap(User.systemUser(String.valueOf(downstreamTenantId)), objectData, updateFieldMap);

        IObjectDescribe describe = serviceFacade.findObject(downstreamTenantId, ApiNames.TPM_ACTIVITY_STORE_OBJ);
        serviceFacade.logWithCustomMessage(
                User.systemUser(String.valueOf(downstreamTenantId)),
                EventType.MODIFY,
                ActionType.MODIFY,
                describe,
                objectData,
                getMessage(status, auditOpinion));

    }


    private String getMessage(String status, String auditOpinion) {
        switch (status) {
            case "7":
                return "省区经理检核通过";
            case "8":
                return "省区经理检核驳回,驳回意见：" + auditOpinion;
            case "9":
                return "行销经理检核通过";
            case "10":
                return "行销经理检核驳回,驳回意见：" + auditOpinion;
            default:
                return "检核完成";
        }
    }

    private boolean validateAmount(BigDecimal modifyAmount, BigDecimal amount) {
        if (modifyAmount == null) {
            return false;
        }
        return amount == null || modifyAmount.compareTo(amount) != 0;
    }

    private String findAccountRecordType(String tenantId, String dealerId) {
        if (Strings.isNullOrEmpty(dealerId)) {
            return null;
        }
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), dealerId, ApiNames.ACCOUNT_OBJ);
        return objectData != null ? objectData.getRecordType() : null;
    }


    private String findDownStreamTenantIdByAccountId(String tenantId, String accountId) {
        if (Strings.isNullOrEmpty(accountId)) {
            log.info("finddownStreamTenantIdByAccountId accountId is empty. tenantId : {}", tenantId);
            return null;
        }

        IFilter mapperAccountIdFilter = new Filter();
        mapperAccountIdFilter.setFieldName(EnterpriseRelationFields.MAPPER_ACCOUNT_ID);
        mapperAccountIdFilter.setOperator(Operator.EQ);
        mapperAccountIdFilter.setFieldValues(Lists.newArrayList(accountId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(mapperAccountIdFilter);
        stq.setLimit(1);
        stq.setOffset(0);

        List<IObjectData> relation = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.ENTERPRISE_RELATION_OBJ, stq, Lists.newArrayList(
                CommonFields.ID, EnterpriseRelationFields.ENTERPRISE_ACCOUNT
        ));

        if (CollectionUtils.isEmpty(relation)) {
            throw new ValidateException("downstream enterprise not found.");
        }

        String tenantAccount = relation.get(0).get(EnterpriseRelationFields.ENTERPRISE_ACCOUNT, String.class);
        if (Strings.isNullOrEmpty(tenantAccount)) {
            throw new ValidateException("downstream enterprise not found.");
        }

        return String.valueOf(enterpriseEditionService.getEnterpriseId(tenantAccount));
    }

    private String findDownStreamTenantIdByOutId(String tenantId, String outTenantId) {
        if (Strings.isNullOrEmpty(outTenantId)) {
            log.info("finddownStreamTenantIdByAccountId outTenantId is empty. tenantId : {}", tenantId);
            return null;
        }
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), outTenantId, ApiNames.ENTERPRISE_RELATION_OBJ);
        if (Objects.isNull(objectData)) {
            return null;
        }
        String tenantAccount = objectData.get(EnterpriseRelationFields.ENTERPRISE_ACCOUNT, String.class);
        return String.valueOf(enterpriseEditionService.getEnterpriseId(tenantAccount));
    }

    private void fillDescribeAndLayout(String tenantId,
                                       Integer userId,
                                       ActivityProofAuditSourceConfigEntity auditSourceConfig,
                                       Map<String, ObjectDescribeDocument> describeMap,
                                       Map<String, Map<String, LayoutDocument>> layoutMap) {
        List<String> relatedObjectApiNames = Lists.newArrayList(auditSourceConfig.getMasterApiName(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
        if (!Strings.isNullOrEmpty(auditSourceConfig.getDetailApiName())) {
            relatedObjectApiNames.add(auditSourceConfig.getDetailApiName());
            relatedObjectApiNames.add(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ);
        }

        for (String relatedObjectApiName : relatedObjectApiNames) {
            fillDescribe(tenantId, relatedObjectApiName, describeMap);
            fillLayout(tenantId, userId, relatedObjectApiName, describeMap, layoutMap);
        }
    }

    private void fillLayout(String tenantId, Integer userId, String apiName, Map<String, ObjectDescribeDocument> describeMap, Map<String, Map<String, LayoutDocument>> layoutMap) {
        layoutMap.put(apiName, loadLayoutEntry(tenantId, userId, describeMap.get(apiName)));
    }

    private void fillDescribe(String tenantId, String apiName, Map<String, ObjectDescribeDocument> describeMap) {
        describeMap.put(apiName, ObjectDescribeDocument.of(serviceFacade.findObject(tenantId, apiName)));
    }

    private Map<String, LayoutDocument> loadLayoutEntry(String tenantId, Integer userId, ObjectDescribeDocument describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        RecordTypeFieldDescribe recordTypeField = describeExt.getRecordTypeField().orElse(null);
        User user = userId == -10000 ? User.systemUser(tenantId) : User.builder().userId(userId.toString()).tenantId(tenantId).build();
        Map<String, LayoutDocument> data = Maps.newHashMap();
        if (recordTypeField != null) {
            for (IRecordTypeOption recordTypeOption : recordTypeField.getRecordTypeOptions()) {
                LayoutDocument detailLayout = LayoutDocument.of(serviceFacade.findObjectLayoutByType(user, recordTypeOption.getApiName(), describeExt.getApiName(), "edit"));
                data.put(recordTypeOption.getApiName(), detailLayout);
            }
        }
        return data;
    }

    private List<StoreAuditDataList.AuditDatumVO> loadAuditDatum(String tenantId,
                                                                 List<String> dataIds,
                                                                 ActivityTypeExt activityType,
                                                                 String auditObjectRecordType) {

        IObjectDescribe describe = serviceFacade.findObject(tenantId, activityType.auditSourceConfig().getMasterApiName());
        IObjectDescribe auditDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);

        IObjectDescribe detailDescribe = null;
        IObjectDescribe auditDetailDescribe = null;
        if (!Strings.isNullOrEmpty(activityType.auditSourceConfig().getDetailApiName())) {
            detailDescribe = serviceFacade.findObject(tenantId, activityType.auditSourceConfig().getDetailApiName());
            auditDetailDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ);
        }

        Map<String, Map<String, IObjectData>> auditDataMap = queryAuditDataMap(tenantId, auditDescribe, dataIds, activityType.auditSourceConfig().getReferenceAuditSourceFieldApiName());
        List<IObjectData> data = findObjectDataByIds(tenantId, describe, dataIds, true);

        List<StoreAuditDataList.AuditDatumVO> result = Lists.newArrayList();
        for (IObjectData datum : data) {
            Map<String, List<ObjectDataDocument>> details = new HashMap<>();
            if (!Strings.isNullOrEmpty(activityType.auditSourceConfig().getDetailApiName())) {
                details.put(activityType.auditSourceConfig().getDetailApiName(), findDetailsByMasterObject(tenantId, detailDescribe, datum).stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
            }

            StoreAuditDataList.AuditDatumVO auditDatum = new StoreAuditDataList.AuditDatumVO();
            auditDatum.setApplicationData(StoreAuditDataList.ObjectDataVO.builder()
                    .data(ObjectDataDocument.of(datum))
                    .details(details)
                    .auditStatus("schedule")
                    .build()
            );

            auditDatum.setAllImages(Maps.newHashMap());
            auditDatum.getAllImages().put(VISIT_DATA_KEY, Lists.newArrayList());
            auditDatum.getAllImages().put(APPLICATION_DATA_KEY, loadImages(describe, datum, activityType.auditSourceConfig().getDisplayFieldApiNamesOfMaster()));

            Map<String, IObjectData> innerAuditDataMap = auditDataMap.get(datum.getId());

            if (!Objects.isNull(innerAuditDataMap)) {
                auditDatum.setMultipleLevelAuditData(Maps.newHashMap());

                for (Map.Entry<String, IObjectData> entry : innerAuditDataMap.entrySet()) {
                    String auditStatus = entry.getValue().get(TPMActivityProofAuditFields.AUDIT_STATUS, String.class);
                    auditDatum.getApplicationData().setAuditStatus(auditStatus);

                    auditDatum.getAllImages().put(AUDIT_DATA_KEY, loadImages(auditDescribe, entry.getValue(), activityType.auditSourceConfig().getDisplayFieldApiNamesOfAuditMaster()));
                    Map<String, List<ObjectDataDocument>> auditDetails = new HashMap<>();
                    if (!Strings.isNullOrEmpty(activityType.auditSourceConfig().getDetailApiName())) {
                        auditDetails.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, findDetailsByMasterObject(tenantId, auditDetailDescribe, entry.getValue()).stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
                    }

                    auditDatum.getMultipleLevelAuditData().put(
                            entry.getKey(),
                            StoreAuditDataList.ObjectDataVO.builder()
                                    .isRandomAudit(true)
                                    .isEnableEdit(true)
                                    .data(ObjectDataDocument.of(entry.getValue()))
                                    .details(auditDetails)
                                    .build()
                    );
                }

                if (Strings.isNullOrEmpty(auditObjectRecordType)) {
                    auditObjectRecordType = activityType.auditNode().getObjectRecordType();
                }
                auditDatum.setAuditData(auditDatum.getMultipleLevelAuditData().get(auditObjectRecordType));
            }
            result.add(auditDatum);
        }
        return result;
    }

    private List<StoreAuditDataList.AuditImageVO> loadImages(IObjectDescribe describe, IObjectData data, List<String> displayFields) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<StoreAuditDataList.AuditImageVO> images = Lists.newArrayList();

        List<Image> imageFieldList = describeExt.getImageFieldList();
        if (CollectionUtils.isNotEmpty(imageFieldList) && CollectionUtils.isNotEmpty(displayFields)) {
            imageFieldList = imageFieldList.stream().filter(image -> displayFields.contains(image.getApiName())).collect(Collectors.toList());
            for (Image field : imageFieldList) {
                JSONArray imageValue = JSON.parseArray(JSON.toJSONString(data.get(field.getApiName())));
                if (!Objects.isNull(imageValue)) {
                    for (int i = 0; i < imageValue.size(); i++) {
                        images.add(StoreAuditDataList.AuditImageVO.fromJSON(field.getLabel(), imageValue.getJSONObject(i)));
                    }
                }
            }
        }
        return images;
    }

    private Map<String, Map<String, IObjectData>> queryAuditDataMap(String tenantId, IObjectDescribe describe, List<String> dataIds, String referenceProofFieldApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(true);

        query.setLimit(200);
        query.setOffset(0);

        IFilter applicationDataIdFilter = new Filter();
        applicationDataIdFilter.setFieldName(referenceProofFieldApiName);
        applicationDataIdFilter.setOperator(Operator.IN);
        applicationDataIdFilter.setFieldValues(dataIds);
        query.setFilters(Lists.newArrayList(applicationDataIdFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query).getData();

        fillWebDetailAuditData(tenantId, describe, data);

        Map<String, Map<String, IObjectData>> auditData = Maps.newHashMap();

        for (IObjectData datum : data) {
            String auditSourceDataId = datum.get(referenceProofFieldApiName, String.class);
            if (!auditData.containsKey(auditSourceDataId)) {
                auditData.put(auditSourceDataId, Maps.newHashMap());
            }
            auditData.get(auditSourceDataId).put(datum.getRecordType(), datum);
        }
        return auditData;
    }


    private void tryLock(String key) {
        RLock lock = redissonCmd.getLock(key);
        try {
            if (!lock.tryLock(5, 10, TimeUnit.SECONDS)) {
                throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_AUDIT_SERVICE_1));
            }
        } catch (InterruptedException e) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_AUDIT_SERVICE_2));
        }
    }

    private void unlock(String key) {
        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    protected IObjectData fillWebDetailAuditData(String tenantId, IObjectDescribe describe, IObjectData data) {
        return fillWebDetailAuditData(tenantId, describe, Lists.newArrayList(data)).get(0);
    }

    protected List<IObjectData> fillWebDetailAuditData(String tenantId, IObjectDescribe describe, List<IObjectData> data) {
        infraServiceFacade.fillQuoteFieldValue(User.systemUser(tenantId), data, describe, null, false);
        serviceFacade.fillObjectDataWithRefObject(describe, data, User.systemUser(tenantId), null, false);
        serviceFacade.fillUserInfo(describe, data, User.systemUser(tenantId));
        serviceFacade.fillDepartmentInfo(describe, data, User.systemUser(tenantId));

        handleDataImageShare(tenantId, describe, data);
        return data;
    }

    private void handleDataImageShare(String tenantId, IObjectDescribe describe, List<IObjectData> data) {
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<Image> imageFieldList = describeExt.getImageFieldList();
        if (CollectionUtils.isNotEmpty(imageFieldList)) {
            for (IObjectData objectData : data) {
                for (Image field : imageFieldList) {
                    List<JSONObject> imageValue = JSON.parseArray(JSON.toJSONString(objectData.get(field.getApiName())), JSONObject.class);
                    if (Objects.nonNull(imageValue)) {
                        for (JSONObject image : imageValue) {
                            if (extendImage(tenantId, image)) {
                                continue;
                            }
                            Pair<String, String> shareFile = fileCenterAdapter.createShareFile(ea, -10000, image.getString("path"));
                            image.put("path", shareFile.getValue());
                        }
                        objectData.set(field.getApiName(), imageValue);
                    }
                }
            }
        }
    }

    private boolean extendImage(String tenantId, JSONObject image) {
        try {
            String signature = image.getString("signature");
            if (!Strings.isNullOrEmpty(signature)) {
                String[] split = signature.split("\\$");
                if (split.length > 0) {
                    String tenantEi = split[0];
                    if (tenantEi != null && !tenantEi.equals(tenantId)) {
                        String tenantEa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantEi));
                        Pair<String, String> shareFile = fileCenterAdapter.createShareFile(tenantEa, -10000, image.getString("path"));
                        image.put("path", shareFile.getValue());
                        return true;
                    }
                }
            }
        } catch (Exception ex) {
            log.error("extendImage fail ex : ", ex);
        }
        return false;
    }

    protected List<IObjectData> findObjectDataByIds(String tenantId, IObjectDescribe describe, List<String> ids, boolean needFillWebDetailData) {
        if (needFillWebDetailData) {
            return fillWebDetailAuditData(tenantId, describe, serviceFacade.findObjectDataByIds(tenantId, ids, describe.getApiName()));
        } else {
            return serviceFacade.findObjectDataByIds(tenantId, ids, describe.getApiName());
        }
    }

    protected List<IObjectData> findDetailsByMasterObject(String tenantId, IObjectDescribe describe, IObjectData masterData) {
        List<IObjectData> applicationDataDetailList = serviceFacade.findDetailObjectDataList(describe, masterData, User.systemUser(tenantId));
        return fillWebDetailAuditData(tenantId, describe, applicationDataDetailList);
    }
}
