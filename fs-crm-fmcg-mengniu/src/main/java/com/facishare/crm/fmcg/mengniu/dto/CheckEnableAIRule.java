package com.facishare.crm.fmcg.mengniu.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/3/5 19:47
 */
public interface CheckEnableAIRule {


    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "api_name")
        @JsonProperty(value = "api_name")
        @SerializedName("api_name")
        private String apiName;
    }


    @Data
    @ToString
    class Result implements Serializable {

        @JSONField(name = "is_show_rule_page")
        @JsonProperty(value = "is_show_rule_page")
        @SerializedName("is_show_rule_page")
        private boolean isShowRulePage;

        @JSONField(name = "product_range_api_name")
        @JsonProperty(value = "product_range_api_name")
        @SerializedName("product_range_api_name")
        private String productRangeApiName;

        @JSONField(name = "product_range_label")
        @JsonProperty(value = "product_range_label")
        @SerializedName("product_range_label")
        private String productRangeLabel;

        @JSONField(name = "rule_count_limit")
        @JsonProperty(value = "rule_count_limit")
        @SerializedName("rule_count_limit")
        private Integer ruleCountLimit;

        @JSONField(name = "order_product_rule")
        @JsonProperty(value = "order_product_rule")
        @SerializedName("order_product_rule")
        private OrderProductRule orderProductRule;
    }

    @Data
    @Builder
    @ToString
    class OrderProductRule {
        @JSONField(name = "is_show_rule_page")
        @JsonProperty(value = "is_show_rule_page")
        @SerializedName("is_show_rule_page")
        private boolean isShowRulePage;

        @JSONField(name = "product_range_api_name")
        @JsonProperty(value = "product_range_api_name")
        @SerializedName("product_range_api_name")
        private String productRangeApiName;

        @JSONField(name = "product_range_label")
        @JsonProperty(value = "product_range_label")
        @SerializedName("product_range_label")
        private String productRangeLabel;

        @JSONField(name = "rule_count_limit")
        @JsonProperty(value = "rule_count_limit")
        @SerializedName("rule_count_limit")
        private Integer ruleAndCountLimit;

        @JSONField(name = "rule_or_count_limit")
        @JsonProperty(value = "rule_or_count_limit")
        @SerializedName("rule_or_count_limit")
        private Integer ruleOrCountLimit;

    }
}
