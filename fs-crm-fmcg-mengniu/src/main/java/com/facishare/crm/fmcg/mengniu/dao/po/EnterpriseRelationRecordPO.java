package com.facishare.crm.fmcg.mengniu.dao.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/18 15:22
 */
@Data
@ToString
@Entity(value = "fmcg_mn_enterprise_relation_record", noClassnameStored = true)
@EqualsAndHashCode(callSuper = false)
public class EnterpriseRelationRecordPO extends BasePO {

    public static final String F_UP_TENANT_ID = "up_tenant_id";
    public static final String F_DOWN_TENANT_ID = "down_tenant_id";
    public static final String F_ACCOUNT_ID = "account_id";
    public static final String F_CONTACT_IDS = "contact_ids";
    public static final String F_STATUS = "status";
    public static final String F_MESSAGE = "message";

    /**
     * 上游
     */
    @Property(F_UP_TENANT_ID)
    private String upTenantId;

    /**
     * 下游
     */
    @Property(F_DOWN_TENANT_ID)
    private String downTenantId;

    /**
     * 客户id
     */
    @Property(F_ACCOUNT_ID)
    private String accountId;

    /**
     * 联系人id  contactIdList
     */
    @Embedded(F_CONTACT_IDS)
    private List<String> contactIds;

    /**
     * 执行状态 0 成功、 1 失败
     */
    @Property(F_STATUS)
    private Integer status;

    @Property(F_MESSAGE)
    private String message;

}
