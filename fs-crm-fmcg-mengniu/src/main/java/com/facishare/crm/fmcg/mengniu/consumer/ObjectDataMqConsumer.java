package com.facishare.crm.fmcg.mengniu.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventAbandonException;
import com.facishare.crm.fmcg.common.adapter.dto.exception.ObjectActionRetryException;
import com.facishare.crm.fmcg.mengniu.consumer.model.TriggerMessageObj;
import com.facishare.crm.fmcg.mengniu.dto.ObjectActionEventData;
import com.facishare.crm.fmcg.mengniu.dto.SalesEvent;
import com.facishare.crm.fmcg.mengniu.handler.ObjectActionEventHandler;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

//IgnoreI18nFile
@Slf4j
@Component("rewardObjectMqConsumer")
@SuppressWarnings("Duplicates")
public class ObjectDataMqConsumer implements ApplicationListener<ContextRefreshedEvent> {

    private static final String CONFIG_NAME = "fs-fmcg-object-mq";
    private static final String NAME_SERVER_KEY = "OBJECT_V2_NAMESERVER";
    private static final String TOPIC_KEY = "OBJECT_V2_TOPICS";
    private static final String GROUP_KEY = "OBJECT_V2_GROUP_CONSUMER_REWARD";

    public static final List<String> SUPPORT_OBJECT_API_NAMES = Lists.newArrayList();
    public static final List<String> SUPPORT_OP = Lists.newArrayList();

    private AutoConfMQPushConsumer consumer;


    static {
        ConfigFactory.getConfig("gray-rel-fmcg", conf -> {

            String json = conf.get("fmcg_tpm_object_action_rewards_support_object_api_names");
            if (!Strings.isNullOrEmpty(json)) {
                try {
                    SUPPORT_OBJECT_API_NAMES.addAll(JSON.parseArray(json, String.class));
                } catch (Exception ex) {
                    log.error("mengniu fmcg_tpm_object_action_rewards_support_object_api_names parse error : ", ex);
                }
            }

            String actionJson = conf.get("fmcg_tpm_object_action_rewards_support_actions");
            if (!Strings.isNullOrEmpty(actionJson)) {
                try {
                    SUPPORT_OP.addAll(JSON.parseArray(actionJson, String.class));
                } catch (Exception ex) {
                    log.error("mengniu fmcg_tpm_object_action_rewards_support_action parse error : ", ex);
                }
            }
        });

    }

    @Resource
    private ObjectActionEventHandler objectActionEventHandler;
    @Resource
    private ServiceFacade serviceFacade;

    @PostConstruct
    public void init() {
        if (!Objects.equals("1", System.getProperty("mn.flag")) ||
                !Objects.equals("1", System.getProperty("mn.object.action.rewards.flag"))) {
            return;
        }

        log.info("object-data reward consumer start init.");

        MessageListenerConcurrently listener = (messages, context) -> {
            boolean reconsume = false;
            for (MessageExt message : messages) {
                try {
                    this.process(message);
                } catch (EventAbandonException ex) {
                    log.warn("object-data reward process EventAbandonException : ", ex);
                } catch (ObjectActionRetryException ex) {
                    reconsume = true;
                    log.warn("object-data reward process ObjectActionRetryException : ", ex);
                } catch (Exception ex) {
                    reconsume = true;
                    log.error("object-data reward process error : ", ex);
                } finally {
                    TraceContext.remove();
                }
            }
            if (reconsume) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            this.consumer = new AutoConfMQPushConsumer(CONFIG_NAME, listener);
            this.consumer.setGroupNameKey(GROUP_KEY);
            this.consumer.setConsumeTopicKey(TOPIC_KEY);
            this.consumer.setNameServerKey(NAME_SERVER_KEY);

        } catch (Exception ex) {
            log.error("init object-data reward consumer error : ", ex);
        }
    }

    public void process(MessageExt message) {
        TraceContext trace = TraceContext.get();
        if (StringUtils.isBlank(trace.getTraceId())) {
            trace.setTraceId("OBJECT_ACTION_EVENT." + message.getMsgId());
        }

        TriggerMessageObj data = JSON.parseObject(new String(message.getBody()), TriggerMessageObj.class);

        process(message, data);
    }


    public void process(MessageExt message, TriggerMessageObj data) {
        for (JSONObject objectBody : data.getBody()) {
            String apiName = objectBody.getString("entityId");
            if (!SUPPORT_OBJECT_API_NAMES.contains(apiName)) {
                continue;
            }

            String objectId = objectBody.getString("objectId");
            IObjectData objectData;
            try {
                objectData = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(data.getTenantId()), objectId, apiName);
            } catch (ObjectDataNotFoundException ex) {
                log.info("data not found ", ex);
                continue;
            } catch (MetaDataBusinessException ex) {
                if ("数据已作废或已删除".equals(ex.getMessage()) && 320002500 == ex.getErrorCode()) {
                    log.info("data not found ", ex);
                    continue;
                }
                log.error("find object error ", ex);
                throw ex;
            }


            //如果是更新，这里可能查询出来的是再次更改之后的，所以这里把改的字段重新赋值
            if ("u".equals(data.getOp()) && objectBody.containsKey("afterTriggerData")) {
                JSONObject afterTriggerData = objectBody.getJSONObject("afterTriggerData");
                afterTriggerData.forEach((k, v) -> objectData.set(k, afterTriggerData.get(k)));
            }

            TraceContext context = TraceContext.get();
            if (!Strings.isNullOrEmpty(context.getTraceId())) {
                context.setTraceId(String.format("%s.%s.%s.%s.%s.%s", context.getTraceId(), data.getTenantId(), apiName, data.getOp(), objectId, message.getReconsumeTimes()));
            }
            objectActionEventHandler.invoke(buildObjectActionEvent(data.getOp(), objectBody, objectData));

        }
    }

    private SalesEvent<ObjectActionEventData> buildObjectActionEvent(String op, JSONObject objectBody, IObjectData objectData) {
        ObjectActionEventData objectActionEventData = new ObjectActionEventData();
        String convertOp = convertAction(op);
        if (!SUPPORT_OP.contains(convertOp)) {
            throw new EventAbandonException("not support op:[" + op + "]");
        }
        objectActionEventData.setObjectAction(convertOp);
        objectActionEventData.setObjectData(objectData);

        SalesEvent<ObjectActionEventData> salesEvent = new SalesEvent<>();
        salesEvent.setData(objectActionEventData);
        salesEvent.setTenantId(objectData.getTenantId());
        salesEvent.setUserId(objectBody.getString("userId"));
        salesEvent.setEventType("USER_OBJECT_ACTION_REWARDS");
        //eventId
        salesEvent.setEventId(String.format("%s.%s.%s.%s.%s", salesEvent.getTenantId(), objectData.getDescribeApiName(), objectActionEventData.getObjectAction(), salesEvent.getEventType(), objectActionEventData.getObjectData().getId().toUpperCase()));
        salesEvent.setEventTime(System.currentTimeMillis());
        return salesEvent;
    }

    private String convertAction(String op) {
        switch (op) {
            case "i":
                return "add";
            case "u":
                return "edit";
            default:
                return "not support";
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (Objects.nonNull(this.consumer) && event.getApplicationContext().getParent() == null) {
            this.consumer.start();
            log.info("object-data reward consumer started.");
        }
    }

    @PreDestroy
    public void shutDown() {
        if (Objects.nonNull(consumer)) {
            this.consumer.close();
            log.info("object-data reward consumer closed.");
        }
    }
}