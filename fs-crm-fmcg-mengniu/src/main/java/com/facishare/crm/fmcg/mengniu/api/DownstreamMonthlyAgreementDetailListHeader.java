package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2023/11/6 15:56
 */

public interface DownstreamMonthlyAgreementDetailListHeader {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "_id")
        @JsonProperty(value = "_id")
        @SerializedName("_id")
        private String id;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "header_list")
        @JsonProperty(value = "header_list")
        @SerializedName("header_list")
        private List<MengNiuHeaderVO> headerList;
    }
}