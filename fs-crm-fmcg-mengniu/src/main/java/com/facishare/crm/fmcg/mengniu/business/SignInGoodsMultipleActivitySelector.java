package com.facishare.crm.fmcg.mengniu.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.business.abstraction.ActivitySelector;
import com.facishare.crm.fmcg.mengniu.business.abstraction.ILocateActivityArg;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@SuppressWarnings("Duplicates")
public class SignInGoodsMultipleActivitySelector extends ActivitySelector<SignInGoodsMultipleActivitySelector.Arg> {

    @Override
    protected List<IObjectData> queryActivityByAccount(Arg arg, String accountId) {
        if (Strings.isNullOrEmpty(accountId)) {
            return Lists.newArrayList();
        }

        IFilter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName("reward_activity_tenant__c");
        dealerIdFilter.setOperator(Operator.EQ);
        dealerIdFilter.setFieldValues(Lists.newArrayList(accountId));

        IFilter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        IFilter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
        activityTypeFilter.setOperator(Operator.EQ);
        activityTypeFilter.setFieldValues(Lists.newArrayList("sign_in_goods_rewards__c"));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(dealerIdFilter, activityStatusFilter, activityTypeFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                arg.getTenant().getManufacturer().getTenantId(),
                ApiNames.TPM_ACTIVITY_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, TPMActivityFields.STORE_RANGE, TPMActivityFields.PRODUCT_RANGE)
        );
    }

    @Override
    protected List<IObjectData> queryActivityByDepartment(Arg arg, String departmentId) {
        if (Strings.isNullOrEmpty(departmentId)) {
            return Lists.newArrayList();
        }

        List<String> upperDepartmentIds = organizationService.queryUpperDepartmentIds(
                        Integer.parseInt(arg.getTenant().getManufacturer().getTenantId()),
                        Integer.parseInt(departmentId))
                .stream()
                .map(Object::toString)
                .collect(Collectors.toList());

        IFilter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName("reward_activity_tenant__c");
        dealerIdFilter.setOperator(Operator.IS);
        dealerIdFilter.setFieldValues(Lists.newArrayList());

        IFilter departmentFilter = new Filter();
        departmentFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
        departmentFilter.setOperator(Operator.HASANYOF);
        departmentFilter.setFieldValues(upperDepartmentIds);

        IFilter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        IFilter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
        activityTypeFilter.setOperator(Operator.EQ);
        activityTypeFilter.setFieldValues(Lists.newArrayList("sign_in_goods_rewards__c"));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(dealerIdFilter, departmentFilter, activityStatusFilter, activityTypeFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                arg.getTenant().getManufacturer().getTenantId(),
                ApiNames.TPM_ACTIVITY_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, TPMActivityFields.STORE_RANGE, TPMActivityFields.PRODUCT_RANGE)
        );
    }

    @Override
    protected List<ActivityExt> fillExtInformation(String tenantId, List<IObjectData> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }

        List<ActivityExt> activityExtList = Lists.newArrayList();

        List<String> storeFixedIds = Lists.newArrayList();
        List<String> productFixedIds = Lists.newArrayList();

        for (IObjectData datum : data) {
            ActivityExt ext = new ActivityExt();
            ext.setId(datum.getId());
            ext.setActivity(datum);

            String storeRangeJson = datum.get(TPMActivityFields.STORE_RANGE, String.class);
            if (!Strings.isNullOrEmpty(storeRangeJson)) {
                JSONObject storeJson = JSON.parseObject(storeRangeJson);
                String storeRangeType = storeJson.getString("type");

                ext.setStoreRangeType(storeRangeType);
                if (ext.getStoreRangeType().equals(CONDITION)) {
                    List<Wheres> wheres = JSON.parseArray(storeJson.getString("value"), Wheres.class);
                    ext.setStoreWheres(wheres);
                }
                if (ext.getStoreRangeType().equals(FIXED)) {
                    storeFixedIds.add(datum.getId());
                }
            }

            String productRangeJson = datum.get(TPMActivityFields.PRODUCT_RANGE, String.class);
            if (!Strings.isNullOrEmpty(productRangeJson)) {
                JSONObject productJson = JSON.parseObject(productRangeJson);
                String productRangeType = productJson.getString("type");

                ext.setProductRangeType(productRangeType);
                if (ext.getProductRangeType().equals(CONDITION)) {
                    List<Wheres> wheres = JSON.parseArray(productJson.getString("value"), Wheres.class);
                    ext.setProductWheres(wheres);
                }
                if (ext.getProductRangeType().equals(FIXED)) {
                    productFixedIds.add(datum.getId());
                }
            }

            activityExtList.add(ext);
        }

        Map<String, List<IObjectData>> storeRangeMap;
        if (!CollectionUtils.isEmpty(storeFixedIds)) {
            IFilter storeActivityIdFilter = new Filter();
            storeActivityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
            storeActivityIdFilter.setOperator(Operator.IN);
            storeActivityIdFilter.setFieldValues(Lists.newArrayList(storeFixedIds));

            SearchTemplateQuery storeRangeQuery = QueryDataUtil.minimumQuery(storeActivityIdFilter);

            List<IObjectData> storeRange = QueryDataUtil.find(
                    serviceFacade,
                    tenantId,
                    ApiNames.TPM_ACTIVITY_STORE_OBJ,
                    storeRangeQuery,
                    Lists.newArrayList("_id", TPMActivityStoreFields.ACTIVITY_ID, TPMActivityStoreFields.STORE_ID)
            );

            storeRangeMap = storeRange.stream().collect(Collectors.groupingBy(g -> g.get(TPMActivityStoreFields.ACTIVITY_ID, String.class)));
        } else {
            storeRangeMap = Maps.newHashMap();
        }

        Map<String, List<IObjectData>> productRangeMap;
        if (!CollectionUtils.isEmpty(productFixedIds)) {
            IFilter productActivityIdFilter = new Filter();
            productActivityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
            productActivityIdFilter.setOperator(Operator.IN);
            productActivityIdFilter.setFieldValues(Lists.newArrayList(productFixedIds));

            SearchTemplateQuery productRangeQuery = QueryDataUtil.minimumQuery(productActivityIdFilter);

            List<IObjectData> productRange = QueryDataUtil.find(
                    serviceFacade,
                    tenantId,
                    ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ,
                    productRangeQuery,
                    Lists.newArrayList("_id", TPMActivityProductRangeFields.ACTIVITY_ID, TPMActivityProductRangeFields.PRODUCT_ID)
            );

            productRangeMap = productRange.stream().collect(Collectors.groupingBy(g -> g.get(TPMActivityProductRangeFields.ACTIVITY_ID, String.class)));
        } else {
            productRangeMap = Maps.newHashMap();
        }

        for (ActivityExt activityExt : activityExtList) {
            if (storeRangeMap.containsKey(activityExt.getId())) {
                List<String> storeIds = storeRangeMap.get(activityExt.getId()).stream().map(m -> m.get(TPMActivityStoreFields.STORE_ID, String.class)).collect(Collectors.toList());
                activityExt.setStoreIds(storeIds);
            }

            if (productRangeMap.containsKey(activityExt.getId())) {
                List<String> productIds = productRangeMap.get(activityExt.getId()).stream().map(m -> m.get(TPMActivityProductRangeFields.PRODUCT_ID, String.class)).collect(Collectors.toList());
                activityExt.setProductIds(productIds);
            }
        }

        return activityExtList;
    }

    @Override
    protected IObjectData filterActivity(Arg arg, List<IObjectData> activities) {
        String tenantId = arg.getTenant().getManufacturer().getTenantId();

        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }

        List<ActivityExt> activityExtList = fillExtInformation(tenantId, activities);

        Map<String, List<ActivityExt>> grouped = activityExtList.stream().collect(Collectors.groupingBy(ActivityExt::getStoreRangeType));

        List<ActivityExt> fixedData = grouped.get(FIXED);
        if (!CollectionUtils.isEmpty(fixedData)) {
            List<ActivityExt> matchedFixedData = fixedData.stream().filter(f -> f.getStoreIds().contains(arg.getStoreId())).collect(Collectors.toList());
            ActivityExt activity = filterActivityByProduct(tenantId, matchedFixedData, arg.getProductId());
            if (!Objects.isNull(activity)) {
                return activity.getActivity();
            }
        }

        List<ActivityExt> conditionData = grouped.get(CONDITION);
        if (!CollectionUtils.isEmpty(conditionData)) {
            List<ActivityExt> matchedConditionData = Lists.newArrayList();
            for (ActivityExt conditionDatum : conditionData) {
                if (storeInConditionRange(tenantId, conditionDatum, arg.getStoreId())) {
                    matchedConditionData.add(conditionDatum);
                }
            }

            ActivityExt activity = filterActivityByProduct(tenantId, matchedConditionData, arg.getProductId());
            if (!Objects.isNull(activity)) {
                return activity.getActivity();
            }
        }

        List<ActivityExt> allData = grouped.get(ALL);
        if (!CollectionUtils.isEmpty(allData)) {
            ActivityExt activity = filterActivityByProduct(tenantId, allData, arg.getProductId());
            if (!Objects.isNull(activity)) {
                return activity.getActivity();
            }
        }

        return null;
    }

    private ActivityExt filterActivityByProduct(String tenantId, List<ActivityExt> activities, String productId) {
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }

        Map<String, List<ActivityExt>> grouped = activities.stream().collect(Collectors.groupingBy(ActivityExt::getProductRangeType));

        List<ActivityExt> fixedData = grouped.get(FIXED);
        if (!CollectionUtils.isEmpty(fixedData)) {
            for (ActivityExt fixedDatum : fixedData) {
                if (fixedDatum.getProductIds().contains(productId)) {
                    return fixedDatum;
                }
            }
        }

        List<ActivityExt> conditionData = grouped.get(CONDITION);
        if (!CollectionUtils.isEmpty(conditionData)) {
            for (ActivityExt conditionDatum : conditionData) {
                if (productInConditionRange(tenantId, conditionDatum, productId)) {
                    return conditionDatum;
                }
            }
        }

        List<ActivityExt> allData = grouped.get(ALL);
        if (!CollectionUtils.isEmpty(allData)) {
            return allData.get(0);
        }

        return null;
    }

    private boolean productInConditionRange(String tenantId, ActivityExt activity, String storeId) {
        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(storeId));

        SearchTemplateQuery stq = QueryDataUtil.minimumFindOneQuery(idFilter);
        stq.setWheres(activity.getProductWheres());

        List<IObjectData> product = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PRODUCT_OBJ,
                stq,
                Lists.newArrayList("_id")
        );

        return CollectionUtils.isNotEmpty(product);
    }

    @Getter
    @Builder
    public static class Arg implements ILocateActivityArg {

        private MengNiuTenantInformation tenant;

        private String storeId;

        private String productId;
    }
}