package com.facishare.crm.fmcg.mengniu.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventAbandonException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.business.StoreStockCheckMultipleActivitySelector;
import com.facishare.crm.fmcg.mengniu.dto.*;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.crm.fmcg.tpm.business.FmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ExceptionStrategyEnum;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.ObjectDataUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class MultipleActivityStoreStockCheckEventHandler extends CommonSalesEventHandler<StoreStockCheckEventData> {

    @Resource
    private StoreStockCheckMultipleActivitySelector storeStockCheckMultipleActivitySelector;

    @Resource
    protected FmcgSerialNumberService fmcgSerialNumberService;


    @Override
    protected String buildEventIdentityKey(SalesEvent<StoreStockCheckEventData> event) {
        return String.format("%s.%s.%s", event.getTenantId(), event.getEventType(), event.getData().getStoreStockCheckId());
    }

    @Override
    protected List<RedPacketReward> calculateRewards(SalesEvent<StoreStockCheckEventData> event) {
        MengNiuTenantInformation tenant = tenantHierarchyService.load(event.getTenantId());
        if (MengNiuTenantInformation.ROLE_OTHERS.equals(tenant.getRole())) {
            return Lists.newArrayList();
        }

        FullMultipleStoreStockCheckEventInformation fullEvent = loadFullEventInformation(tenant, event);

        List<RedPacketReward> allRewards = Lists.newArrayList();
        for (FullMultipleStoreStockCheckEventDetail eventDetail : fullEvent.getDetails()) {

            FullStoreStockCheckEventInformation fakeEvent = FullStoreStockCheckEventInformation.builder()
                    .activity(eventDetail.getActivity())
                    .storeStockCheck(fullEvent.getStoreStockCheck())
                    .rewardLimitConfig(eventDetail.getRewardLimitConfig())
                    .tenantName(fullEvent.getTenantName())
                    .store(fullEvent.getStore())
                    .storeStockCheckDetails(eventDetail.getStoreStockCheckDetails())
                    .build();

            RedPacketReward storeOwnerReward = calculateStoreOwnerReward(tenant, event, fakeEvent);
            RedPacketReward storeWarehouseOwnerReward = calculateStoreWarehouseOwnerReward(tenant, event, fakeEvent);
            RedPacketReward mBossReward = calculateMBossReward(tenant, event, fakeEvent);
            RedPacketReward salesmenReward = calculateSalesmenReward(tenant, event, fakeEvent);

            List<RedPacketReward> rewards = Lists.newArrayList(mBossReward, storeWarehouseOwnerReward, storeOwnerReward, salesmenReward);

            try {
                validateActivityAmount(fakeEvent.getActivity(), rewards);
            } catch (EventAbandonException ex) {
                log.info("insufficient balance : {}.{}", event.getData().getStoreStockCheckId(), fakeEvent.getActivity().getId());
                continue;
            }

            allRewards.addAll(rewards);
        }

        return allRewards;
    }

    private RedPacketReward calculateStoreWarehouseOwnerReward(MengNiuTenantInformation tenant, SalesEvent<StoreStockCheckEventData> event, FullStoreStockCheckEventInformation fullEvent) {
        try {
            List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(tenant, fullEvent, RewardConstants.ROLE_SW_OWNER);
            PaymentAccount from = loadTenantCloudAccountByRole(tenant, fullEvent.getActivity(), RewardConstants.ROLE_SW_OWNER);
            PaymentAccount to = loadWeChatPaymentAccountFromStoreWarehouse(tenant, fullEvent.getStoreStockCheck().getTenantId(), fullEvent.getStoreStockCheck().get("account_id__c", String.class));

            String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_SW_OWNER.toLowerCase());
            String publishMode = fullEvent.getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);
            // 取活动上的有效天数
            Long expirationDate = getExpirationDate(fullEvent, publishMode, RewardConstants.ROLE_SW_OWNER.toLowerCase());

            return RedPacketReward.of(tenant, event, fullEvent, RewardConstants.ROLE_SW_OWNER, from, to, details, publishMode, expirationDate);
        } catch (Exception ex) {
            log.error("store owner calculate error : ", ex);
            return null;
        }
    }


    private Long getExpirationDate(FullStoreStockCheckEventInformation fullEvent, String publishMode, String role) {
        Long expirationDate = null;
        if (TPMGrayUtils.isMengNiuSignInGoodsFreshStandard(fullEvent.getActivity().getTenantId())) {
            try {
                String effectiveDaysKey = String.format("%s_effective_days__c", role);
                Integer effectiveDay = fullEvent.getActivity().get(effectiveDaysKey, Integer.class);
                expirationDate = RedPacketReward.PUBLISH_MODE_AUTO.equals(publishMode) ? null : getExpirationDate(effectiveDay);
            } catch (Exception exception) {
                log.error("get expiration date error : ", exception);
            }
        }
        return expirationDate;
    }

    private RewardLimitConfig loadRewardLimitConfig(MengNiuTenantInformation tenant, IObjectData activity, String channelId, String storeId) {
        IObjectData storeConfig = loadRewardLimitConfigByStore(tenant, activity.getId(), storeId);
        if (Objects.nonNull(storeConfig)) {
            return convertToRewardLimitConfig(tenant, activity, storeConfig);
        }

        IObjectData channelConfig = loadRewardLimitConfigByChannel(tenant, activity.getId(), channelId);
        if (Objects.nonNull(channelConfig)) {
            return convertToRewardLimitConfig(tenant, activity, channelConfig);
        }

        IObjectData activityConfig = loadRewardLimitConfigByActivity(tenant, activity.getId());
        if (Objects.nonNull(activityConfig)) {
            return convertToRewardLimitConfig(tenant, activity, activityConfig);
        }
        return convertToRewardLimitConfig(activity);
    }

    private RewardLimitConfig convertToRewardLimitConfig(IObjectData activity) {
        RewardLimitConfig config = new RewardLimitConfig();
        config.setLimitMode(activity.get("reward_limit_mode__c", String.class, RewardLimitConfig.MULTIPLE_ORDER));
        config.setTotalLimit(-1);
        config.setMonthlyTotalLimit(-1);
        config.setSkuLimit(Maps.newHashMap());
        config.setMonthlySkuLimit(Maps.newHashMap());
        return config;
    }

    private RewardLimitConfig convertToRewardLimitConfig(MengNiuTenantInformation tenant, IObjectData activity, IObjectData activityConfig) {
        RewardLimitConfig config = new RewardLimitConfig();

        config.setLimitMode(activity.get("reward_limit_mode__c", String.class, RewardLimitConfig.MULTIPLE_ORDER));
        config.setTotalLimit(activityConfig.get("total_limit__c", Integer.class, -1));
        config.setMonthlyTotalLimit(activityConfig.get("monthly_total_limit__c", Integer.class, -1));
        config.setSkuLimit(Maps.newHashMap());
        config.setMonthlySkuLimit(Maps.newHashMap());

        List<IObjectData> skuLimitList = querySkuLimit(tenant, activityConfig.getId());

        for (IObjectData skuLimit : skuLimitList) {
            String skuId = skuLimit.get("sku_id__c", String.class);
            Integer limit = skuLimit.get("limit__c", Integer.class);

            if (!Strings.isNullOrEmpty(skuId) && !Objects.isNull(limit) && limit > -1) {
                config.getSkuLimit().put(skuId, limit);
            }
        }

        for (IObjectData skuLimit : skuLimitList) {
            String skuId = skuLimit.get("sku_id__c", String.class);
            Integer limit = skuLimit.get("monthly_limit__c", Integer.class);

            if (!Strings.isNullOrEmpty(skuId) && !Objects.isNull(limit) && limit > -1) {
                config.getMonthlySkuLimit().put(skuId, limit);
            }
        }

        return config;
    }

    private List<IObjectData> querySkuLimit(MengNiuTenantInformation tenant, String masterConfigId) {
        IFilter masterIdFilter = new Filter();
        masterIdFilter.setFieldName("store_reward_limit_id__c");
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(Lists.newArrayList(masterConfigId));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(masterIdFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                "store_sku_reward_limit__c",
                stq,
                Lists.newArrayList(
                        "_id", "sku_id__c", "limit__c", "store_reward_limit_id__c", "monthly_limit__c"
                ));
    }

    private IObjectData loadRewardLimitConfigByActivity(MengNiuTenantInformation tenant, String activityId) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName("store_id__c");
        storeIdFilter.setOperator(Operator.IS);
        storeIdFilter.setFieldValues(Lists.newArrayList());

        List<IFilter> iFilters = Lists.newArrayList(activityIdFilter, storeIdFilter);

        IFilter channelIdFilter = new Filter();
        channelIdFilter.setFieldName("channel_id__c");
        channelIdFilter.setOperator(Operator.IS);
        channelIdFilter.setFieldValues(Lists.newArrayList());

        iFilters.add(channelIdFilter);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                iFilters,
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                "store_reward_limit__c",
                stq,
                Lists.newArrayList(
                        "_id", "activity_id__c", "store_id__c", "total_limit__c", "monthly_total_limit__c"
                )).stream().findFirst().orElse(null);
    }

    private IObjectData loadRewardLimitConfigByChannel(MengNiuTenantInformation tenant, String activityId, String channelId) {
        if (Strings.isNullOrEmpty(channelId)) {
            return null;
        }

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName("channel_id__c");
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(channelId));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(Lists.newArrayList(activityIdFilter, storeIdFilter), Lists.newArrayList(order));

        return QueryDataUtil.find(serviceFacade, tenant.getManufacturer().getTenantId(), "store_reward_limit__c", stq, Lists.newArrayList("_id", "activity_id__c", "store_id__c", "channel_id__c", "total_limit__c", "monthly_total_limit__c")).stream().findFirst().orElse(null);
    }

    private IObjectData loadRewardLimitConfigByStore(MengNiuTenantInformation tenant, String activityId, String storeId) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName("store_id__c");
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(activityIdFilter, storeIdFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                "store_reward_limit__c",
                stq,
                Lists.newArrayList(
                        "_id", "activity_id__c", "store_id__c", "total_limit__c", "monthly_total_limit__c"
                )).stream().findFirst().orElse(null);
    }

    private void validateActivityAmount(IObjectData activity, List<RedPacketReward> rewards) {
        Boolean allowOverLimitReward = activity.get("allow_over_limit_reward__c", Boolean.class);
        BigDecimal budget = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class);

        BigDecimal incomingRewardAmount = calculateIncomingRewardAmount(rewards);
        BigDecimal rewardedAmount = calculateRewardedAmount(activity);

        log.info("after amount information : {} - {} - {}", budget, rewardedAmount, incomingRewardAmount);

        if (budget.compareTo(rewardedAmount.add(incomingRewardAmount)) <= 0) {
            if (Boolean.TRUE.equals(allowOverLimitReward)) {
                for (RedPacketReward reward : rewards) {
                    if (Objects.nonNull(reward)) {
                        reward.setOverLimit(true);
                    }
                }
            } else {
                throw new EventAbandonException("insufficient balance.");
            }
        }
    }

    private FullMultipleStoreStockCheckEventInformation loadFullEventInformation(MengNiuTenantInformation tenant, SalesEvent<StoreStockCheckEventData> event) {
        IObjectData storeStockCheck = serviceFacade.findObjectDataIgnoreAll(User.systemUser(event.getTenantId()), event.getData().getStoreStockCheckId(), ApiNames.STORE_STOCK_CHECK_UDEF_OBJ);
        IObjectData store = serviceFacade.findObjectDataIgnoreAll(User.systemUser(storeStockCheck.getTenantId()), storeStockCheck.get("account_id__c", String.class), ApiNames.ACCOUNT_OBJ);
        String channel = store.get(AccountFields.MENGNIU_STORE_TYPE, String.class);
        List<IObjectData> allStoreStockCheckDetails = queryStoreStockCheckDetails(event.getTenantId(), event.getData().getStoreStockCheckId());
        List<String> productIds = allStoreStockCheckDetails.stream().map(m -> m.get("product_id__c", String.class)).distinct().collect(Collectors.toList());

        Map<String, List<String>> activityProductMapping = new HashMap<>();
        Map<String, IObjectData> activityMap = new HashMap<>();
        for (String productId : productIds) {
            IObjectData activity = storeStockCheckMultipleActivitySelector.invoke(StoreStockCheckMultipleActivitySelector.Arg.builder().tenant(tenant).storeId(store.getId()).productId(productId).build());
            if (Objects.isNull(activity)) {
                continue;
            }
            if (activityProductMapping.containsKey(activity.getId())) {
                activityProductMapping.get(activity.getId()).add(productId);
            } else {
                activityProductMapping.put(activity.getId(), Lists.newArrayList(productId));
            }
            activityMap.put(activity.getId(), activity);
        }

        if (MapUtils.isEmpty(activityProductMapping)) {
            throw new EventAbandonException("activity not found.");
        }

        FullMultipleStoreStockCheckEventInformation eventInformation = FullMultipleStoreStockCheckEventInformation.builder()
                .store(store)
                .storeStockCheck(storeStockCheck)
                .tenantName(getTenantName(event.getTenantId()))
                .details(Lists.newArrayList())
                .build();
        for (Map.Entry<String, List<String>> entry : activityProductMapping.entrySet()) {
            String activityId = entry.getKey();
            List<String> productIdList = entry.getValue();
            IObjectData activity = activityMap.get(activityId);
            RewardLimitConfig rewardLimitConfig = loadRewardLimitConfig(tenant, activity, channel, store.getId());

            List<IObjectData> storeStockCheckDetails = Lists.newArrayList();
            for (IObjectData allStoreStockCheckDetail : allStoreStockCheckDetails) {
                if (productIdList.contains(allStoreStockCheckDetail.get("product_id__c", String.class))) {
                    storeStockCheckDetails.add(allStoreStockCheckDetail);
                }
            }

            log.info("activity : {}", activity);

            List<String> allSerialNumberIds = Lists.newArrayList();
            for (IObjectData storeStockCheckDetail : storeStockCheckDetails) {
                @SuppressWarnings("unchecked")
                List<String> serialNumberIds = storeStockCheckDetail.get("unique_product_code_combination__c", List.class);
                if (!CollectionUtils.isEmpty(serialNumberIds)) {
                    allSerialNumberIds.addAll(serialNumberIds);
                }
            }

            // 过滤活动 id 下的商品条码已经生成过红包激励的，不再重复激励该商品。
            if (TPMGrayUtils.isMengNiuFilterActivitySerialNumber(activity.getTenantId())) {
                // 过滤码+活动重复激励的
                filterActivitySerialNumber(activity.getTenantId(), activity.getId(), allSerialNumberIds);
                // 执行判断异常码状态的过滤
                filterSerialNumberByException(activity, allSerialNumberIds);
            }

            Map<String, IObjectData> serialNumberMap = loadSerialNumberMap(event.getTenantId(), allSerialNumberIds);

            if (TPMGrayUtils.isMengNiuFilterCustomBCSerialNumber(activity.getTenantId())) {
                filterSerialNumberByCustom(serialNumberMap, activity);
            }

            JSONObject productRangeJson = JSON.parseObject(activity.get(TPMActivityFields.PRODUCT_RANGE, String.class));
            String productRangeType = productRangeJson.getString("type");
            Set<String> skuIds = Sets.newHashSet();
            if ("FIXED".equalsIgnoreCase(productRangeType)) {
                List<IObjectData> range = queryActivityProductRange(activity);
                skuIds = range.stream().map(m -> m.get("product_id", String.class)).collect(Collectors.toSet());
                if (TPMGrayUtils.isMengNiuSignInGoodsFreshStandard(activity.getTenantId())) {
                    log.info("signInGoodsFreshStandard tenantId is {}", activity.getTenantId());
                    handlerProductRangeByFreshStandard(activity, serialNumberMap, storeStockCheckDetails, range);
                }
            }

            List<FullStoreStockCheckDetailInformation> fullDetails = Lists.newArrayList();
            for (IObjectData storeStockCheckDetail : storeStockCheckDetails) {

                @SuppressWarnings("unchecked")
                List<String> serialNumberIds = storeStockCheckDetail.get("unique_product_code_combination__c", List.class);

                if (!CollectionUtils.isEmpty(serialNumberIds)) {
                    for (String serialNumberId : serialNumberIds) {
                        if (serialNumberMap.containsKey(serialNumberId)) {
                            String skuId = storeStockCheckDetail.get("product_id__c", String.class);

                            if ("ALL".equalsIgnoreCase(productRangeType) || ("FIXED".equalsIgnoreCase(productRangeType) && skuIds.contains(skuId))) {

                                FullStoreStockCheckDetailInformation fullDetail = new FullStoreStockCheckDetailInformation();
                                fullDetail.setId(storeStockCheckDetail.getId());
                                fullDetail.setName(storeStockCheckDetail.get("name", String.class));
                                fullDetail.setSkuId(skuId);
                                fullDetail.setSnId(serialNumberId);
                                fullDetail.setSnObj(serialNumberMap.get(serialNumberId));

                                fullDetails.add(fullDetail);
                            }
                        }
                    }
                }
            }

            eventInformation.getDetails().add(FullMultipleStoreStockCheckEventDetail.builder()
                    .storeStockCheckDetails(fullDetails)
                    .rewardLimitConfig(rewardLimitConfig)
                    .activity(activity)
                    .build());
        }

        return eventInformation;
    }

    private void filterSerialNumberByCustom(Map<String, IObjectData> serialNumberMap, IObjectData activity) {
        try {
            // 从serialNumberMap中获取所有条码的name
            List<String> serialNumberNames = serialNumberMap.values().stream()
                    .map(serialNumber -> serialNumber.get("name", String.class))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            log.info("serialNumberNames size: {}", serialNumberNames.size());
            // 根据 serialNumberNames 查是否存在异常码
            List<IObjectData> customSerialNames = queryCustomSerialNumber(activity.getTenantId(), serialNumberNames);
            if (CollectionUtils.isEmpty(customSerialNames)) {
                return;
            }
            log.info("exceptionSerialNumber size: {}", customSerialNames.size());
            // 如果serialNumberMap的 values存在exceptionSerialNames的name，则过滤掉 exceptionSerialNames的name，则过滤掉
            serialNumberMap.values().removeIf(serialNumber -> customSerialNames.stream()
                    .map(v -> v.get("sn_name__c", String.class))
                    .anyMatch(name -> name.equals(serialNumber.get("name", String.class))));
            log.info("serialNumberMap size: {}", serialNumberMap.size());

        } catch (Exception e) {
            log.error("过滤活动条码异常Custom", e);
        }
    }


    private List<IObjectData> queryCustomSerialNumber(String tenantId, List<String> serialNumberNames) {
        if (CollectionUtils.isEmpty(serialNumberNames)) {
            return Lists.newArrayList();
        }

        IFilter serialNumberNameFilter = new Filter();
        serialNumberNameFilter.setFieldName("sn_name__c");
        serialNumberNameFilter.setOperator(Operator.IN);
        serialNumberNameFilter.setFieldValues(serialNumberNames);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                "outer_rewards_notify__c",
                QueryDataUtil.minimumQuery(Lists.newArrayList(serialNumberNameFilter), Lists.newArrayList(order)),
                Lists.newArrayList("_id", "sn_name__c"));
    }

    /**
     * 过滤活动ID下已经生成过红包激励的商品条码，避免重复激励
     *
     * @param tenantId           租户ID
     * @param activityId         活动
     * @param allSerialNumberIds 所有商品条码ID列表（会被修改，过滤掉已激励过的条码）
     */
    private void filterActivitySerialNumber(String tenantId, String activityId, List<String> allSerialNumberIds) {
        if (CollectionUtils.isEmpty(allSerialNumberIds)) {
            return;
        }

        log.info("过滤活动条码, activityId: {}, 条码数量: {}", activityId, allSerialNumberIds.size());

        try {
            // 1. 查询已存在的红包记录详情
            List<IObjectData> recordDetails = queryRedPacketRecordDetails(tenantId, allSerialNumberIds);
            if (CollectionUtils.isEmpty(recordDetails)) {
                return;
            }

            // 2. 获取红包记录ID列表
            List<String> redPacketIds = recordDetails.stream()
                    .map(detail -> detail.get(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID, String.class))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(redPacketIds)) {
                return;
            }

            // 3. 查询当前活动下的红包记录
            List<IObjectData> records = queryRedPacketRecords(tenantId, activityId, redPacketIds);
            if (CollectionUtils.isEmpty(records)) {
                return;
            }

            // 4. 获取需要移除的条码ID
            Set<String> filteredRedPacketIds = records.stream().map(DBRecord::getId).collect(Collectors.toSet());
            List<String> serialNumberIdsToRemove = recordDetails.stream()
                    .filter(detail -> filteredRedPacketIds.contains(detail.get(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID, String.class)))
                    .map(detail -> detail.get(RedPacketRecordDetailObjFields.SERIAL_NUMBER_ID, String.class))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(serialNumberIdsToRemove)) {
                return;
            }

            // 5. 移除已激励过的条码ID
            int beforeSize = allSerialNumberIds.size();
            allSerialNumberIds.removeAll(serialNumberIdsToRemove);
            log.info("过滤完成, 移除{}个条码, 过滤前:{}, 过滤后:{}",
                    beforeSize - allSerialNumberIds.size(), beforeSize, allSerialNumberIds.size());

        } catch (Exception e) {
            log.error("过滤活动条码异常", e);
        }
    }

    private void filterSerialNumberByException(IObjectData activity, List<String> allSerialNumberIds) {
        String exceptionReward = activity.get("serial_number_exception_reward__c", String.class);
        try {
            if (ExceptionStrategyEnum.NO_REWARD.code().equals(exceptionReward) && CollectionUtils.isNotEmpty(allSerialNumberIds)) {
                log.info("activity serial number exception reward is {}", exceptionReward);
                List<String> serialNumberExceptionType = CommonUtils.castIgnore(activity.get("serial_number_exception_type__c"), String.class);
                if (!CollectionUtils.isEmpty(serialNumberExceptionType)) {
                    filterSerialNumberByExceptionType(activity.getTenantId(), serialNumberExceptionType, allSerialNumberIds);
                }
            }
        } catch (Exception e) {
            log.error("过滤活动条码异常Exception", e);
        }
    }

    private void filterSerialNumberByExceptionType(String tenantId, List<String> serialNumberExceptionTypes, List<String> allSerialNumberIds) {
        String storeCheckActionId = fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.STORE_STOCK_CHECK);
        if (!Strings.isNullOrEmpty(storeCheckActionId)) {
            log.info("根据码状态过滤条码, actionId: {}, 条码数量: {}", storeCheckActionId, allSerialNumberIds.size());
            List<IObjectData> statusExceptionTypes = queryStatusExceptionTypes(tenantId, storeCheckActionId, allSerialNumberIds, serialNumberExceptionTypes);
            if (CollectionUtils.isEmpty(statusExceptionTypes)) {
                return;
            }
            List<String> serialNumberIdsToRemove = statusExceptionTypes.stream()
                    .map(status -> status.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(serialNumberIdsToRemove)) {
                return;
            }
            int beforeSize = allSerialNumberIds.size();
            allSerialNumberIds.removeAll(serialNumberIdsToRemove);
            log.info("根据码状态过滤完成, 移除{}个条码, 过滤前:{}, 过滤后:{}", beforeSize - allSerialNumberIds.size(), beforeSize, allSerialNumberIds.size());
        }
    }

    /**
     * 查询码状态记录
     */
    private List<IObjectData> queryStatusExceptionTypes(String tenantId, String actionId, List<String> serialNumberIds, List<String> serialNumberExceptionTypes) {
        if (CollectionUtils.isEmpty(serialNumberExceptionTypes)) {
            return null;
        }
        IFilter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(FMCGSerialNumberStatusFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));

        IFilter serialNumberIdFilter = new Filter();
        serialNumberIdFilter.setFieldName(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID);
        serialNumberIdFilter.setOperator(Operator.IN);
        serialNumberIdFilter.setFieldValues(serialNumberIds);

        IFilter exceptionStatusFilter = new Filter();
        exceptionStatusFilter.setFieldName(FMCGSerialNumberStatusFields.WHETHER_ABNORMAL);
        exceptionStatusFilter.setOperator(Operator.EQ);
        exceptionStatusFilter.setFieldValues(Lists.newArrayList("true"));

        IFilter exceptionTypeFilter = new Filter();
        exceptionTypeFilter.setFieldName(FMCGSerialNumberStatusFields.EXCEPTION_TYPE);
        exceptionTypeFilter.setOperator(Operator.IN);
        exceptionTypeFilter.setFieldValues(serialNumberExceptionTypes);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ,
                QueryDataUtil.minimumQuery(actionIdFilter, serialNumberIdFilter, exceptionStatusFilter, exceptionTypeFilter),
                Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.EXCEPTION_TYPE, FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID));
    }

    /**
     * 查询红包记录详情
     */
    private List<IObjectData> queryRedPacketRecordDetails(String tenantId, List<String> serialNumberIds) {
        IFilter serialNumberIdFilter = new Filter();
        serialNumberIdFilter.setFieldName(RedPacketRecordDetailObjFields.SERIAL_NUMBER_ID);
        serialNumberIdFilter.setOperator(Operator.IN);
        serialNumberIdFilter.setFieldValues(serialNumberIds);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.RED_PACKET_RECORD_DETAIL_OBJ,
                QueryDataUtil.minimumQuery(Lists.newArrayList(serialNumberIdFilter), Lists.newArrayList(order)),
                Lists.newArrayList("_id", RedPacketRecordDetailObjFields.SERIAL_NUMBER_ID, RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID));
    }

    /**
     * 查询红包记录
     */
    private List<IObjectData> queryRedPacketRecords(String tenantId, String activityId, List<String> redPacketIds) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordObjFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter redPacketIdFilter = new Filter();
        redPacketIdFilter.setFieldName(CommonFields.ID);
        redPacketIdFilter.setOperator(Operator.IN);
        redPacketIdFilter.setFieldValues(redPacketIds);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.RED_PACKET_RECORD_OBJ,
                QueryDataUtil.minimumQuery(Lists.newArrayList(activityIdFilter, redPacketIdFilter), Lists.newArrayList(order)),
                Lists.newArrayList("_id"));
    }

    private void handlerProductRangeByFreshStandard(IObjectData activity,
                                                    Map<String, IObjectData> serialNumberMap,
                                                    List<IObjectData> storeStockCheckDetails,
                                                    List<IObjectData> range) {

        // no limit 不处理
        String type = activity.get(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, String.class);
        if (TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT.equalsIgnoreCase(type)) {
            return;
        }

        if (CollectionUtils.isEmpty(range)) {
            return;
        }

        Map<String, IObjectData> productRangeMap = range.stream()
                .collect(Collectors.toMap(
                v -> v.get("product_id", String.class),
                v -> v,
                (existing, replacement) -> existing));
        Set<String> productIds = productRangeMap.keySet();
        Map<String, Integer> prodcutQuantityMap = new HashMap<>();
        if (TPMActivityFields.ProductRangeFreshStandard.BY_REMAINING_DAYS.equalsIgnoreCase(type)) {
            prodcutQuantityMap = findProductQualityGuaranteePeriod(activity.getTenantId(), productIds);
        }
        log.info("prodcutQuantityMap size  is {}", prodcutQuantityMap.size());
        log.info("before serialNumberMap size is {}", serialNumberMap.size());
        // 产品保险度类型
        for (IObjectData storeStockCheckDetail : storeStockCheckDetails) {
            @SuppressWarnings("unchecked")
            List<String> serialNumberIds = storeStockCheckDetail.get("unique_product_code_combination__c", List.class);
            if (!CollectionUtils.isEmpty(serialNumberIds)) {
                for (String serialNumberId : serialNumberIds) {
                    IObjectData serialNumberObj = serialNumberMap.get(serialNumberId);
                    if (serialNumberObj != null) {
                        String skuId = storeStockCheckDetail.get("product_id__c", String.class);
                        if (productRangeMap.containsKey(skuId)) {
                            IObjectData productRange = productRangeMap.get(skuId);
                            Long manufactureDate = serialNumberObj.get(FMCGSerialNumberFields.MANUFACTURE_DATE, Long.class);
                            if (TPMActivityFields.ProductRangeFreshStandard.BY_DATE_RANGE.equalsIgnoreCase(type) &&
                                    !filterProductRangeByDateRange(productRange, manufactureDate, skuId)) {
                                log.info("serialNumberId:{} 不满足按日期范围，排除", serialNumberId);
                                // 不满足按日期范围，排除
                                serialNumberMap.remove(serialNumberId);
                            } else if (TPMActivityFields.ProductRangeFreshStandard.BY_REMAINING_DAYS.equalsIgnoreCase(type) &&
                                    !filterProductRangeByRemainingDays(productRange, manufactureDate, prodcutQuantityMap, skuId)) {
                                log.info("serialNumberId:{} 不满足按剩余有效期，排除", serialNumberId);
                                //不满足按剩余有效期，排除
                                serialNumberMap.remove(serialNumberId);
                            }
                        }
                    }
                }
            }
        }
        log.info("after serialNumberMap size is {}", serialNumberMap.size());
    }

    private boolean filterProductRangeByRemainingDays(IObjectData productRange,
                                                      Long manufactureDate,
                                                      Map<String, Integer> prodcutQuantityMap,
                                                      String skuId) {
        Integer quantity = prodcutQuantityMap.get(skuId);
        if (quantity == null) {
            log.info("productRange productId {} quantity is null", skuId);
            return false;
        }

        if (manufactureDate == null) {
            log.info(" productId {} serialNumber manufactureDate is null", skuId);
            return false;
        }

        String matchMethod = productRange.get(TPMActivityProductRangeFields.MATCH_METHOD, String.class);
        Long expiredDays = productRange.get(TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS, Long.class);
        long remainingDays = (toDayStart(manufactureDate) + quantity * 24 * 60 * 60 * 1000L - toDayStart(System.currentTimeMillis())) / (24 * 60 * 60 * 1000);
        log.info("remainingDays is {}, expiredDays is {}", remainingDays, expiredDays);
        if (matchMethod.equals(TPMActivityProductRangeFields.MATCH_METHOD__NEW_GOODS)) {
            return remainingDays >= expiredDays;
        } else {
            return remainingDays <= expiredDays;
        }
    }

    private Map<String, Integer> findProductQualityGuaranteePeriod(String tenantId, Set<String> productIds) {
        // 查询产品
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(new ArrayList<>(productIds));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(true);

        SearchTemplateQuery rangeQuery = QueryDataUtil.minimumQuery(Lists.newArrayList(idFilter), Lists.newArrayList(order));

        List<IObjectData> product = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PRODUCT_OBJ,
                rangeQuery,
                Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.RECORD_TYPE, ProductFields.QUALITY_GUARANTEE_PERIOD)
        );
        return product.stream().filter(v -> v.get(ProductFields.QUALITY_GUARANTEE_PERIOD, Integer.class) != null)
                .collect(Collectors.toMap(v -> v.get(CommonFields.ID, String.class), v -> v.get(ProductFields.QUALITY_GUARANTEE_PERIOD, Integer.class)));
    }

    private long toDayStart(long time) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        return localDateTime.toLocalDate().atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    private boolean filterProductRangeByDateRange(IObjectData productRange, Long manufactureDate, String skuId) {
        if (manufactureDate == null) {
            log.info(" productId {} serialNumber manufactureDate is null", skuId);
            return false;
        }

        Long start = productRange.get(TPMActivityProductRangeFields.MANUFACTURE_DATE_START, Long.class, 0L);
        Long end = productRange.get(TPMActivityProductRangeFields.MANUFACTURE_DATE_END, Long.class, 0L);
        return manufactureDate >= start && manufactureDate <= end;
    }

    private Map<String, IObjectData> loadSerialNumberMap(String tenantId, List<String> allSerialNumberIds) {
        if (CollectionUtils.isEmpty(allSerialNumberIds)) {
            return Maps.newHashMap();
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName("_id");
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(allSerialNumberIds);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(Lists.newArrayList(idFilter), Lists.newArrayList(order));

        return QueryDataUtil.find(serviceFacade, tenantId, "FMCGSerialNumberObj", stq, Lists.newArrayList(
                "_id", "name", "manufacture_date", "batch_code"
        )).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
    }

    private List<IObjectData> queryActivityProductRange(IObjectData activity) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        SearchTemplateQuery rangeQuery = QueryDataUtil.minimumQuery(activityIdFilter);

        List<IObjectData> range = QueryDataUtil.find(
                serviceFacade,
                activity.getTenantId(),
                "TPMActivityProductRangeObj",
                rangeQuery,
                Lists.newArrayList("_id", "product_id", "store_owner_red_packet_amount__c", "m_boss_red_packet_amount__c",
                        TPMActivityProductRangeFields.MANUFACTURE_DATE_START, TPMActivityProductRangeFields.MANUFACTURE_DATE_END,
                        TPMActivityProductRangeFields.MATCH_METHOD, TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS)
        );

        if (CollectionUtils.isEmpty(range)) {
            return Lists.newArrayList();
        }
        return range;
    }

    private List<IObjectData> queryStoreStockCheckDetails(String tenantId, String storeStockCheckId) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName("store_stock_check_id__c");
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(storeStockCheckId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter);

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.STORE_STOCK_CHECK_DETAIL_UDEF_OBJ, stq, Lists.newArrayList(
                "_id",
                "name",
                "unique_product_code_combination__c",
                "product_id__c"
        ));
    }

    private RedPacketReward calculateSalesmenReward(
            MengNiuTenantInformation tenant,
            SalesEvent<StoreStockCheckEventData> event,
            FullStoreStockCheckEventInformation fullEvent) {

        List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(tenant, fullEvent, RewardConstants.ROLE_SALESMEN);
        PaymentAccount from = loadTenantCloudAccountByRole(tenant, fullEvent.getActivity(), RewardConstants.ROLE_SALESMEN);

        String rewardPersonId;

        if (TPMGrayUtils.isMEngNiuEventGray(tenant.getManufacturer().getTenantId())) {
            rewardPersonId = getSalesmenOwner(fullEvent.getStoreStockCheck());
        } else {
            rewardPersonId = fullEvent.getStoreStockCheck().getOwner().get(0);
        }
        log.info("storeStockCheck calculateSalesmenReward rewardPersonId is {}", rewardPersonId);
        PaymentAccount to;
        if (Objects.isNull(rewardPersonId)) {
            to = PaymentAccount.of(WeChatPaymentAccount.builder().build());
        } else {
            to = PaymentAccount.of(
                    ApiNames.PERSONNEL_OBJ,
                    String.format("%s.%s", fullEvent.getStoreStockCheck().getTenantId(), rewardPersonId),
                    loadWeChatPaymentAccountFromEmployee(fullEvent.getStoreStockCheck().getTenantId(), rewardPersonId));
        }

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_SALESMEN.toLowerCase());
        String publishMode = fullEvent.getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);
        // 取活动上的有效天数
        Long expirationDate = getExpirationDate(fullEvent, publishMode, RewardConstants.ROLE_SALESMEN.toLowerCase());

        return RedPacketReward.of(tenant, event, fullEvent, RewardConstants.ROLE_SALESMEN, from, to, details, publishMode, expirationDate);
    }

    private String getSalesmenOwner(IObjectData storeStockCheck) {
        String storeStockCheckOwner = storeStockCheck.getOwner().get(0);
        // 如果存在外部负责人字段，说明，是负责人是外部的

        if (Objects.nonNull(storeStockCheck.getOutOwner()) && CollectionUtils.isNotEmpty(storeStockCheck.getOutOwner())) {

            // 查询店老板对应的业代人员，字段，获取人员 id, 如果 storeManger为空 取 owner的值
            List<String> storeManager = ObjectDataUtils.employees(storeStockCheck, "store_manager__c");
            if (CollectionUtils.isEmpty(storeManager)) {
                log.warn("store manager is empty : {}.{}", storeStockCheck.getTenantId(), storeStockCheck.getName());
                return null;
            } else {
                if (TPMGrayUtils.isSalesmenOwnerEventGray(storeStockCheck.getTenantId())){
                    //过滤掉 值 为 “-10000”的情况，然后如果为空，则返回 null
                    storeManager = storeManager.stream().filter(v -> !"-10000".equals(v)).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(storeManager)) {
                        return null;
                    }
                }
                return storeManager.get(0);
            }
        }

        return storeStockCheckOwner;
    }

    private RedPacketReward calculateStoreOwnerReward(
            MengNiuTenantInformation tenant,
            SalesEvent<StoreStockCheckEventData> event,
            FullStoreStockCheckEventInformation fullEvent) {

        List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(tenant, fullEvent, RewardConstants.ROLE_STORE_OWNER);
        PaymentAccount from = loadTenantCloudAccountByRole(tenant, fullEvent.getActivity(), RewardConstants.ROLE_STORE_OWNER);
        PaymentAccount to = loadWeChatPaymentAccountFromStore(fullEvent.getStoreStockCheck().getTenantId(), fullEvent.getStoreStockCheck().get("account_id__c", String.class));

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_STORE_OWNER.toLowerCase());
        String publishMode = fullEvent.getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);
        // 取活动上的有效天数
        Long expirationDate = getExpirationDate(fullEvent, publishMode, RewardConstants.ROLE_STORE_OWNER.toLowerCase());

        return RedPacketReward.of(tenant, event, fullEvent, RewardConstants.ROLE_STORE_OWNER, from, to, details, publishMode, expirationDate);
    }

    private static Long getExpirationDate(Integer effectiveDay) {
        Long expirationDate = null;
        if (effectiveDay != null) {
            LocalDateTime localDateTime = LocalDateTime.now();
            expirationDate = localDateTime.plusDays(effectiveDay + 1).withHour(0).withMinute(0).withSecond(0).withNano(0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli() - 1;
        }
        return expirationDate;
    }

    private List<FullStoreStockCheckDetailInformation> calculateLimitedDetails(MengNiuTenantInformation tenant, FullStoreStockCheckEventInformation fullEvent, String role) {
        RewardedData rewardedData = calculateRewardedData(tenant, fullEvent, role);
        RewardedData monthlyRewardedData = calculateMonthlyRewardedData(tenant, fullEvent, role);

        if (fullEvent.getRewardLimitConfig().getLimitMode().equals(RewardLimitConfig.SINGLE_ORDER) && rewardedData.getTotalRecordCount() > 0) {
            return Lists.newArrayList();
        }

        if (fullEvent.getRewardLimitConfig().getTotalLimit() <= -1
                && fullEvent.getRewardLimitConfig().getMonthlyTotalLimit() <= -1
                && fullEvent.getRewardLimitConfig().getSkuLimit().isEmpty()
                && fullEvent.getRewardLimitConfig().getMonthlySkuLimit().isEmpty()) {
            return fullEvent.getStoreStockCheckDetails();
        }

        if (fullEvent.getRewardLimitConfig().getTotalLimit() > -1 && rewardedData.getTotalCount() >= fullEvent.getRewardLimitConfig().getTotalLimit()) {
            return Lists.newArrayList();
        }

        if (fullEvent.getRewardLimitConfig().getMonthlyTotalLimit() > -1 && monthlyRewardedData.getTotalCount() >= fullEvent.getRewardLimitConfig().getMonthlyTotalLimit()) {
            return Lists.newArrayList();
        }

        List<FullStoreStockCheckDetailInformation> details = Lists.newArrayList();

        for (FullStoreStockCheckDetailInformation storeStockCheckDetail : fullEvent.getStoreStockCheckDetails()) {

            String skuId = storeStockCheckDetail.getSkuId();

            if (fullEvent.getRewardLimitConfig().getSkuLimit().containsKey(skuId)) {
                int limit = fullEvent.getRewardLimitConfig().getSkuLimit().get(skuId);
                int rewardedCount = rewardedData.getSkuCount().getOrDefault(skuId, 0);
                if (rewardedCount >= limit) {
                    continue;
                }
            }

            if (fullEvent.getRewardLimitConfig().getMonthlySkuLimit().containsKey(skuId)) {
                int limit = fullEvent.getRewardLimitConfig().getMonthlySkuLimit().get(skuId);
                int rewardedCount = monthlyRewardedData.getSkuCount().getOrDefault(skuId, 0);
                if (rewardedCount >= limit) {
                    continue;
                }
            }

            details.add(storeStockCheckDetail);

            if (rewardedData.getSkuCount().containsKey(skuId)) {
                rewardedData.getSkuCount().put(skuId, rewardedData.getSkuCount().get(skuId) + 1);
            } else {
                rewardedData.getSkuCount().put(skuId, 1);
            }

            if (monthlyRewardedData.getSkuCount().containsKey(skuId)) {
                monthlyRewardedData.getSkuCount().put(skuId, monthlyRewardedData.getSkuCount().get(skuId) + 1);
            } else {
                monthlyRewardedData.getSkuCount().put(skuId, 1);
            }
        }

        if (fullEvent.getRewardLimitConfig().getTotalLimit() != -1 && fullEvent.getRewardLimitConfig().getMonthlyTotalLimit() != -1) {
            int count1 = fullEvent.getRewardLimitConfig().getTotalLimit() - rewardedData.getTotalCount();
            int count2 = fullEvent.getRewardLimitConfig().getMonthlyTotalLimit() - monthlyRewardedData.getTotalCount();
            int count = Math.min(count1, count2);
            if (details.size() > count) {
                details = details.subList(0, count);
            }
        } else if (fullEvent.getRewardLimitConfig().getTotalLimit() != -1) {
            int count = fullEvent.getRewardLimitConfig().getTotalLimit() - rewardedData.getTotalCount();
            if (details.size() > count) {
                details = details.subList(0, count);
            }
        } else if (fullEvent.getRewardLimitConfig().getMonthlyTotalLimit() != -1) {
            int count = fullEvent.getRewardLimitConfig().getMonthlyTotalLimit() - monthlyRewardedData.getTotalCount();
            if (details.size() > count) {
                details = details.subList(0, count);
            }
        }

        return details;
    }

    private RewardedData calculateRewardedData(MengNiuTenantInformation tenant, FullStoreStockCheckEventInformation fullEvent, String role) {

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordObjFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getActivity().getId()));

        IFilter relatedStoreIdFilter = new Filter();
        relatedStoreIdFilter.setFieldName(RedPacketRecordObjFields.ACCOUNT_ID);
        relatedStoreIdFilter.setOperator(Operator.EQ);
        relatedStoreIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getStore().getId()));

        IFilter dataIdFilter = new Filter();
        dataIdFilter.setFieldName(RedPacketRecordObjFields.RELATED_OBJECT_DATA_ID);
        dataIdFilter.setOperator(Operator.NEQ);
        dataIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getStoreStockCheck().getId()));

        IFilter roleFilter = new Filter();
        roleFilter.setFieldName(RedPacketRecordObjFields.ROLE);
        roleFilter.setOperator(Operator.EQ);
        roleFilter.setFieldValues(Lists.newArrayList(RewardConstants.ROLE_VALUE_MAP.get(role)));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(activityIdFilter, relatedStoreIdFilter, roleFilter, dataIdFilter),
                Lists.newArrayList(order)
        );

        List<IObjectData> records = QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                ApiNames.RED_PACKET_RECORD_OBJ,
                stq,
                Lists.newArrayList(
                        "_id"
                ));


        RewardedData data = new RewardedData();
        data.setTotalRecordCount(records.size());

        List<String> recordIds = records.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<IObjectData> recordDetails = queryRecordDetails(tenant, recordIds);

        data.setTotalCount(recordDetails.size());
        data.setSkuCount(Maps.newHashMap());

        for (IObjectData recordDetail : recordDetails) {
            String productId = recordDetail.get(RedPacketRecordDetailObjFields.PRODUCT_ID, String.class);
            if (!Strings.isNullOrEmpty(productId)) {
                if (data.getSkuCount().containsKey(productId)) {
                    data.getSkuCount().put(productId, data.getSkuCount().get(productId) + 1);
                } else {
                    data.getSkuCount().put(productId, 1);
                }
            }
        }

        return data;
    }

    private RewardedData calculateMonthlyRewardedData(MengNiuTenantInformation tenant, FullStoreStockCheckEventInformation fullEvent, String role) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long begin = cal.getTimeInMillis();

        cal.add(Calendar.MONTH, 1);
        long end = cal.getTimeInMillis();

        IFilter timeFilter = new Filter();
        timeFilter.setFieldName(RedPacketRecordObjFields.REWARD_TIME);
        timeFilter.setOperator(Operator.BETWEEN);
        timeFilter.setFieldValues(Lists.newArrayList(String.valueOf(begin), String.valueOf(end)));

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordObjFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getActivity().getId()));

        IFilter relatedStoreIdFilter = new Filter();
        relatedStoreIdFilter.setFieldName(RedPacketRecordObjFields.ACCOUNT_ID);
        relatedStoreIdFilter.setOperator(Operator.EQ);
        relatedStoreIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getStore().getId()));

        IFilter dataIdFilter = new Filter();
        dataIdFilter.setFieldName(RedPacketRecordObjFields.RELATED_OBJECT_DATA_ID);
        dataIdFilter.setOperator(Operator.NEQ);
        dataIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getStoreStockCheck().getId()));

        IFilter roleFilter = new Filter();
        roleFilter.setFieldName(RedPacketRecordObjFields.ROLE);
        roleFilter.setOperator(Operator.EQ);
        roleFilter.setFieldValues(Lists.newArrayList(RewardConstants.ROLE_VALUE_MAP.get(role)));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(activityIdFilter, relatedStoreIdFilter, roleFilter, dataIdFilter, timeFilter),
                Lists.newArrayList(order)
        );

        List<IObjectData> records = QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                ApiNames.RED_PACKET_RECORD_OBJ,
                stq,
                Lists.newArrayList(
                        "_id"
                ));


        RewardedData data = new RewardedData();
        data.setTotalRecordCount(records.size());

        List<String> recordIds = records.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<IObjectData> recordDetails = queryRecordDetails(tenant, recordIds);

        data.setTotalCount(recordDetails.size());
        data.setSkuCount(Maps.newHashMap());

        for (IObjectData recordDetail : recordDetails) {
            String productId = recordDetail.get(RedPacketRecordDetailObjFields.PRODUCT_ID, String.class);
            if (!Strings.isNullOrEmpty(productId)) {
                if (data.getSkuCount().containsKey(productId)) {
                    data.getSkuCount().put(productId, data.getSkuCount().get(productId) + 1);
                } else {
                    data.getSkuCount().put(productId, 1);
                }
            }
        }

        return data;
    }

    private List<IObjectData> queryRecordDetails(MengNiuTenantInformation tenant, List<String> recordIds) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return Lists.newArrayList();
        }

        IFilter masterRecordIdFilter = new Filter();
        masterRecordIdFilter.setFieldName(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID);
        masterRecordIdFilter.setOperator(Operator.IN);
        masterRecordIdFilter.setFieldValues(recordIds);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(masterRecordIdFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                ApiNames.RED_PACKET_RECORD_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        "_id", RedPacketRecordDetailObjFields.PRODUCT_ID
                ));
    }

    private List<RedPacketRewardDetail> buildupRedPacketRewardDetails(
            MengNiuTenantInformation tenant,
            FullStoreStockCheckEventInformation fullEvent,
            String role) {

        String defaultAmountKey = role.toLowerCase() + "_red_packet_amount__c";
        BigDecimal defaultAmount = fullEvent.getActivity().get(defaultAmountKey, BigDecimal.class);
        String storeId = fullEvent.getStoreStockCheck().get("account_id__c", String.class);

        List<FullStoreStockCheckDetailInformation> limitedDetails = calculateLimitedDetails(tenant, fullEvent, role);

        List<RedPacketRewardDetail> details = Lists.newArrayList();
        for (FullStoreStockCheckDetailInformation detail : limitedDetails) {
            BigDecimal amount = defaultAmount;
            RewardAmountConfig amountConfig = rewardAmountConfigService.get(
                    tenant.getManufacturer().getTenantId(),
                    fullEvent.getActivity().getId(),
                    role,
                    storeId,
                    detail.getSkuId());
            if (!Objects.isNull(amountConfig)) {
                amount = amountConfig.getAmount();
            }

            RedPacketRewardDetail recordDetail = new RedPacketRewardDetail();
            recordDetail.setSerialNumberId(detail.getSnId());
            recordDetail.setProductId(detail.getSkuId());
            recordDetail.setSerialNumberName(detail.getSnObj().getName());
            recordDetail.setManufactureDate(detail.getSnObj().get("manufacture_date", Long.class));
            recordDetail.setBatchCode(detail.getSnObj().get("batch_code", String.class));
            recordDetail.setSalesOrderDetailId(detail.getId());
            recordDetail.setSalesOrderDetailName(detail.getName());
            recordDetail.setAmount(amount);
            recordDetail.setAmountConfigId(amountConfig == null ? null : amountConfig.getId());
            details.add(recordDetail);
        }
        return details;
    }

    private RedPacketReward calculateMBossReward(
            MengNiuTenantInformation tenant,
            SalesEvent<StoreStockCheckEventData> event,
            FullStoreStockCheckEventInformation fullEvent) {
        if (!tenant.getRole().equals(MengNiuTenantInformation.ROLE_M)) {
            return null;
        }

        List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(tenant, fullEvent, RewardConstants.ROLE_M_BOSS);
        PaymentAccount from = loadTenantCloudAccountByRole(tenant, fullEvent.getActivity(), RewardConstants.ROLE_M_BOSS);
        String mBossEmployeeId = findMBossEmployeeId(tenant.getM().getTenantId());
        WeChatPaymentAccount account;
        if (Strings.isNullOrEmpty(mBossEmployeeId)) {
            account = WeChatPaymentAccount.builder().build();
        } else {
            account = loadWeChatPaymentAccountFromEmployee(fullEvent.getStoreStockCheck().getTenantId(), mBossEmployeeId);
        }
        PaymentAccount to = PaymentAccount.of(
                ApiNames.PERSONNEL_OBJ,
                String.format("%s.%s", fullEvent.getStoreStockCheck().getTenantId(), mBossEmployeeId),
                account
        );

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_M_BOSS.toLowerCase());
        String publishMode = fullEvent.getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);
        // 取活动上的有效天数
        Long expirationDate = getExpirationDate(fullEvent, publishMode, RewardConstants.ROLE_M_BOSS.toLowerCase());

        return RedPacketReward.of(tenant, event, fullEvent, RewardConstants.ROLE_M_BOSS, from, to, details, publishMode, expirationDate);
    }

    @Override
    protected void validateEventData(StoreStockCheckEventData data) {
        if (Strings.isNullOrEmpty(data.getStoreStockCheckId())) {
            throw new EventAbandonException("store stock check id empty.");
        }
    }
}