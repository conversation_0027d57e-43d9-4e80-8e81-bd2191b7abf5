package com.facishare.crm.fmcg.mengniu.api;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/8 16:17
 */
public interface MengNiuRewardRetry {

    @Data
    @ToString
    class Arg implements Serializable {

        private String tenantId;

        private String id;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private boolean success;

        private int code;

        private String message;
    }
}