package com.facishare.crm.fmcg.mengniu.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class PaymentAccount implements Serializable {

    /**
     * WeChat
     * Cloud
     */
    private String accountType;

    private TenantCloudPaymentAccount tenantCloudPaymentAccount;

    private TenantWeChatPaymentAccount tenantWeChatPaymentAccount;

    private WeChatPaymentAccount weChatPaymentAccount;

    private String rewardPersonId;

    private String rewardPersonType;

    public static PaymentAccount of(TenantWeChatPaymentAccount tenantWeChatPaymentAccount) {
        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setAccountType("TenantWeChat");
        paymentAccount.setTenantWeChatPaymentAccount(tenantWeChatPaymentAccount);
        return paymentAccount;
    }

    public static PaymentAccount of(WeChatPaymentAccount weChatPaymentAccount) {
        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setAccountType("WeChat");
        paymentAccount.setWeChatPaymentAccount(weChatPaymentAccount);
        return paymentAccount;
    }

    public static PaymentAccount of(String rewardPersonType, String rewardPersonId, WeChatPaymentAccount weChatPaymentAccount) {
        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setAccountType("WeChat");
        paymentAccount.setWeChatPaymentAccount(weChatPaymentAccount);
        paymentAccount.setRewardPersonType(rewardPersonType);
        paymentAccount.setRewardPersonId(rewardPersonId);
        return paymentAccount;
    }

    public static PaymentAccount of(TenantCloudPaymentAccount tenantCloudPaymentAccount) {
        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setAccountType("TenantCloud");
        paymentAccount.setTenantCloudPaymentAccount(tenantCloudPaymentAccount);
        return paymentAccount;
    }
}