package com.facishare.crm.fmcg.dms.web.service;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.model.PaymentCalculateMaxAmount;
import com.facishare.crm.fmcg.dms.web.abstraction.BaseService;
import com.facishare.crm.fmcg.dms.web.abstraction.IRedPaymentService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@SuppressWarnings("Duplicates")
public class RedPaymentService extends BaseService implements IRedPaymentService {
    @Override
    public PaymentCalculateMaxAmount.Result calculateMaxAmount(PaymentCalculateMaxAmount.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        IObjectData returnGoodsInvoice = serviceFacade.findObjectDataIgnoreAll(User.systemUser(context.getTenantId()), arg.getReturnGoodsInvoiceId(), ApiNames.RETURNED_GOODS_INVOICE_OBJ);
        BigDecimal returnGoodsInvAmount = returnGoodsInvoice.get(ReturnedGoodsInvoiceFields.RETURNED_GOODS_INV_AMOUNT, BigDecimal.class, new BigDecimal("0")).abs();

        BigDecimal carSalesReturnedAmount = calculateRefundAmount(context.getTenantId(), arg.getReturnGoodsInvoiceId());

        List<String> accountReceivableDetailIds = accountReceivableDetailIds(context.getTenantId(), arg.getReturnGoodsInvoiceId());
        BigDecimal totalSettledAmount = calculateTotalSettledAmount(context.getTenantId(), accountReceivableDetailIds);


        BigDecimal amount;
        if (carSalesReturnedAmount.compareTo(totalSettledAmount) < 0) {
            amount = returnGoodsInvAmount.subtract(totalSettledAmount);
        } else {
            amount = returnGoodsInvAmount.subtract(carSalesReturnedAmount);
        }

        return PaymentCalculateMaxAmount.Result.builder().amount(amount.negate()).build();

    }

    private BigDecimal calculateRefundAmount(String tenantId, String returnGoodsInvoiceId) {

        if (StringUtils.isEmpty(returnGoodsInvoiceId)) {
            return new BigDecimal("0");
        }
        BigDecimal total = new BigDecimal("0");

        Filter arDetailIdFilter = new Filter();
        arDetailIdFilter.setFieldName(OrderPaymentFields.RETURNED_GOODS_INVOICE_ID);
        arDetailIdFilter.setOperator(Operator.EQ);
        arDetailIdFilter.setFieldValues(Lists.newArrayList(returnGoodsInvoiceId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(arDetailIdFilter);
        List<IObjectData> details = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ORDER_PAYMENT_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, OrderPaymentFields.PAYMENT_AMOUNT)
        );

        for (IObjectData detail : details) {
            BigDecimal amount = detail.get(OrderPaymentFields.PAYMENT_AMOUNT, BigDecimal.class);
            if (Objects.nonNull(amount)) {
                total = total.add(amount);
            }

        }
        return total.abs();
    }

    private BigDecimal calculateTotalSettledAmount(String tenantId, List<String> accountReceivableDetailIds) {

        BigDecimal creditSettledAmount = calculateCreditSettledAmount(tenantId, accountReceivableDetailIds);
        BigDecimal debitSettledAmount = calculateDebitSettledAmount(tenantId, accountReceivableDetailIds);

        return creditSettledAmount.add(debitSettledAmount).abs();

    }

    private List<String> accountReceivableDetailIds(String tenantId, String returnGoodsInvoiceId) {
        if (StringUtils.isEmpty(returnGoodsInvoiceId)) {
            return Lists.newArrayList();
        }
        Filter sourceDataIdFilter = new Filter();
        sourceDataIdFilter.setFieldName(AccountsReceivableDetailFields.SOURCE_DATA_ID);
        sourceDataIdFilter.setOperator(Operator.EQ);
        sourceDataIdFilter.setFieldValues(Lists.newArrayList(returnGoodsInvoiceId));

        Filter sourceApiNameFilter = new Filter();
        sourceApiNameFilter.setFieldName(AccountsReceivableDetailFields.SOURCE_API_NAME);
        sourceApiNameFilter.setOperator(Operator.EQ);
        sourceApiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.RETURNED_GOODS_INVOICE_OBJ));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(sourceDataIdFilter, sourceApiNameFilter);
        List<IObjectData> details = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID)
        );
        return details.stream().map(IObjectData::getId).collect(Collectors.toList());
    }

    //收付款对象已结算金额
    protected BigDecimal calculateCreditSettledAmount(String tenantId, List<String> accountReceivableNoteDetailIds) {
        List<List<String>> partition = Lists.partition(accountReceivableNoteDetailIds, 200);
        BigDecimal amount = new BigDecimal("0");
        for (List<String> accountReceivableNoteDetailIdsGroup : partition) {

            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setSearchSource("db");
            query.setLimit(200);
            query.setOffset(0);

            Filter debitDataIdFilter = new Filter();
            debitDataIdFilter.setFieldName(MatchNoteDetailFields.CREDIT_DETAIL_DATA_ID);
            debitDataIdFilter.setOperator(Operator.IN);
            debitDataIdFilter.setFieldValues(accountReceivableNoteDetailIdsGroup);

            Filter debitApiNameFilter = new Filter();
            debitApiNameFilter.setFieldName(MatchNoteDetailFields.CREDIT_DETAIL_API_NAME);
            debitApiNameFilter.setOperator(Operator.EQ);
            debitApiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ));

            Filter isDeletedFilter = new Filter();
            isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
            isDeletedFilter.setOperator(Operator.EQ);
            isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

            Filter lifeStatusFilter = new Filter();
            lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
            lifeStatusFilter.setOperator(Operator.EQ);
            lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));


            query.setFilters(Lists.newArrayList(debitDataIdFilter, debitApiNameFilter, isDeletedFilter, lifeStatusFilter));

            List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                    User.systemUser(tenantId),
                    query,
                    ApiNames.MATCH_NOTE_DETAIL_OBJ,
                    Lists.newArrayList(MatchNoteDetailFields.CREDIT_DETAIL_DATA_ID, MatchNoteDetailFields.CREDIT_DETAIL_API_NAME),
                    "sum",
                    MatchNoteDetailFields.CREDIT_MATCH_AMOUNT
            );

            if (!CollectionUtils.isEmpty(data)) {
                for (IObjectData datum : data) {
                    BigDecimal sumAmount = datum.get("sum_" + MatchNoteDetailFields.CREDIT_MATCH_AMOUNT, BigDecimal.class);
                    if (!Objects.isNull(sumAmount)) {
                        amount = amount.add(sumAmount);
                    }
                }

            }
        }
        return amount;
    }

    //核销对象已结算金额
    protected BigDecimal calculateDebitSettledAmount(String tenantId, List<String> accountReceivableNoteDetailIds) {
        List<List<String>> partition = Lists.partition(accountReceivableNoteDetailIds, 200);
        BigDecimal amount = new BigDecimal("0");
        for (List<String> accountReceivableNoteDetailIdsGroup : partition) {

            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setSearchSource("db");
            query.setLimit(200);
            query.setOffset(0);

            Filter debitDataIdFilter = new Filter();
            debitDataIdFilter.setFieldName(MatchNoteDetailFields.DEBIT_DETAIL_DATA_ID);
            debitDataIdFilter.setOperator(Operator.IN);
            debitDataIdFilter.setFieldValues(accountReceivableNoteDetailIdsGroup);

            Filter debitApiNameFilter = new Filter();
            debitApiNameFilter.setFieldName(MatchNoteDetailFields.DEBIT_DETAIL_API_NAME);
            debitApiNameFilter.setOperator(Operator.EQ);
            debitApiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ));

            Filter isDeletedFilter = new Filter();
            isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
            isDeletedFilter.setOperator(Operator.EQ);
            isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

            Filter lifeStatusFilter = new Filter();
            lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
            lifeStatusFilter.setOperator(Operator.EQ);
            lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));


            query.setFilters(Lists.newArrayList(debitDataIdFilter, debitApiNameFilter, isDeletedFilter, lifeStatusFilter));

            List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                    User.systemUser(tenantId),
                    query,
                    ApiNames.MATCH_NOTE_DETAIL_OBJ,
                    Lists.newArrayList(MatchNoteDetailFields.DEBIT_DETAIL_DATA_ID, MatchNoteDetailFields.DEBIT_DETAIL_API_NAME),
                    "sum",
                    MatchNoteDetailFields.THIS_MATCH_AMOUNT
            );

            if (!CollectionUtils.isEmpty(data)) {
                for (IObjectData datum : data) {
                    BigDecimal sumAmount = datum.get("sum_" + MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class);
                    if (!Objects.isNull(sumAmount)) {
                        amount = amount.add(sumAmount);
                    }
                }

            }
        }
        return amount;
    }
}
