package com.facishare.crm.fmcg.dms.web.aop;

import com.alibaba.fastjson.JSON;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;


@Aspect
@Component("dmsServiceAspect")
@Slf4j
public class ServiceAspect {

    @Around(value = "execution(* com.facishare.crm.fmcg.dms.web.*.*.*(..))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        String method = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();

        try {
            ApiContext context = ApiContextManager.getContext();
            String json = JSON.toJSONString(args);
            log.info("web request context - {}, method - {}, args - {}", context, method, json.length() > 2000 ? json.substring(0, 2000) : json);
        } catch (Exception ex) {
            log.info("try log parameters error : ", ex);
        }

        Object result;

        try {
            result = joinPoint.proceed(args);
        } catch (AppBusinessException ex) {
            log.info(String.format("[AppBusinessException] - method - %s : ", joinPoint.getSignature().getName()), ex);
            throw new BizException(ex.getMessage(), ex.getErrorCode());
        } catch (Exception ex) {
            log.info(String.format("[UnknownException] - method - %s : ", joinPoint.getSignature().getName()), ex);
            throw ex;
        }

        try {
            log.info("method - {}, result - {}", method, JSON.toJSONString(result));
        } catch (Exception ex) {
            log.info("try log result error : ", ex);
        }

        return result;
    }

}
