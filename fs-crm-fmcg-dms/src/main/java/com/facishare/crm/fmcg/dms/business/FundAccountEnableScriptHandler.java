package com.facishare.crm.fmcg.dms.business;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.OpeningBalanceSettingFields;
import com.facishare.crm.fmcg.dms.business.abstraction.BaseHandler;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class FundAccountEnableScriptHandler extends BaseHandler implements IDMSScriptHandler {


    @Override
    public boolean checkHandler(String handlerName) {
        return ScriptHandlerNameEnum.FUND_ACCOUNT_ENABLE.getHandlerName().equals(handlerName);
    }


    @Override
    public void addFields(String tenantId, Map<String, IObjectDescribe> describes) {

        //入账账户
        try {
            fieldBusiness.addField(tenantId, describes.get(ApiNames.OPENING_BALANCE_SETTING_OBJ), ApiNames.OPENING_BALANCE_SETTING_OBJ,
                    OpeningBalanceSettingFields.FUND_ACCOUNT_ID, true, false);
        } catch (Exception ex) {
            log.error("新增期初设置.入账账户信息失败", ex);
        }


    }


}
