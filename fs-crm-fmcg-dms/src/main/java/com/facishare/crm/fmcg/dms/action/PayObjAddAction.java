package com.facishare.crm.fmcg.dms.action;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.dms.business.abstraction.IAmountCalculateService;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.crm.fmcg.dms.service.DMSBuryService;
import com.facishare.crm.fmcg.dms.service.abastraction.DMSBuryModule;
import com.facishare.crm.fmcg.dms.service.abastraction.DMSBuryOperation;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
public class PayObjAddAction extends StandardAddAction {
    private final IAmountCalculateService amountCalculateService = SpringUtil.getContext().getBean(IAmountCalculateService.class);

    private Map<String, List<IObjectData>> detailsGroupByPurchaseOrderId;
    private Map<String, IObjectData> purchaseOrderObjGroupByPurchaseOrderId;

    private Map<String, List<IObjectData>> detailsGroupByPurchaseReturnNoteId;
    private Map<String, IObjectData> purchaseOrderObjGroupByPurchaseReturnNoteId;

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        validateSupplier();

        validatePayAmount();

        validateDetails();

        validatePurchaseAmount();

        validatePurchaseReturnAmount();

        validateAvailableAmount();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        
        DMSBuryService.asyncTpmLog(
                Integer.valueOf(actionContext.getTenantId()),
                actionContext.getUser().getUserIdInt(),
                DMSBuryModule.DMSBuryModuleName.PAY_AMOUNT_CREATE_NUM,
                DMSBuryOperation.CREATE
        );

        return super.after(arg, result);
    }

    private void validateSupplier() {

        IObjectData data = arg.getObjectData().toObjectData();
        if (PayFields.RECORD_TYPE_NO_DETAILS.equals(data.getRecordType())) {
            return;
        }
        String masterSupplierId = data.get(PayFields.SUPPLIER_ID, String.class);

        String payType = data.get(PayFields.PAY_TYPE, String.class);

        List<ObjectDataDocument> detailDocuments = arg.getDetails().get(ApiNames.PAY_DETAIL_OBJ);
        if (CollectionUtils.isEmpty(detailDocuments)) {
            throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_0));
        }
        List<IObjectData> details = detailDocuments.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        List<IObjectData> orders;
        if (PayFields.PAY_TYPE__BLUE.equals(payType)) {
            List<String> purchaseOrderIds = details.stream().map(v -> v.get(PayDetailFields.PURCHASE_ORDER_ID, String.class)).collect(Collectors.toList());
            orders = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), purchaseOrderIds, ApiNames.PURCHASE_ORDER_OBJ);
        } else if (PayFields.PAY_TYPE__RED.equals(payType)) {
            List<String> purchaseReturnNoteIds = details.stream().map(v -> v.get(PayDetailFields.PURCHASE_RETURN_NOTE_ID, String.class)).collect(Collectors.toList());
            orders = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), purchaseReturnNoteIds, ApiNames.PURCHASE_RETURN_NOTE_OBJ);
        } else {
            throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_1));
        }

        for (IObjectData order : orders) {
            String orderSupplierId = order.get(PayDetailFields.SUPPLIER_ID, String.class);
            if (!Objects.equals(masterSupplierId, orderSupplierId)) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_2), PayFields.PAY_TYPE__BLUE.equals(payType) ? I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_3) : I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_4)));
            }
        }

    }


    private void validatePayAmount() {
        IObjectData data = arg.getObjectData().toObjectData();
        String payType = data.get(PayFields.PAY_TYPE, String.class);
        BigDecimal payAmount = data.get(PayFields.PAY_AMOUNT, BigDecimal.class);
        if (BigDecimal.ZERO.compareTo(payAmount) == 0) {
            throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_5));
        }
        if (PayFields.PAY_TYPE__BLUE.equals(payType)) {
            if (BigDecimal.ZERO.compareTo(payAmount) > 0) {
                throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_6));
            }
        } else if (PayFields.PAY_TYPE__RED.equals(payType)) {
            if (BigDecimal.ZERO.compareTo(payAmount) < 0) {
                throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_7));
            }

        } else {
            throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_8));
        }
    }

    private void validateDetails() {
        IObjectData data = arg.getObjectData().toObjectData();
        if (PayFields.RECORD_TYPE_NO_DETAILS.equals(data.getRecordType())) {
            return;
        }
        List<ObjectDataDocument> detailDocuments = arg.getDetails().get(ApiNames.PAY_DETAIL_OBJ);
        if (CollectionUtils.isEmpty(detailDocuments)) {
            throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_9));
        }
        List<IObjectData> details = detailDocuments.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        String payType = data.get(PayFields.PAY_TYPE, String.class);

        if (Objects.equals(PayFields.PAY_TYPE__BLUE, payType)) {
            for (IObjectData detail : details) {
                if (BigDecimal.ZERO.compareTo(detail.get(PayDetailFields.PAY_AMOUNT, BigDecimal.class)) == 0) {
                    throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_10));
                }
                if (StringUtils.isNotEmpty(detail.get(PayDetailFields.PURCHASE_RETURN_NOTE_ID, String.class))) {
                    throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_11));
                }
                if (StringUtils.isEmpty(detail.get(PayDetailFields.PURCHASE_ORDER_ID, String.class))) {
                    throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_12));
                }
            }
        } else if (Objects.equals(PayFields.PAY_TYPE__RED, payType)) {
            for (IObjectData detail : details) {
                if (BigDecimal.ZERO.compareTo(detail.get(PayDetailFields.PAY_AMOUNT, BigDecimal.class)) == 0) {
                    throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_13));
                }
                if (StringUtils.isNotEmpty(detail.get(PayDetailFields.PURCHASE_ORDER_ID, String.class))) {
                    throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_14));
                }
                if (StringUtils.isEmpty(detail.get(PayDetailFields.PURCHASE_RETURN_NOTE_ID, String.class))) {
                    throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_15));
                }
            }
        } else {
            throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_16));
        }


        detailsGroupByPurchaseOrderId = details.stream()
                .filter(objectData -> StringUtils.isNotEmpty(objectData.get(PayDetailFields.PURCHASE_ORDER_ID, String.class)))
                .collect(Collectors.groupingBy(objectData -> objectData.get(PayDetailFields.PURCHASE_ORDER_ID, String.class)));
        List<IObjectData> purchaseOrderObjs = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(detailsGroupByPurchaseOrderId.keySet()), ApiNames.PURCHASE_ORDER_OBJ);
        purchaseOrderObjGroupByPurchaseOrderId = purchaseOrderObjs.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (oldData, newData) -> newData));

        detailsGroupByPurchaseReturnNoteId = details.stream()
                .filter(objectData -> StringUtils.isNotEmpty(objectData.get(PayDetailFields.PURCHASE_RETURN_NOTE_ID, String.class)))
                .collect(Collectors.groupingBy(objectData -> objectData.get(PayDetailFields.PURCHASE_RETURN_NOTE_ID, String.class)));
        List<IObjectData> purchaseReturnNoteObjs = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(detailsGroupByPurchaseReturnNoteId.keySet()), ApiNames.PURCHASE_RETURN_NOTE_OBJ);
        purchaseOrderObjGroupByPurchaseReturnNoteId = purchaseReturnNoteObjs.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (oldData, newData) -> newData));

    }


    //校验若付款金额+MAX（采购单.已付款金额，采购单.付款核销金额）> 采购金额，否则提示“付款金额超过待付款金额”。
    private void validatePurchaseAmount() {
        IObjectData data = arg.getObjectData().toObjectData();
        if (PayFields.RECORD_TYPE_NO_DETAILS.equals(data.getRecordType())) {
            return;
        }
        String payType = data.get(PayFields.PAY_TYPE, String.class);
        if (!Objects.equals(PayFields.PAY_TYPE__BLUE, payType)) {
            return;
        }
        for (Map.Entry<String, List<IObjectData>> entry : detailsGroupByPurchaseOrderId.entrySet()) {
            String purchaseOrderId = entry.getKey();
            List<IObjectData> payDetails = entry.getValue();

            BigDecimal payAmountTotal = new BigDecimal("0");
            for (IObjectData payDetail : payDetails) {
                payAmountTotal = payAmountTotal.add(payDetail.get(PayDetailFields.PAY_AMOUNT, BigDecimal.class));
            }

            BigDecimal purchasePayTotalAmount = amountCalculateService.calculateObjPayTotalAmount(actionContext.getTenantId(), PayDetailFields.PURCHASE_ORDER_ID, purchaseOrderId);
            BigDecimal purchasePayMatchTotalAmount = amountCalculateService.calculateObjPayMatchTotalAmount(actionContext.getTenantId(), ApiNames.PURCHASE_ORDER_OBJ, purchaseOrderId);

            IObjectData purchaseOrderObj = purchaseOrderObjGroupByPurchaseOrderId.get(purchaseOrderId);
            BigDecimal purchaseTotalAmount = purchaseOrderObj.get(PurchaseOrderObjFields.TOTAL_MONEY, BigDecimal.class);

            if (purchaseTotalAmount == null) {
                purchaseTotalAmount = amountCalculateService.calculateTotalMoneyWithPurchaseOrderDetails(actionContext.getTenantId(), purchaseOrderId);
                if (purchaseTotalAmount == null) {
                    purchaseTotalAmount = new BigDecimal("0");
                }
            }

            BigDecimal purchaseMaxAmount;
            if (purchasePayTotalAmount.compareTo(purchasePayMatchTotalAmount) < 0) {
                purchaseMaxAmount = purchasePayMatchTotalAmount;
            } else {
                purchaseMaxAmount = purchasePayTotalAmount;
            }

            if (purchaseTotalAmount.compareTo(payAmountTotal.add(purchaseMaxAmount)) < 0) {
                throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_17));
            }
        }
    }

    private void validatePurchaseReturnAmount() {
        IObjectData data = arg.getObjectData().toObjectData();
        if (PayFields.RECORD_TYPE_NO_DETAILS.equals(data.getRecordType())) {
            return;
        }
        String payType = data.get(PayFields.PAY_TYPE, String.class);
        if (!Objects.equals(PayFields.PAY_TYPE__RED, payType)) {
            return;
        }

        for (Map.Entry<String, List<IObjectData>> entry : detailsGroupByPurchaseReturnNoteId.entrySet()) {
            String purchaseReturnNoteId = entry.getKey();
            List<IObjectData> payDetails = entry.getValue();

            BigDecimal payAmountTotal = new BigDecimal("0");
            for (IObjectData payDetail : payDetails) {
                payAmountTotal = payAmountTotal.add(payDetail.get(PayDetailFields.PAY_AMOUNT, BigDecimal.class).abs());
            }

            BigDecimal purchaseReturnPayTotalAmount = amountCalculateService.calculateObjPayTotalAmount(actionContext.getTenantId(), PayDetailFields.PURCHASE_RETURN_NOTE_ID, purchaseReturnNoteId);
            BigDecimal purchaseReturnPayMatchTotalAmount = amountCalculateService.calculateObjPayMatchTotalAmount(actionContext.getTenantId(), ApiNames.PURCHASE_RETURN_NOTE_OBJ, purchaseReturnNoteId);

            IObjectData purchaseReturnNote = purchaseOrderObjGroupByPurchaseReturnNoteId.get(purchaseReturnNoteId);
            BigDecimal refundAmount = purchaseReturnNote.get(PurchaseReturnNoteObjFields.REFUND_AMOUNT, BigDecimal.class);

            if (refundAmount == null) {
                refundAmount = amountCalculateService.calculateTotalMoneyWithPurchaseReturnDetails(actionContext.getTenantId(), purchaseReturnNoteId);
                if (refundAmount == null) {
                    refundAmount = new BigDecimal("0");
                }
            }

            BigDecimal purchaseMaxAmount;
            if (purchaseReturnPayTotalAmount.compareTo(purchaseReturnPayMatchTotalAmount) < 0) {
                purchaseMaxAmount = purchaseReturnPayMatchTotalAmount;
            } else {
                purchaseMaxAmount = purchaseReturnPayTotalAmount;
            }

            if (refundAmount.abs().compareTo(payAmountTotal.add(purchaseMaxAmount)) < 0) {
                throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_18));
            }
        }
    }

    private void validateAvailableAmount() {
        IObjectData data = arg.getObjectData().toObjectData();
        if (PayFields.RECORD_TYPE_NO_DETAILS.equals(data.getRecordType())) {
            return;
        }
        List<IObjectData> details = arg.getDetails().get(ApiNames.PAY_DETAIL_OBJ).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        BigDecimal detailsPayAmount = new BigDecimal("0");
        for (IObjectData detail : details) {
            detailsPayAmount = detailsPayAmount.add(detail.get(PayDetailFields.PAY_AMOUNT, BigDecimal.class).abs());
        }

        BigDecimal payAmount = data.get(PayFields.PAY_AMOUNT, BigDecimal.class).abs();

        if (payAmount.compareTo(detailsPayAmount) < 0) {
            throw new ValidateException(I18N.text(I18NKeys.PAY_OBJ_ADD_ACTION_19));
        }

    }
}
