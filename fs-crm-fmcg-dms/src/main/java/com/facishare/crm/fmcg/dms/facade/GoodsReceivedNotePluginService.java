package com.facishare.crm.fmcg.dms.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.AccountsPayableDetailFields;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("Duplicates")
@Service
@ServiceModule("goods_receivable_note_action_plugin")
public class GoodsReceivedNotePluginService extends PluginBaseService {


    @ServiceMethod("invalid_after")
    public InvalidActionDomainPlugin.Result invalidAfter(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        IObjectData data = arg.getObjectData().toObjectData();
        String goodsReceivableNoteId = data.getId();
        log.info("goods_receivable_note_action_plugin invalid_after :{}:{}", data.getName(), data.getId());

        List<IObjectData> accountsPayableDetails = accountsPayableDetails(serviceContext.getTenantId(), AccountsPayableDetailFields.GOODS_RECEIVED_NOTE_ID, Lists.newArrayList(goodsReceivableNoteId));

        invalidPayable(serviceContext, accountsPayableDetails);
        invalidMatchNoteWithPayableDetails(serviceContext, accountsPayableDetails);
        return new InvalidActionDomainPlugin.Result();
    }


    @ServiceMethod("bulk_invalid_after")
    public BulkInvalidActionDomainPlugin.Result bulkInvalidAfter(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        List<String> goodsReceivableNoteIds = arg.getObjectDataList().stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
        log.info("goods_receivable_note_action_plugin bulk_invalid_after :{}:{}", goodsReceivableNoteIds, JSON.toJSONString(arg.getObjectDataList().stream().map(v -> v.get(CommonFields.NAME)).collect(Collectors.toList())));

        List<IObjectData> accountsPayableDetails = accountsPayableDetails(serviceContext.getTenantId(), AccountsPayableDetailFields.GOODS_RECEIVED_NOTE_ID, goodsReceivableNoteIds);

        invalidPayable(serviceContext, accountsPayableDetails);
        invalidMatchNoteWithPayableDetails(serviceContext, accountsPayableDetails);

        return new BulkInvalidActionDomainPlugin.Result();
    }


}
