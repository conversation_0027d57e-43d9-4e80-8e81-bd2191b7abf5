package com.facishare.crm.fmcg.dms.service.converter.strategy;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.AutoReceivableConvertService;
import com.facishare.crm.fmcg.dms.service.abastraction.BaseStrategy;
import com.facishare.crm.fmcg.dms.service.abastraction.IOutboundDeliveryNoteRecordTypeProcessStrategy;
import com.facishare.crm.fmcg.dms.service.converter.OutboundDeliveryNoteAutoReceivableConvertToReceivableService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Component("outboundDeliveryNoteExchangeRecordTypeStrategy")
public class OutboundDeliveryNoteExchangeRecordTypeStrategy extends BaseStrategy implements IOutboundDeliveryNoteRecordTypeProcessStrategy {

    @Override
    public void beforeConvert(FinancialBill bill) {
        bill.setData(serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.OUTBOUND_DELIVERY_NOTE_OBJ));
        bill.setDetails(queryOutboundDeliveryNoteDetails(bill.getTenantId(), bill.getData().getId()));

        String returnGoodsInvoiceId = bill.getData().get(OutboundDeliveryNoteFields.RETURNED_GOODS_INVOICE_ID, String.class);
        if (Strings.isNullOrEmpty(returnGoodsInvoiceId)) {
            throw new AbandonActionException("returnGoodsInvoiceId can not be null or empty.");
        }

        IObjectData returnGoodsInvoice = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), returnGoodsInvoiceId, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
        List<IObjectData> details = queryReturnGoodsInvoiceDetails(bill.getTenantId(), returnGoodsInvoiceId);

        bill.setRelatedBill(FinancialBill.builder()
                .tenantId(bill.getTenantId())
                .apiName(ApiNames.RETURNED_GOODS_INVOICE_OBJ)
                .data(returnGoodsInvoice)
                .details(details)
                .build());
    }

    @Override
    public void validate(FinancialBill bill) {
        String returnGoodsInvoiceId = bill.getRelatedBill().getData().getId();
        boolean b;
        if (!TPMGrayUtils.accountReceivableDetailUseWhatField(bill.getTenantId())) {
            b = existsReceivableByUniqueIdWithExchange(bill.getTenantId(), returnGoodsInvoiceId, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
        } else {
            b = existsReceivableByUniqueId(bill.getTenantId(), returnGoodsInvoiceId, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
        }
        if (b) {
            throw new AbandonActionException(String.format("returnGoodsInvoiceId:[%s] is already create AccountsReceivable.", returnGoodsInvoiceId));
        }
    }

    @Override
    public IObjectData convertToMaster(FinancialBill bill) {
        return innerConvertToMaster(bill);
    }

    @Override
    public List<IObjectData> convertToDetails(FinancialBill bill) {
        return innerConvertToDetails(bill);
    }

    private IObjectData innerConvertToMaster(FinancialBill bill) {
        IObjectData returnGoodsInvoiceObj = bill.getRelatedBill().getData();
        IObjectData data = new ObjectData();

        data.setTenantId(bill.getTenantId());
        data.setDescribeApiName(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ);
        data.set(CommonFields.CREATE_BY, bill.getData().get(CommonFields.CREATE_BY));
        data.set(CommonFields.OWNER, bill.getData().get(CommonFields.OWNER));
        data.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);

        String accountId = returnGoodsInvoiceObj.get(ReturnedGoodsInvoiceFields.ACCOUNT_ID, String.class);
        data.set(AccountsReceivableNoteFields.ACCOUNT_ID, accountId);
        data.set(AccountsReceivableNoteFields.CONTACT_OBJECT, AccountsReceivableNoteFields.CONTACT_OBJECT__AccountObj);

        data.set(AccountsReceivableNoteFields.OPENING_BALANCE, false);

        long now = System.currentTimeMillis();
        data.set(AccountsReceivableNoteFields.NOTE_DATE, now);
        Long returnGoodsDate = returnGoodsInvoiceObj.get(ReturnedGoodsInvoiceFields.RETURNED_GOODS_TIME, Long.class);
        Long dueDate = calculateDueDate(bill.getTenantId(), returnGoodsDate, accountId, now);
        data.set(AccountsReceivableNoteFields.DUE_DATE, dueDate);
        Long nowDayBeginTime = OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.getDayBeginTimestamp(System.currentTimeMillis());
        data.set(AccountsReceivableNoteFields.IS_OVERDUE, nowDayBeginTime > dueDate);
        data.set(AccountsReceivableNoteFields.SYSTEM_SOURCE, AccountsReceivableNoteFields.SYSTEM_SOURCE__saleSystem);
        data.set(AccountsReceivableNoteFields.SETTLED_STATUS, AccountsReceivableNoteFields.SETTLED_STATUS__no);

        BigDecimal total = new BigDecimal("0");
        for (IObjectData detail : bill.getRelatedBill().getDetails()) {
            BigDecimal subtotal = detail.get(ReturnedGoodsInvoiceProductFields.SUBTOTAL, BigDecimal.class);
            if (Objects.isNull(subtotal)) {
                continue;
            }
            total = total.add(subtotal);
        }
        if (AutoReceivableConvertService.zero(total)) {
            throw new AbandonActionException("outboundDeliveryNote subtotal is zero.");
        }
        data.set(AccountsReceivableNoteFields.PRICE_TAX_TOTAL_AMOUNT, total);

        data.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE, AccountsReceivableNoteFields.OUTBOUND_DELIVERY_NOTE_OBJ);
        data.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_API_NAME, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
        data.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID, returnGoodsInvoiceObj.getId());

        return data;
    }


    private List<IObjectData> innerConvertToDetails(FinancialBill bill) {
        List<IObjectData> result = Lists.newArrayList();
        for (IObjectData detail : bill.getRelatedBill().getDetails()) {
            IObjectData data = new ObjectData();

            data.setTenantId(bill.getTenantId());
            data.setDescribeApiName(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ);
            data.set(CommonFields.CREATE_BY, detail.get(CommonFields.CREATE_BY));
            data.set(CommonFields.OWNER, detail.get(CommonFields.OWNER));
            data.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);
            data.set(CommonFields.LIFE_STATUS, CommonFields.LIFE_STATUS__NORMAL);
            BigDecimal productMoney = detail.get(ReturnedGoodsInvoiceProductFields.SUBTOTAL, BigDecimal.class);
            if (Objects.isNull(productMoney) || AutoReceivableConvertService.zero(productMoney)) {
                continue;
            }
            data.set(AccountsReceivableDetailFields.PRICE_TAX_AMOUNT, productMoney);
            data.set(AccountsReceivableDetailFields.AR_QUANTITY, detail.get(ReturnedGoodsInvoiceProductFields.AUXILIARY_QUANTITY));

            data.set(AccountsReceivableDetailFields.TAX_PRICE, productMoney);
            data.set(AccountsReceivableDetailFields.SKU_ID, detail.get(ReturnedGoodsInvoiceProductFields.PRODUCT_ID));
            if (TPMGrayUtils.accountReceivableDetailUseWhatField(bill.getTenantId())) {
                data.set(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID, bill.getRelatedBill().getData().getId());
                data.set(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_API_NAME, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
                data.set(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_DATA_ID, detail.getId());
                data.set(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_API_NAME, ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ);
            }

            data.set(AccountsReceivableDetailFields.SOURCE_DETAIL_DATA_ID, detail.getId());
            data.set(AccountsReceivableDetailFields.SOURCE_DETAIL_API_NAME, ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ);
            data.set(AccountsReceivableDetailFields.SOURCE_DATA_ID, bill.getRelatedBill().getData().getId());
            data.set(AccountsReceivableDetailFields.SOURCE_API_NAME, ApiNames.RETURNED_GOODS_INVOICE_OBJ);

            result.add(data);
        }
        return result;
    }

    private List<IObjectData> queryReturnGoodsInvoiceDetails(String tenantId, String id) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(ReturnedGoodsInvoiceProductFields.RETURNED_GOODS_INV_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        IFilter recordTypeFilter = new Filter();
        recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
        recordTypeFilter.setOperator(Operator.EQ);
        recordTypeFilter.setFieldValues(Lists.newArrayList(ReturnedGoodsInvoiceProductFields.SWAP_OUT));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter, recordTypeFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        ReturnedGoodsInvoiceProductFields.AUXILIARY_RETURNED_PRODUCT_PRICE,
                        ReturnedGoodsInvoiceProductFields.AUXILIARY_QUANTITY,
                        ReturnedGoodsInvoiceProductFields.RETURNED_PRODUCT_PRICE,
                        ReturnedGoodsInvoiceProductFields.QUANTITY,
                        ReturnedGoodsInvoiceProductFields.RETURNED_GOODS_INV_ID,
                        ReturnedGoodsInvoiceProductFields.ORDER_PRODUCT_ID,
                        ReturnedGoodsInvoiceProductFields.ORDER_ID,
                        ReturnedGoodsInvoiceProductFields.SETTLED_AMOUNT,
                        ReturnedGoodsInvoiceProductFields.PRODUCT_ID,
                        ReturnedGoodsInvoiceProductFields.SUBTOTAL
                )
        );
    }


}
