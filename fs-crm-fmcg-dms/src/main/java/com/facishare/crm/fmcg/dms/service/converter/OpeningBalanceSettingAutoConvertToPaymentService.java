package com.facishare.crm.fmcg.dms.service.converter;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.dms.model.ConvertResult;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.AutoOpeningBalanceSettingConvertService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.CustomButtonAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class OpeningBalanceSettingAutoConvertToPaymentService extends AutoOpeningBalanceSettingConvertService {

    @Override
    protected void beforeConvert(FinancialBill bill) {

        bill.setData(serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.OPENING_BALANCE_SETTING_OBJ));


    }

    @Override
    protected void validate(FinancialBill bill) {
        idempotent(bill);


    }

    @Override
    protected ConvertResult convertData(FinancialBill bill) {
        return ConvertResult.builder().data(covertToMaster(bill)).details(convertToDetails(bill)).build();
    }

    private IObjectData covertToMaster(FinancialBill bill) {
        IObjectData data = new ObjectData();

        data.setTenantId(bill.getTenantId());
        data.setDescribeApiName(ApiNames.PAYMENT_OBJ);
        data.set(CommonFields.CREATE_BY, bill.getData().get(CommonFields.CREATE_BY));
        data.set(CommonFields.OWNER, bill.getData().get(CommonFields.OWNER));
        data.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);


        data.set(PaymentFields.PAYMENT_TIME, bill.getData().get(OpeningBalanceSettingFields.BUSINESS_DATE));
        data.set(PaymentFields.ACCOUNT_ID, bill.getData().get(OpeningBalanceSettingFields.ACCOUNT_ID));

        data.set(PaymentFields.PAY_TYPE, PaymentFields.PAY_TYPE__OFF_LINE);
        data.set(PaymentFields.CONTACT_OBJECT, PaymentFields.CONTACT_OBJECT__ACCOUNT);
        data.set(PaymentFields.OPENING_BALANCE, true);
        data.set(PaymentFields.REMARKS, bill.getData().get(OpeningBalanceSettingFields.REMARKS));

        BigDecimal balance = bill.getData().get(OpeningBalanceSettingFields.OPENING_BALANCE, BigDecimal.class);
        data.set(PaymentFields.COLLECTION_TYPE, BigDecimal.ZERO.compareTo(balance) > 0 ? PaymentFields.COLLECTION_TYPE__RED : PaymentFields.COLLECTION_TYPE__BLUE);

        data.set(PaymentFields.AMOUNT, balance);
        return data;
    }

    private List<IObjectData> convertToDetails(FinancialBill bill) {
        return Lists.newArrayList();

    }


    @Override
    protected void after(FinancialBill bill, BaseObjectSaveAction.Result result) {
        super.after(bill, result);

        //入账
        paymentEnterAccount(bill, result);
    }

    private void paymentEnterAccount(FinancialBill bill, BaseObjectSaveAction.Result result) {
        IObjectData payment = result.getObjectData().toObjectData();
        IObjectData openingBalanceData = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.OPENING_BALANCE_SETTING_OBJ);
        String newCustomerAccountId = openingBalanceData.get(OpeningBalanceSettingFields.FUND_ACCOUNT_ID, String.class);
        if (StringUtils.isEmpty(newCustomerAccountId)) {
            return;
        }
        IObjectData newCustomerAccount = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), newCustomerAccountId, ApiNames.NEW_CUSTOMER_ACCOUNT_OBJ);
        String fundAccountId = newCustomerAccount.get(NewCustomerAccountObjFields.FUND_ACCOUNT_ID, String.class);

        ActionContext actionContext = new ActionContext(RequestContext.builder().tenantId(bill.getTenantId()).user(User.systemUser(bill.getTenantId())).build(),
                ApiNames.PAYMENT_OBJ, ObjectAction.ENTER_ACCOUNT.getActionCode());
        CustomButtonAction.Arg enterAccountArg = new CustomButtonAction.Arg();
        enterAccountArg.setArgs(new ObjectDataDocument());
        enterAccountArg.setObjectDataId(payment.getId());
        enterAccountArg.getArgs().put("form_fund_account_id", fundAccountId);
        serviceFacade.triggerAction(actionContext, enterAccountArg, CustomButtonAction.Result.class);
    }
}
