package com.facishare.crm.fmcg.dms.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;


public class AccountsPayableNoteObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException("action not allowed!");
    }
}
