package com.facishare.crm.fmcg.dms.business.cache;

import com.facishare.crm.fmcg.dms.errors.ModuleRetryException;
import com.facishare.paas.license.arg.JudgeModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class ModuleEnableCache extends LimitedReloadCache<String, Boolean> implements IModuleEnableCache {

    @Resource
    private LicenseClient licenseClient;

    public static final String CONNECTION_CHAR = "&&";
    private final AtomicInteger reloadCounter = new AtomicInteger(0);
    private static int MAX_RELOADS_PER_MINUTE = 1000;
    private long lastResetTime = System.currentTimeMillis();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", conf -> {

            String json = conf.get("MAX_RELOADS_PER_MINUTE", "1000");
            if (!Strings.isNullOrEmpty(json)) {
                try {
                    MAX_RELOADS_PER_MINUTE = Integer.parseInt(json);
                } catch (Exception ex) {
                    log.error("MAX_RELOADS_PER_MINUTE parse error : ", ex);
                }
            }

        });

    }

    public ModuleEnableCache() {
        super(5000, 3 * 60, 3 * 60, TimeUnit.MINUTES);
    }

    /**
     * 从数据库取值
     *
     * @param key:94931&&accounts_receivable
     * @return Boolean
     */
    @Override
    protected Boolean loadValueByKey(String key) {
        String[] arr = key.split(CONNECTION_CHAR, 2);
        return checkEnableByModuleCodeFromRemote(arr[0], arr[1]);
    }

    /**
     * 限流：一分钟内请求超过1000次，不再刷新缓存。而是直接取cache里边的值，如果cache为空抛出retry异常
     *
     * @return boolean
     */
    @Override
    protected boolean overlimit() {
        return false;
    }

    @Override
    protected boolean hit() {
        return true;
    }

    @Override
    public Boolean get(String tenantId, String moduleCode) {
        String key = formatKey(tenantId, moduleCode);
        return super.get(key);
    }

    @Override
    public void put(String tenantId, String moduleCode, Boolean enabled) {
        String key = formatKey(tenantId, moduleCode);
        super.put(key, enabled);
    }

    @Override
    public void invalidate(String tenantId, String moduleCode) {
        String key = formatKey(tenantId, moduleCode);
        super.invalidate(key);
    }

    private boolean checkEnableByModuleCodeFromRemote(String tenantId, String module) {

        JudgeModuleArg arg = new JudgeModuleArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setTenantId(tenantId);
        licenseContext.setAppId("CRM");
        licenseContext.setUserId("1000");
        arg.setContext(licenseContext);
        arg.setModuleCodes(Lists.newArrayList(module));
        try {
            Result<JudgeModulePojo> result = licenseClient.judgeModule(arg);
            if (Objects.nonNull(result) && Objects.nonNull(result.getResult()) && CollectionUtils.isNotEmpty(result.getResult().getModuleFlags())) {
                return result.getResult().getModuleFlags().stream().anyMatch(o -> o.isFlag() && module.equals(o.getModuleCode()));
            }
        } catch (Exception e) {
            throw new ModuleRetryException("judgeModule error");
        }
        return false;
    }

    private String formatKey(String tenantId, String module) {
        return tenantId + CONNECTION_CHAR + module;
    }
}
