package com.facishare.crm.fmcg.dms.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.impl.EIEAConverterImpl;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.business.DMSScriptHandlerFactory;
import com.facishare.crm.fmcg.dms.business.PaasLicenseBusinessService;
import com.facishare.crm.fmcg.dms.business.abstraction.FieldBusiness;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.enums.AccountPayableSwitchEnum;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.crm.fmcg.dms.constants.AccountsReceivableConstants;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.errors.DMSBusinessException;
import com.facishare.crm.fmcg.dms.errors.RetryActionException;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.crm.fmcg.dms.model.*;
import com.facishare.crm.fmcg.dms.mq.PaasLicenseConsumer;
import com.facishare.crm.fmcg.dms.service.DMSObjectActionService;
import com.facishare.crm.fmcg.dms.service.converter.AutoConverterActuator;
import com.facishare.crm.fmcg.dms.service.converter.OutboundDeliveryNoteAutoReceivableConvertToReceivableService;
import com.facishare.crm.fmcg.dms.service.mapper.MatchableBillMapper;
import com.facishare.crm.fmcg.dms.service.matcher.AutoMatcher;
import com.facishare.crm.fmcg.dms.web.abstraction.BaseService;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsReceivableNoteService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.restful.client.exception.FRestClientException;
import com.fmcg.framework.http.FundAccountProxy;
import com.fmcg.framework.http.contract.fund.CancelEntry;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class AccountsReceivableNoteService extends BaseService implements IAccountsReceivableNoteService {
    private static final long LOCK_WAIT = 20;
    private static final long LOCK_LEASE = 60;
    @Resource
    private AutoMatcher autoMatcher;
    @Resource
    private MatchableBillMapper matchableBillMapper;
    @Resource
    private PaasLicenseBusinessService paasLicenseBusinessService;
    @Resource
    protected BizConfClient bizConfClient;
    @Resource
    private DMSScriptHandlerFactory dmsScriptHandlerFactory;
    @Resource
    protected RedissonClient redissonCmd;
    @Resource
    private FundAccountProxy fundAccountProxy;
    @Resource
    protected DMSObjectActionService dmsObjectActionService;
    @Resource
    private AutoConverterActuator autoConverterActuator;
    @Resource
    private EIEAConverterImpl eieaConverter;
    @Resource
    private FieldBusiness fieldBusiness;

    /**
     * 待结算金额：
     * : IF(价税合计 < 0, - 价税合计 , 价税合计) - 已结算金额
     * <p>
     * 价税合计：
     * 应收单明细价税合计总和
     * <p>
     * 已结算金额：
     * 核销对象已结算金额+收付款对象已结算金额
     * <p>
     * 核销对象已结算金额：
     * 本次核销金额求和
     * <p>
     * 收付款对象已结算金额
     * 本次核销金额求和
     * RECEIVABLE_NO_SETTLED_AMOUNT_SUM_REAL_TIME
     */
    @Override
    public ReceivableNoSettledAmountSum.Result sum(ReceivableNoSettledAmountSum.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        if (denyAccountsReceivableEnable(context.getTenantId())) {
            return noSettledAmountSumDisableAccountReceivable(arg.getAccountIds());
        }
        if (!TPMGrayUtils.receivableNoSettledAmountSumRealTime(context.getTenantId())) {
            return noSettledAmountSumWithNotRealTime(context.getTenantId(), arg.getAccountIds());
        }
        ReceivableNoSettledAmountSum.Result result = new ReceivableNoSettledAmountSum.Result();
        result.setSumNoSettledAmount(new HashMap<>());

        List<IObjectData> receivableNotes = queryAccountsReceivableByAccountIds(context.getTenantId(), arg.getAccountIds());
        List<IObjectData> details = details(context.getTenantId(), new HashSet<>(receivableNotes.stream().map(IObjectData::getId).collect(Collectors.toList())));
        Map<String, List<IObjectData>> detailsByAccountReceivableId = details.stream().collect(Collectors.groupingBy(k -> k.get(AccountsReceivableDetailFields.AR_ID, String.class)));
        Map<String, List<IObjectData>> receivableNotesByAccountId = receivableNotes.stream().collect(Collectors.groupingBy(k -> k.get(AccountsReceivableNoteFields.ACCOUNT_ID, String.class)));


        for (Map.Entry<String, List<IObjectData>> entry : receivableNotesByAccountId.entrySet()) {

            String accountId = entry.getKey();
            List<String> accountReceivableNoteIds = entry.getValue().stream().map(IObjectData::getId).collect(Collectors.toList());
            List<IObjectData> accountAllDetails = Lists.newArrayList();
            for (String receivableNoteId : accountReceivableNoteIds) {
                accountAllDetails.addAll(detailsByAccountReceivableId.get(receivableNoteId));
            }
            BigDecimal priceTaxTotalAmount = calculatePriceTaxTotalAmount(accountAllDetails);

            BigDecimal creditSettledAmount = calculateCreditSettledAmount(context.getTenantId(), accountReceivableNoteIds);
            BigDecimal debitSettledAmount = calculateDebitSettledAmount(context.getTenantId(), accountReceivableNoteIds);


            BigDecimal settledAmount = creditSettledAmount.add(debitSettledAmount);

            BigDecimal noSettledAmount = priceTaxTotalAmount.subtract(settledAmount);

            result.getSumNoSettledAmount().put(accountId, noSettledAmount);

        }
        return result;
    }

    @Override
    public AccountsReceivableCreate.Result createRedAccountsReceivableFromRebate(AccountsReceivableCreate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (denyAccountsReceivableEnable(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_0));
        }
        String key = buildRebateLockKey(context.getTenantId(), arg.getObjectId());
        if (!tryLock(key)) {
            throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_1));
        }

        try {
            List<IObjectData> accountsReceivableByObjectReceivable = queryAccountsReceivableByObjectReceivable(context.getTenantId(), arg.getObjectId());
            if (CollectionUtils.isNotEmpty(accountsReceivableByObjectReceivable)) {
                throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_2));
            }

            IObjectData rebate = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getObjectId(), ApiNames.REBATE_OBJ);
            if (Objects.isNull(rebate)) {
                throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_3));
            }
            long startDate = rebate.get(RebateFields.START_DATE, Long.class);
            long endDate = OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.getDayEndTime(new Date(rebate.get(RebateFields.END_DATE, Long.class))).getTime();
            long now = System.currentTimeMillis();
            if (now < startDate) {
                throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_4));
            }
            if (now > endDate) {
                throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_5));
            }
            String accountId = rebate.get(RebateFields.ACCOUNT_ID, String.class);
            if (StringUtils.isEmpty(accountId)) {
                throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_6));
            }

            IObjectData account = serviceFacade.findObjectDataIgnoreAll(User.systemUser(context.getTenantId()), accountId, ApiNames.ACCOUNT_OBJ);
            if (account == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_7));
            }
            String accountingPeriodType = account.get(AccountFields.ACCOUNTING_PERIOD_TYPE, String.class);
            if (StringUtils.isEmpty(accountingPeriodType) || Objects.equals(accountingPeriodType, AccountFields.ACCOUNTING_PERIOD_TYPE__CASH)) {
                throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_8));
            }

            Boolean enterIntoRebate = rebate.get(RebateFields.ENTER_INTO_ACCOUNT, Boolean.class);
            if (Boolean.TRUE.equals(enterIntoRebate)) {
                String fundAccountId = rebate.get(RebateFields.FUND_ACCOUNT_ID, String.class);
                if (StringUtils.isEmpty(fundAccountId)) {
                    throw new ValidateException("[fund_account_id] is null。");
                }

                IObjectData fundAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(context.getTenantId()), fundAccountId, ApiNames.FUND_ACCOUNT_OBJ);
                if (fundAccount == null) {
                    throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_9));
                }
            }

            if (Boolean.TRUE.equals(enterIntoRebate)) {
                doCancelEntryAction(context.getTenantId(), arg.getObjectId());
            } else {
                BigDecimal sumAmount = rebate.get(RebateFields.SUM_AMOUNT, BigDecimal.class);
                BigDecimal usedAmount = calculateRebateUsedAmount(context.getTenantId(), rebate.getId());
                BigDecimal unusedAmount = sumAmount.subtract(usedAmount);
                if (unusedAmount.compareTo(sumAmount) != 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_NOTE_SERVICE_10));
                }
            }
            BaseObjectSaveAction.Result result = save(context.getTenantId(), covertAccountsReceivable(context.getTenantId(), rebate), covertAccountsReceivableDetails(context.getTenantId(), rebate));

            save(context.getTenantId(), covertRebateDetail(context.getTenantId(), result.getObjectData().toObjectData(), rebate), Lists.newArrayList());

        } finally {
            unlock(key);
        }

        return new AccountsReceivableCreate.Result();
    }

    private boolean tryLock(String key) {

        RLock lock = redissonCmd.getLock(key);

        log.info("try lock rebate : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format("try lock rebate cause thread interrupted exception : %s", key));
        }
    }

    private void unlock(String key) {
        RLock lock = redissonCmd.getLock(key);

        log.info("unlock rebate  : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private String buildAutoMatchButtonEnableLockKey(String tenantId) {
        return String.format("FMCG:AUTO_MATCH_BUTTON_ENABLE_LOCK_KEY_%s", tenantId);
    }

    private String buildRebateLockKey(String tenantId, String rebateId) {
        return String.format("RED_ACCOUNTS_RECEIVABLE_CREATE_FROM_REBATE_%s_%s", tenantId, rebateId);
    }

    private ReceivableNoSettledAmountSum.Result noSettledAmountSumDisableAccountReceivable(List<String> accountIds) {
        ReceivableNoSettledAmountSum.Result result = new ReceivableNoSettledAmountSum.Result();
        result.setSumNoSettledAmount(new HashMap<>());
        result.setOpenAccountReceivable(false);
        for (String accountId : accountIds) {
            result.getSumNoSettledAmount().put(accountId, new BigDecimal("0"));
        }
        return result;
    }

    private ReceivableNoSettledAmountSum.Result noSettledAmountSumWithNotRealTime(String tenantId, List<String> accountIds) {
        ReceivableNoSettledAmountSum.Result result = new ReceivableNoSettledAmountSum.Result();
        result.setSumNoSettledAmount(new HashMap<>());
        List<IObjectData> receivableNotes = queryAccountsReceivableByAccountIds(tenantId, accountIds);
        Map<String, List<IObjectData>> receivableNotesByAccountId = receivableNotes.stream().collect(Collectors.groupingBy(k -> k.get(AccountsReceivableNoteFields.ACCOUNT_ID, String.class)));
        for (String accountId : accountIds) {
            BigDecimal allOfNoSettledAmount = new BigDecimal("0");
            List<IObjectData> receivableNotesOfAccount = receivableNotesByAccountId.get(accountId);
            if (CollectionUtils.isEmpty(receivableNotesOfAccount)) {
                return result;
            }
            for (IObjectData receivableNoteOfAccount : receivableNotesOfAccount) {
                BigDecimal noSettledAmount = receivableNoteOfAccount.get(AccountsReceivableDetailFields.NO_SETTLED_AMOUNT, BigDecimal.class);
                if (Objects.isNull(noSettledAmount)) {
                    continue;
                }
                allOfNoSettledAmount = allOfNoSettledAmount.add(noSettledAmount);
            }
            result.getSumNoSettledAmount().put(accountId, allOfNoSettledAmount);
        }
        return result;
    }

    @Override
    public boolean denyAccountsReceivableEnable(String tenantId) {
        QueryConfigByRankArg arg = QueryConfigByRankArg.builder().rank(Rank.TENANT).key("accounts_receivable_status").tenantId(tenantId).pkg("CRM").build();
        String config;
        try {
            config = bizConfClient.queryConfigByRank(arg);
        } catch (FRestClientException e) {
            throw new RetryActionException("load match config error, try again later.");
        }
        if (Strings.isNullOrEmpty(config)) {
            return true;
        }
        int value = Integer.parseInt(config);
        return value != 2;
    }

    @Override
    public void initField(List<Integer> tenantIds) {

        for (Integer tenantIdInt : tenantIds) {
            String tenantId = String.valueOf(tenantIdInt);
            boolean enableKX = paasLicenseBusinessService.checkKXEnableByModuleCode(tenantId, PaasLicenseConsumer.KX_INDUSTRY);
            boolean enableAccountReceivable = enableAccountsReceivableEnable(tenantId);
            Map<String, IObjectDescribe> describes = serviceFacade.findObjects(tenantId, Lists.newArrayList(ApiNames.GOODS_RECEIVED_NOTE_OBJ
                    , ApiNames.RETURNED_GOODS_INVOICE_OBJ, ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ, ApiNames.SALES_ORDER_OBJ));

            if (enableAccountReceivable) {

                IDMSScriptHandler onlyAccountReceivableEnableHandler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.ACCOUNTS_RECEIVABLE_ENABLE.getHandlerName());
                //开启应收之后 入库单增加”累计结算金额“字段，退货单产品增加”已结算金额“字段
                //开启应收之后退货单增加字段“已退款金额”，“待退款金额”，”累计结算金额“
                onlyAccountReceivableEnableHandler.addFields(tenantId, describes);
                //开启应收之后，更新字段计算公式
                onlyAccountReceivableEnableHandler.updateFields(tenantId, describes);

                //启用应收之后增加退款按钮，下个版本放开
                onlyAccountReceivableEnableHandler.initButton(tenantId);
            } else if (enableKX) {
                //开启快消，未开启应收，处理字段逻辑
                IDMSScriptHandler onlyKXEnable = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.KX_ENABLE.getHandlerName());
                onlyKXEnable.updateFields(tenantId, describes);
            }
        }
    }

    @Override
    public void initRefundButton(List<Integer> tenantIds) {
        for (Integer tenantIdInt : tenantIds) {
            String tenantId = String.valueOf(tenantIdInt);
            boolean enableAccountReceivable = enableAccountsReceivableEnable(tenantId);
            if (enableAccountReceivable) {
                Map<String, IObjectDescribe> describes = serviceFacade.findObjects(tenantId, Lists.newArrayList(ApiNames.RETURNED_GOODS_INVOICE_OBJ));
                IObjectDescribe orderDescribe = describes.get(ApiNames.RETURNED_GOODS_INVOICE_OBJ);
                for (IFieldDescribe fieldDescribe : orderDescribe.getFieldDescribes()) {
                    if (ReturnedGoodsInvoiceFields.PENDING_REFUND_AMOUNT.equals(fieldDescribe.getApiName())) {
                        fieldDescribe.setExpression("($returned_goods_inv_amount$-MAX(IF($refund_amount$<0, -$refund_amount$, $refund_amount$),IF($total_settled_amount$<0, -$total_settled_amount$, $total_settled_amount$)))*-1");
                        serviceFacade.updateFieldDescribe(orderDescribe, com.beust.jcommander.internal.Lists.newArrayList(fieldDescribe));
                    }
                }

                IDMSScriptHandler onlyAccountReceivableEnableHandler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.ACCOUNTS_RECEIVABLE_ENABLE.getHandlerName());
                onlyAccountReceivableEnableHandler.initButton(tenantId);
            }
        }
    }

    @Override
    public RetryMatch.Result retryMatch(RetryMatch.Arg arg) {
        FinancialBill bill = FinancialBill.builder()
                .tenantId(arg.getTenantId())
                .apiName(arg.getApiName())
                .id(arg.getDataId())
                .build();

        List<FinancialBill> matchableBills = matchableBillMapper.map(bill);
        for (FinancialBill matchableBill : matchableBills) {
            autoMatcher.match(matchableBill);
        }

        return new RetryMatch.Result();
    }

    @Override
    public RetryConvert.Result retryConvert(RetryConvert.Arg arg) {
        FinancialBill bill = FinancialBill.builder().tenantId(arg.getTenantId()).apiName(arg.getApiName()).id(arg.getDataId()).build();

        if (Strings.isNullOrEmpty(bill.getApiName()) || Strings.isNullOrEmpty(bill.getId())) {
            throw new AbandonActionException("apiName or objectId is null.");
        }

        TraceContext context = TraceContext.get();
        if (Strings.isNullOrEmpty(context.getTraceId())) {
            context.setTraceId(String.format("DMS_OBJECT_MQ_RETRY.%s.%s.%s", arg.getTenantId(), bill.getApiName(), bill.getId()));
        }

        try {

            autoConverterActuator.convert(bill);

        } finally {
            TraceContext.remove();
        }
        return new RetryConvert.Result();
    }

    @Override
    public void invalidMatchNoteWithRebateAccounts(List<String> tenantIds, String flag) {
        String tenantIdStr;
        try {
            File mainJsonFile = ResourceUtils.getFile("classpath:dms/mnTenantIds.txt");
            tenantIdStr = new String(Files.readAllBytes(mainJsonFile.toPath()));
        } catch (Exception ex) {
            throw new RuntimeException("read mnTenantIds.txt error", ex);
        }
        List<String> tenantIdsResult = Lists.newArrayList();
//        tenantIdsResult.add("89150");
        if ("true".equals(flag)) {
            tenantIdsResult.addAll(tenantIds);
        } else {
            String[] split = tenantIdStr.split(",");
            for (String s : split) {
                if (org.springframework.util.StringUtils.isEmpty(s)) {
                    continue;
                }
                tenantIdsResult.add(s.trim());
            }
        }
        List<String> fields = Lists.newArrayList(CommonFields.ID, MatchNoteFields.CREDIT_DATA_ID, MatchNoteFields.CREDIT_API_NAME, MatchNoteFields.DEBIT_DATA_ID);
        StringBuilder sb = new StringBuilder();
        for (String tenantId : tenantIdsResult) {
            IObjectDescribe matchNoteDetailDescribe;
            try {
                matchNoteDetailDescribe = serviceFacade.findObject(tenantId, ApiNames.MATCH_NOTE_DETAIL_OBJ);
            } catch (ObjectDefNotFoundError ex) {
                continue;
            }

            List<String> allRebateAccountIds = getAllRebateAccountIds(tenantId);
            //查询所有核销单
            //获取所有收付款对象
            //首付款对象进行filter
            //核销单filter
            //作废
            QueryDataUtil.findAndConsume(serviceFacade, User.systemUser(tenantId), ApiNames.MATCH_NOTE_OBJ, getQueryTemplate(), fields, dataList -> {

                List<String> creditIds = dataList.stream().map(d -> d.get(MatchNoteFields.CREDIT_DATA_ID, String.class)).collect(Collectors.toList());
                List<IObjectData> newCustomerAccountObjs = serviceFacade.findObjectDataByIds(tenantId, creditIds, ApiNames.NEW_CUSTOMER_ACCOUNT_OBJ);
                List<String> newCustomerAccountIdsWithRebateAccountId = newCustomerAccountObjs.stream()
                        .filter(d -> allRebateAccountIds.contains(d.get(NewCustomerAccountObjFields.FUND_ACCOUNT_ID, String.class)))
                        .map(IObjectData::getId)
                        .collect(Collectors.toList());


                List<String> needInvalidMatchNoteIds = dataList.stream().filter(d -> newCustomerAccountIdsWithRebateAccountId.contains(d.get(MatchNoteFields.CREDIT_DATA_ID, String.class))).map(IObjectData::getId).collect(Collectors.toList());
                List<IObjectData> matchNotes = serviceFacade.findObjectDataByIds(tenantId, needInvalidMatchNoteIds, ApiNames.MATCH_NOTE_OBJ);

                List<IObjectData> details = serviceFacade.findDetailObjectDataList(User.systemUser(tenantId), matchNoteDetailDescribe, matchNotes);

                List<IObjectData> accountsReceivables = serviceFacade.findObjectDataByIds(tenantId, matchNotes.stream().map(d -> d.get(MatchNoteFields.DEBIT_DATA_ID, String.class)).collect(Collectors.toList())
                        , ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ);
                Set<String> accountsReceivableNames = accountsReceivables.stream().map(IObjectData::getName).collect(Collectors.toSet());
                Set<String> matchNoteNames = matchNotes.stream().map(IObjectData::getName).collect(Collectors.toSet());
                sb.append("tenantId:").append(tenantId).append("ar:").append(JSON.toJSONString(accountsReceivableNames)).append("matchnote:").append(JSON.toJSONString(matchNoteNames)).append("&&&");
            });
        }
        log.info("=====:{}", sb.toString());

    }

    @Override
    public EnableReceivableAutoMatch.Result enableAutoMatch(EnableReceivableAutoMatch.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        //SFA开关开了，不允许开
        String isOpenAutoMatch = queryConfig(context.getTenantId(), "is_open_auto_match");
        if ("1".equals(isOpenAutoMatch)) {
            updateReceivabelAutoMatchButtonStatus(User.systemUser(context.getTenantId()), AccountPayableSwitchEnum.OPEN_FAIL.getStatus());
            throw new DMSBusinessException(I18N.text(I18NKeys.ALREADY_ENABLE_SFA_AUTO_MATCH));
        }

        String status = queryConfig(context.getTenantId(), AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY);
        if (Objects.equals(status, String.valueOf(AccountPayableSwitchEnum.OPENED.getStatus()))) {
            throw new DMSBusinessException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_DUPLICATE_ENABLE_ERROR));
        }
        String key = buildAutoMatchButtonEnableLockKey(context.getTenantId());
        if (!tryLock(key)) {
            throw new DMSBusinessException(I18N.text(I18NKeys.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_DUPLICATE_ENABLE_ERROR));
        }

        try {
            updateReceivabelAutoMatchButtonStatus(User.systemUser(context.getTenantId()), AccountPayableSwitchEnum.OPENED.getStatus());
        } catch (Exception ex) {
            updateReceivabelAutoMatchButtonStatus(User.systemUser(context.getTenantId()), AccountPayableSwitchEnum.OPEN_FAIL.getStatus());
        } finally {
            unlock(key);
        }

        try {
            IObjectDescribe accountsReceivableNoteObjDescribe = serviceFacade.findObject(context.getTenantId(), ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ);
            for (IFieldDescribe fieldDescribe : accountsReceivableNoteObjDescribe.getFieldDescribes()) {
                if (AccountsReceivableNoteFields.OBJECT_RECEIVABLE.equals(fieldDescribe.getApiName())) {

                    fieldDescribe.set("options", addObjectReceivableOptions((List<Map<String, String>>) fieldDescribe.get("options")));

                    serviceFacade.updateFieldDescribe(accountsReceivableNoteObjDescribe, com.beust.jcommander.internal.Lists.newArrayList(fieldDescribe));
                }
            }
        } catch (Exception ex) {
            log.error("add field error");
        }

        IObjectDescribe returnGoodsInvoice = null;
        try {
            IObjectDescribe payment = serviceFacade.findObject(context.getTenantId(), ApiNames.PAYMENT_OBJ);
            fieldBusiness.addField(context.getTenantId(), payment, ApiNames.PAYMENT_OBJ,
                    PaymentFields.RETURNED_GOODS_INVOICE_ID, false, true);

        } catch (Exception ex) {
            log.error("新增回款单.退货单字段失败", ex);
        }

        try {
            IObjectDescribe payment = serviceFacade.findObject(context.getTenantId(), ApiNames.PAYMENT_OBJ);
            for (IFieldDescribe fieldDescribe : payment.getFieldDescribes()) {
                if (PaymentFields.RETURNED_GOODS_INVOICE_ID.equals(fieldDescribe.getApiName())) {

                    returnGoodsInvoice = serviceFacade.findObject(context.getTenantId(), ApiNames.RETURNED_GOODS_INVOICE_OBJ);
                    fieldBusiness.addField(context.getTenantId(), returnGoodsInvoice, ApiNames.RETURNED_GOODS_INVOICE_OBJ,
                            ReturnedGoodsInvoiceFields.EXCHANGE_SETTLEMENT_AMOUNT, false, true);

                }
            }

        } catch (Exception ex) {
            log.error("新增退货单单.换货结算金额失败", ex);
        }
        try {
            if (returnGoodsInvoice != null) {

                for (IFieldDescribe fieldDescribe : returnGoodsInvoice.getFieldDescribes()) {
                    if (ReturnedGoodsInvoiceFields.PENDING_REFUND_AMOUNT.equals(fieldDescribe.getApiName())) {

                        fieldDescribe.set("expression", "(IF($returned_goods_inv_amount$<0, -$returned_goods_inv_amount$, $returned_goods_inv_amount$)-MAX(MAX(IF($exchange_settlement_amount$<0, -$exchange_settlement_amount$, $exchange_settlement_amount$),IF($refund_amount$<0, -$refund_amount$, $refund_amount$)),IF($total_settled_amount$<0, -$total_settled_amount$, $total_settled_amount$)))*IF($returned_goods_inv_amount$<0, 1, -1)");

                        serviceFacade.updateFieldDescribe(returnGoodsInvoice, Lists.newArrayList(fieldDescribe));
                    }
                }
            }

        } catch (Exception ex) {
            log.error("updateFields RETURNED_GOODS_INVOICE_OBJ error,", ex);
        }
        return EnableReceivableAutoMatch.Result.builder().enable(true).status(String.valueOf(AccountPayableSwitchEnum.OPENED.getStatus())).build();
    }

    @Override
    public QueryReceivableAutoMatchStatus.Result queryAutoMatchStatus(QueryReceivableAutoMatchStatus.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String status = queryConfig(context.getTenantId(), AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY);
        return QueryReceivableAutoMatchStatus.Result.builder().status(StringUtils.isEmpty(status) ? String.valueOf(AccountPayableSwitchEnum.NOT_OPEN.getStatus()) : status).build();
    }


    @Override
    public void batchEnableAutoMatch(List<Integer> tenantIds, String flag, String env) {
        String tenantIdStr;
        if (Objects.equals("mn", env)) {
            try {
                File mainJsonFile = ResourceUtils.getFile("classpath:dms/mnTenantIds.txt");
                tenantIdStr = new String(Files.readAllBytes(mainJsonFile.toPath()));
            } catch (Exception ex) {
                throw new RuntimeException("read mnTenantIds.txt error", ex);
            }
        } else {
            try {
                File mainJsonFile = ResourceUtils.getFile("classpath:dms/fsTenantIds.txt");
                tenantIdStr = new String(Files.readAllBytes(mainJsonFile.toPath()));
            } catch (Exception ex) {
                throw new RuntimeException("read mnTenantIds.txt error", ex);
            }
        }

        List<String> tenantIdsResult = Lists.newArrayList();
        if ("true".equals(flag)) {
            tenantIdsResult.addAll(tenantIds.stream().map(String::valueOf).collect(Collectors.toList()));
        } else {
            String[] split = tenantIdStr.split(",");
            for (String s : split) {
                if (org.springframework.util.StringUtils.isEmpty(s)) {
                    continue;
                }
                tenantIdsResult.add(s.trim());
            }
        }
        List<String> openedTenantIds = Lists.newArrayList();
        List<String> openingTenantIds = Lists.newArrayList();
        List<String> lockErrorTenantIds = Lists.newArrayList();
        List<String> failedTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIdsResult) {
            ApiContext context = ApiContext.builder().tenantId(String.valueOf(tenantId)).build();
            if (enableAccountsReceivableAutoMatchButton(context.getTenantId())) {
                openedTenantIds.add(tenantId);
                continue;
            }
            String status = queryConfig(context.getTenantId(), AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY);
            if (Objects.equals(status, String.valueOf(AccountPayableSwitchEnum.OPENING.getStatus()))) {
                openingTenantIds.add(tenantId);
                continue;
            }
            if (!tryLock(context.getTenantId())) {
                lockErrorTenantIds.add(tenantId);
                continue;
            }

            try {
                updateReceivabelAutoMatchButtonStatus(User.systemUser(context.getTenantId()), AccountPayableSwitchEnum.OPENED.getStatus());
            } catch (Exception ex) {
                log.error("batchEnableAutoMatch err,tenantId:{},", tenantId, ex);
                failedTenantIds.add(tenantId);
            } finally {
                unlock(context.getTenantId());
            }
        }

        log.info("openedTenantIds:{},openingTenantIds:{},lockErrorTenantIds:{},failedTenantIds:{}", JSON.toJSONString(openedTenantIds),
                JSON.toJSONString(openingTenantIds), JSON.toJSONString(lockErrorTenantIds), JSON.toJSONString(failedTenantIds));

    }

    @Override
    public void fixMengniuPaymentMatch(RetryMatch.Arg arg, Long begin, Long end) {
        log.info("fixMengniuPaymentMatch start");
        if (!tryLock("777421")) {
            log.info("fixMengniuPaymentMatch lck fail");
            return;
        }
        try {
            if (StringUtils.isNotEmpty(arg.getTenantId())) {
                fixMNPaymentMatch(arg.getTenantId(), begin, end);
            } else {
                List<String> allTenantIds = findAllTenantIds("777421");
                if (CollectionUtils.isEmpty(allTenantIds)) {
                    log.info("tenant null");
                    return;
                }
                for (String tenantId : allTenantIds) {
                    try {
                        fixMNPaymentMatch(tenantId, begin, end);
                    } catch (Exception ex) {
                        log.error("fixMengniuPaymentMatch error,tenantId:{}", tenantId, ex);
                    }

                }
            }
        } finally {
            unlock("777421");
        }
        log.info("fixMengniuPaymentMatch end");
    }

    private void fixMNPaymentMatch(String tenantId, Long begin, Long end) {
        int limit = 2;
        int offset = 0;
        int max = 0;
        while (true) {
            max++;
            if (max > 500) {
                break;
            }
            List<IObjectData> payments = payments(tenantId, begin, end, limit, offset);

            if (CollectionUtils.isEmpty(payments)) {
                break;
            }

            for (IObjectData payment : payments) {
                try {
                    FinancialBill bill = FinancialBill.builder()
                            .tenantId(tenantId)
                            .apiName(ApiNames.PAYMENT_OBJ)
                            .id(payment.getId())
                            .build();

                    List<FinancialBill> matchableBills = matchableBillMapper.map(bill);
                    for (FinancialBill matchableBill : matchableBills) {
                        autoMatcher.match(matchableBill);
                    }
                } catch (AbandonActionException ignore) {
                }

            }

            offset += limit;
        }
    }

    private List<String> findAllTenantIds(String mengniuTenantId) {

        List<String> tenantIds = Lists.newArrayList();
        List<IObjectData> dataList = queryEnterpriseRelationObj(mengniuTenantId);
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<String> enterpriseAccount = dataList.stream().map(data -> data.get("enterprise_account", String.class)).collect(Collectors.toList());
            Map<String, Integer> enterpriseAccountToIdMap = eieaConverter.enterpriseAccountToId(enterpriseAccount);
            tenantIds = enterpriseAccountToIdMap.values().stream().map(String::valueOf).distinct().collect(Collectors.toList());
        }
        return tenantIds;
    }

    private List<IObjectData> queryEnterpriseRelationObj(String tenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");
        query.setLimit(-1);


        //1 复制中; 2 复制成功; 3 复制失败
        IFilter copyStatusFilter = new Filter();
        copyStatusFilter.setFieldName("copy_status");
        copyStatusFilter.setOperator(Operator.EQ);
        copyStatusFilter.setFieldValues(Lists.newArrayList("2"));


        //1 正常
        IFilter typeFilter = new Filter();
        typeFilter.setFieldName("relation_type");
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList("1"));


        query.setFilters(Lists.newArrayList(copyStatusFilter, typeFilter));

        List<String> fields = Lists.newArrayList("enterprise_account");
        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.ENTERPRISE_RELATION_OBJ, query, fields);
    }

    private List<IObjectData> payments(String tenantId, long begin, long end, int limit, int offset) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(limit);
        query.setOffset(offset);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(true);

        IFilter filter = new Filter();
        filter.setFieldName(PaymentFields.MATCH_STATUS);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(PaymentFields.MATCH_STATUS__NO));


        IFilter beginFilter = new Filter();
        beginFilter.setFieldName(CommonFields.CREATE_TIME);
        beginFilter.setOperator(Operator.GTE);
        beginFilter.setFieldValues(Lists.newArrayList(String.valueOf(begin)));


        IFilter endFilter = new Filter();
        endFilter.setFieldName(CommonFields.CREATE_TIME);
        endFilter.setOperator(Operator.LTE);
        endFilter.setFieldValues(Lists.newArrayList(String.valueOf(end)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(filter, beginFilter, endFilter, lifeStatusFilter));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PAYMENT_OBJ,
                query,
                Lists.newArrayList(CommonFields.ID)
        );
    }

    private String queryConfig(String tenantId, String key) {
        QueryConfigByRankArg queryStatusArg =
                QueryConfigByRankArg.builder().rank(Rank.TENANT).key(key).tenantId(tenantId).pkg(AccountsReceivableConstants.SWITCH_PKG).build();
        String config;
        try {
            config = bizConfClient.queryConfigByRank(queryStatusArg);
        } catch (FRestClientException ex) {
            throw new DMSBusinessException("query config error");
        }
        return config;
    }

    private List<String> getAllRebateAccountIds(String tenantId) {
        IFilter filter = new Filter();
        filter.setFieldName(FundAccountFields.ACCESS_MODULE);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(FundAccountFields.ACCESS_MODULE_BACK));


        SearchTemplateQuery query = QueryDataUtil.minimumQuery(filter);

        // 通过码对象，查找这个码对象的门店签收流转记录数据
        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.FUND_ACCOUNT_OBJ,
                query,
                Lists.newArrayList(CommonFields.ID)
        );
        return data.stream().map(IObjectData::getId).collect(Collectors.toList());
    }

    private SearchTemplateQuery getQueryTemplate() {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");


        IFilter filter = new Filter();
        filter.setFieldName(MatchNoteFields.VERIFICATION_METHOD);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(MatchNoteFields.VERIFICATION_METHOD__ACCOUNT_FLOW_OFFSET_AR));


        query.setFilters(Lists.newArrayList(filter));

        return query;
    }

    private boolean enableAccountsReceivableEnable(String tenantId) {
        QueryConfigByRankArg arg = QueryConfigByRankArg.builder().rank(Rank.TENANT).key("accounts_receivable_status").tenantId(tenantId).pkg("CRM").build();
        String config;
        try {
            config = bizConfClient.queryConfigByRank(arg);
        } catch (FRestClientException e) {
            throw new RetryActionException("load match config error, try again later.");
        }
        if (Strings.isNullOrEmpty(config)) {
            return true;
        }
        int value = Integer.parseInt(config);
        return value == 2;
    }


    //收付款对象已结算金额
    protected BigDecimal calculateCreditSettledAmount(String tenantId, List<String> accountReceivableNoteIds) {
        List<List<String>> partition = Lists.partition(accountReceivableNoteIds, 200);
        BigDecimal amount = new BigDecimal("0");
        for (List<String> accountReceivableNoteIdsGroup : partition) {

            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setSearchSource("db");
            query.setLimit(200);
            query.setOffset(0);

            Filter debitDataIdFilter = new Filter();
            debitDataIdFilter.setFieldName(MatchNoteFields.CREDIT_DATA_ID);
            debitDataIdFilter.setOperator(Operator.IN);
            debitDataIdFilter.setFieldValues(accountReceivableNoteIdsGroup);

            Filter debitApiNameFilter = new Filter();
            debitApiNameFilter.setFieldName(MatchNoteDetailFields.CREDIT_API_NAME);
            debitApiNameFilter.setOperator(Operator.EQ);
            debitApiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ));

            Filter isDeletedFilter = new Filter();
            isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
            isDeletedFilter.setOperator(Operator.EQ);
            isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

            Filter lifeStatusFilter = new Filter();
            lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
            lifeStatusFilter.setOperator(Operator.EQ);
            lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));


            query.setFilters(Lists.newArrayList(debitDataIdFilter, debitApiNameFilter, isDeletedFilter, lifeStatusFilter));

            List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                    User.systemUser(tenantId),
                    query,
                    ApiNames.MATCH_NOTE_OBJ,
                    Lists.newArrayList(MatchNoteDetailFields.CREDIT_DATA_ID, MatchNoteDetailFields.CREDIT_API_NAME),
                    "sum",
                    MatchNoteDetailFields.CREDIT_MATCH_AMOUNT
            );

            if (!CollectionUtils.isEmpty(data)) {
                for (IObjectData datum : data) {
                    BigDecimal sumAmount = datum.get("sum_" + MatchNoteDetailFields.CREDIT_MATCH_AMOUNT, BigDecimal.class);
                    if (!Objects.isNull(sumAmount)) {
                        amount = amount.add(sumAmount);
                    }
                }

            }
        }
        return amount;
    }

    //核销对象已结算金额
    protected BigDecimal calculateDebitSettledAmount(String tenantId, List<String> accountReceivableNoteIds) {
        List<List<String>> partition = Lists.partition(accountReceivableNoteIds, 200);
        BigDecimal amount = new BigDecimal("0");
        for (List<String> accountReceivableNoteIdsGroup : partition) {

            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setSearchSource("db");
            query.setLimit(200);
            query.setOffset(0);

            Filter debitDataIdFilter = new Filter();
            debitDataIdFilter.setFieldName(MatchNoteFields.DEBIT_DATA_ID);
            debitDataIdFilter.setOperator(Operator.IN);
            debitDataIdFilter.setFieldValues(accountReceivableNoteIdsGroup);

            Filter debitApiNameFilter = new Filter();
            debitApiNameFilter.setFieldName(MatchNoteDetailFields.DEBIT_API_NAME);
            debitApiNameFilter.setOperator(Operator.EQ);
            debitApiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ));

            Filter isDeletedFilter = new Filter();
            isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
            isDeletedFilter.setOperator(Operator.EQ);
            isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

            Filter lifeStatusFilter = new Filter();
            lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
            lifeStatusFilter.setOperator(Operator.EQ);
            lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));


            query.setFilters(Lists.newArrayList(debitDataIdFilter, debitApiNameFilter, isDeletedFilter, lifeStatusFilter));

            List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                    User.systemUser(tenantId),
                    query,
                    ApiNames.MATCH_NOTE_OBJ,
                    Lists.newArrayList(MatchNoteDetailFields.DEBIT_DATA_ID, MatchNoteDetailFields.DEBIT_API_NAME),
                    "sum",
                    MatchNoteDetailFields.THIS_MATCH_AMOUNT
            );

            if (!CollectionUtils.isEmpty(data)) {
                for (IObjectData datum : data) {
                    BigDecimal sumAmount = datum.get("sum_" + MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class);
                    if (!Objects.isNull(sumAmount)) {
                        amount = amount.add(sumAmount);
                    }
                }

            }
        }
        return amount;
    }

    private List<IObjectData> details(String tenantId, Set<String> accountsReceivableIds) {
        if (CollectionUtils.isEmpty(accountsReceivableIds)) {
            return Lists.newArrayList();
        }

        IFilter accountFilter = new Filter();
        accountFilter.setFieldName(AccountsReceivableDetailFields.AR_ID);
        accountFilter.setOperator(Operator.IN);
        accountFilter.setFieldValues(Lists.newArrayList(accountsReceivableIds));


        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__IN_CHANGE, CommonFields.LIFE_STATUS__NORMAL));


        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(accountFilter, lifeStatusFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, AccountsReceivableDetailFields.AR_ID, AccountsReceivableDetailFields.PRICE_TAX_AMOUNT)
        );
    }

    private BigDecimal calculatePriceTaxTotalAmount(List<IObjectData> details) {
        BigDecimal result = new BigDecimal("0");
        if (CollectionUtils.isEmpty(details)) {
            return result;
        }
        for (IObjectData detail : details) {
            BigDecimal priceTaxAmount = detail.get(AccountsReceivableDetailFields.PRICE_TAX_AMOUNT, BigDecimal.class);
            result = result.add(priceTaxAmount);
        }
        return result;


    }


    private List<IObjectData> queryAccountsReceivableByAccountIds(String tenantId, List<String> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Lists.newArrayList();
        }

        IFilter accountFilter = new Filter();
        accountFilter.setFieldName(AccountsReceivableNoteFields.ACCOUNT_ID);
        accountFilter.setOperator(Operator.IN);
        accountFilter.setFieldValues(accountIds);

        IFilter amountFilter = new Filter();
        amountFilter.setFieldName(AccountsReceivableNoteFields.PRICE_TAX_TOTAL_AMOUNT);
        amountFilter.setOperator(Operator.LT);
        amountFilter.setFieldValues(Lists.newArrayList("0"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(accountFilter, amountFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, AccountsReceivableNoteFields.ACCOUNT_ID, AccountsReceivableDetailFields.NO_SETTLED_AMOUNT)
        );
    }

    private void doCancelEntryAction(String tenantId, String objectId) {
        CancelEntry.Arg arg = new CancelEntry.Arg();
        arg.setDataId(objectId);
        CancelEntry.Result result = fundAccountProxy.cancelEntry(Integer.parseInt(tenantId), -10000, ApiNames.REBATE_OBJ, arg);
        if (result.getCode() != 0) {
            log.error("cancel entry fail:{}", JSON.toJSONString(result.getMessage()));
            throw new MetaDataBusinessException(result.getMessage());
        }
    }

    private BaseObjectSaveAction.Result save(String tenantId, IObjectData master, List<IObjectData> details) {
        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Add")
                .apiName(master.getDescribeApiName())
                .objectData(master)
                .user(User.systemUser(tenantId))
                .triggerFlow(false)
                .triggerWorkflow(true)
                .build();

        if (CollectionUtils.isNotEmpty(details)) {
            arg.setDetails(details);
            arg.setDetailApiName(details.get(0).getDescribeApiName());
        }
        BaseObjectSaveAction.Result result = dmsObjectActionService.triggerAction(arg);

        log.info("save success : {}", result.getObjectData().getId());
        return result;
    }

    private IObjectData covertRebateDetail(String tenantId, IObjectData accountsReceivable, IObjectData rebate) {
        IObjectData data = new ObjectData();

        data.setTenantId(tenantId);
        data.setDescribeApiName(ApiNames.REBATE_DETAIL_OBJ);
        data.set(CommonFields.CREATE_BY, Lists.newArrayList("-10000"));
        data.set(CommonFields.OWNER, Lists.newArrayList("-10000"));
        data.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);

        data.set(RebateDetailFields.BIZ_STATUS, RebateDetailFields.BIZ_STATUS_EFFECTIVE);
        data.set(RebateDetailFields.USED_AMOUNT, rebate.get(RebateFields.SUM_AMOUNT));
        data.set(RebateDetailFields.OBJECT_DATA_ID, accountsReceivable.getId());
        data.set(RebateDetailFields.OBJECT_API_NAME, accountsReceivable.getDescribeApiName());
        data.set(RebateDetailFields.SOURCE_ID, rebate.getId());
        data.set(RebateDetailFields.SOURCE_OBJECT_API_NAME, rebate.getDescribeApiName());

        return data;
    }

    private IObjectData covertAccountsReceivable(String tenantId, IObjectData rebate) {
        IObjectData data = new ObjectData();

        data.setTenantId(tenantId);
        data.setDescribeApiName(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ);
        data.set(CommonFields.CREATE_BY, Lists.newArrayList("-10000"));
        data.set(CommonFields.OWNER, Lists.newArrayList("-10000"));
        data.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);
        long now = System.currentTimeMillis();
        data.set(AccountsReceivableNoteFields.ACCOUNT_ID, rebate.get(RebateFields.ACCOUNT_ID));
        data.set(AccountsReceivableNoteFields.CONTACT_OBJECT, AccountsReceivableNoteFields.CONTACT_OBJECT__AccountObj);
        data.set(AccountsReceivableNoteFields.OPENING_BALANCE, false);
        data.set(AccountsReceivableNoteFields.NOTE_DATE, now);
        data.set(AccountsReceivableNoteFields.DUE_DATE, now);
        data.set(AccountsReceivableNoteFields.SYSTEM_SOURCE, AccountsReceivableNoteFields.SYSTEM_SOURCE__saleSystem);
        data.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE, ApiNames.REBATE_OBJ);
        data.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_API_NAME, ApiNames.REBATE_OBJ);
        data.set(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID, rebate.getId());

        return data;
    }

    private List<IObjectData> covertAccountsReceivableDetails(String tenantId, IObjectData rebate) {

        IObjectData data = new ObjectData();
        data.setTenantId(tenantId);
        data.setDescribeApiName(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ);

        data.set(CommonFields.CREATE_BY, Lists.newArrayList("-10000"));
        data.set(CommonFields.OWNER, Lists.newArrayList("-10000"));
        data.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);
        data.set(CommonFields.LIFE_STATUS, CommonFields.LIFE_STATUS__NORMAL);

        data.set(AccountsPayableDetailFields.PRICE_TAX_AMOUNT, rebate.get(RebateFields.SUM_AMOUNT, BigDecimal.class).negate());

        return Lists.newArrayList(data);
    }

    private List<IObjectData> queryAccountsReceivableByObjectReceivable(String tenantId, String rebateId) {
        if (StringUtils.isEmpty(rebateId)) {
            return Lists.newArrayList();
        }

        IFilter objectReceivableFilter = new Filter();
        objectReceivableFilter.setFieldName(AccountsReceivableNoteFields.OBJECT_RECEIVABLE);
        objectReceivableFilter.setOperator(Operator.EQ);
        objectReceivableFilter.setFieldValues(Lists.newArrayList(ApiNames.REBATE_OBJ));

        IFilter idFilter = new Filter();
        idFilter.setFieldName(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(rebateId));


        IFilter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.REBATE_OBJ));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(objectReceivableFilter, idFilter, apiNameFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID)
        );
    }

    private BigDecimal calculateRebateUsedAmount(String tenantId, String rebateId) {
        BigDecimal amount = new BigDecimal("0");

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");
        query.setLimit(200);
        query.setOffset(0);

        Filter idFilter = new Filter();
        idFilter.setFieldName(RebateDetailFields.SOURCE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(rebateId));

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(RebateDetailFields.SOURCE_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.REBATE_OBJ));


        Filter bizStatusFilter = new Filter();
        bizStatusFilter.setFieldName(RebateDetailFields.BIZ_STATUS);
        bizStatusFilter.setOperator(Operator.EQ);
        bizStatusFilter.setFieldValues(Lists.newArrayList(RebateDetailFields.BIZ_STATUS_EFFECTIVE));

        Filter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
        isDeletedFilter.setOperator(Operator.EQ);
        isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));


        query.setFilters(Lists.newArrayList(idFilter, apiNameFilter, bizStatusFilter, isDeletedFilter, lifeStatusFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                ApiNames.REBATE_DETAIL_OBJ,
                Lists.newArrayList(RebateDetailFields.SOURCE_ID, RebateDetailFields.SOURCE_OBJECT_API_NAME),
                "sum",
                RebateDetailFields.USED_AMOUNT
        );

        if (!CollectionUtils.isEmpty(data)) {
            for (IObjectData datum : data) {
                BigDecimal sumAmount = datum.get("sum_" + RebateDetailFields.USED_AMOUNT, BigDecimal.class);
                if (!Objects.isNull(sumAmount)) {
                    amount = amount.add(sumAmount);
                }
            }

        }
        return amount;
    }

    private List<Map<String, String>> addObjectReceivableOptions(List<Map<String, String>> options) {
        List<Map<String, String>> newOptions = com.beust.jcommander.internal.Lists.newArrayList(options);
        newOptions.add(buildOption("出库单", "OutboundDeliveryNoteObj"));//ignorei18n
        return newOptions;
    }

    private Map<String, String> buildOption(String label, String value) {
        Map<String, String> option = Maps.newHashMap();
        option.put("label", label);
        option.put("value", value);
        return option;
    }
}
