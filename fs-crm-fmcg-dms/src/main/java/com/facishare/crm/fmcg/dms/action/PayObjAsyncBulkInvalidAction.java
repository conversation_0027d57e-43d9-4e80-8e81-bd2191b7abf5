package com.facishare.crm.fmcg.dms.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.MatchNoteFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAsyncBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class PayObjAsyncBulkInvalidAction extends StandardAsyncBulkInvalidAction {

    @Override
    protected void before(StandardBulkInvalidAction.Arg arg) {
        super.before(arg);
        validateExistsMatchNote();
    }

    private void validateExistsMatchNote() {

        log.info("PayObjAsyncBulkInvalidAction validateExistsMatchNote start");
        List<IObjectData> notes = matchNotes();
        if (CollectionUtils.isNotEmpty(notes)) {
            List<IObjectData> payObjs =
                    serviceFacade.findObjectDataByIds(actionContext.getTenantId(), notes.stream().map(obj -> obj.get(MatchNoteFields.CREDIT_DATA_ID, String.class)).collect(Collectors.toList()), ApiNames.PAY_OBJ);
            StringBuilder sb = new StringBuilder();
            for (IObjectData payObj : payObjs) {
                sb.append(payObj.getName()).append(",");
            }
            String names = sb.toString();
            String tips = names.substring(0, names.length() - 1);
            throw new ValidateException(String.format(I18N.text(I18NKeys.PAY_OBJ_ASYNC_BULK_INVALID_ACTION_0), tips));
        }

    }

    private List<IObjectData> matchNotes() {


        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        IFilter creditDataIdFilter = new Filter();
        creditDataIdFilter.setFieldName(MatchNoteFields.CREDIT_DATA_ID);
        creditDataIdFilter.setOperator(Operator.IN);
        creditDataIdFilter.setFieldValues(Lists.newArrayList(arg.getDataIds()));

        IFilter creditApiNameFilter = new Filter();
        creditApiNameFilter.setFieldName(MatchNoteFields.CREDIT_API_NAME);
        creditApiNameFilter.setOperator(Operator.EQ);
        creditApiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.PAY_OBJ));

        query.setFilters(Lists.newArrayList(creditDataIdFilter, creditApiNameFilter));

        return QueryDataUtil.find(
                serviceFacade,
                actionContext.getTenantId(),
                ApiNames.MATCH_NOTE_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        MatchNoteFields.CREDIT_DATA_ID
                )
        );

    }

}
