package com.facishare.crm.fmcg.dms.web.abstraction;

import com.facishare.crm.fmcg.dms.model.GetAccountsPeriodConfigObjectInformation;
import com.facishare.crm.fmcg.dms.model.SaveAccountsPeriodConfigArg;
import com.facishare.crm.fmcg.dms.model.SaveAccountsPeriodConfigResult;

public interface IAccountsPeriodConfigService {
    GetAccountsPeriodConfigObjectInformation.Result getAccountsPeriodConfigInitInformation(GetAccountsPeriodConfigObjectInformation.Arg arg);

    SaveAccountsPeriodConfigResult saveAccountsPeriodConfig(SaveAccountsPeriodConfigArg arg);

    SaveAccountsPeriodConfigResult getAccountsPeriodConfig(GetAccountsPeriodConfigObjectInformation.Arg arg);

}
