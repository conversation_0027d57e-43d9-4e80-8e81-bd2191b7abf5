package com.facishare.crm.fmcg.fesco.service;

import com.facishare.crm.fmcg.fesco.model.fesco.*;

public interface IFescoService {

    /**
     * get token by identity & pwd
     * api doc : <a href="http://dsm.feizhubaoxian.com:4999/web/#/58/779">Oauth2.0接入</a>
     *
     * @return token
     */
    String token();

    /**
     * create payment task
     * api doc : <a href="http://dsm.feizhubaoxian.com:4999/web/#/58/805">T1#创建任务</a>
     *
     * @param arg payment information
     * @return payment result
     */
    FescoCommonResult<CreateTask.TaskData> createTask(CreateTask.Arg arg);

    FescoCommonResult<CreateAsyncTask.TaskResultData> createAsyncTask2(CreateAsyncTask.Arg arg);

    /**
     * create payment task
     * api doc : <a href="http://dsm.feizhubaoxian.com:4999/web/#/50/680">T2#创建子任务</a>
     *
     * @param arg payment information
     * @return payment result
     */
    FescoCommonResult<CreateTaskDetail.TaskData> createTaskDetail(CreateTaskDetail.Arg arg);

    /**
     * user regist api
     * api doc : <a href="http://dsm.feizhubaoxian.com:4999/web/#/58/785">U1#用户标准认证-H5</a>
     *
     * @param arg user information
     * @return regist result
     */
    FescoCommonResult<String> regist(Regist.Arg arg);

    /**
     * create payment task
     * api doc : <a href="http://dsm.feizhubaoxian.com:4999/web/#/50/678">U4#查询用户认证状态</a>
     *
     * @param arg payment information
     * @return payment result
     */
    FescoCommonResult<GetUserAuth.UserAuthInfo> getUserAuth(GetUserAuth.Arg arg);

    /**
     * create payment task
     * api doc : <a href="http://dsm.feizhubaoxian.com:4999/web/#/58/791">U7#用户注销</a>
     *
     * @param arg payment information
     * @return payment result
     */
    FescoCommonResult<String> cancelUser(CancelUser.Arg arg);

    FescoCommonResult<String> cancelTask(CancelTask.Arg arg);

    FescoCommonResult<QueryTask.TaskData> queryTask(QueryTask.Arg arg);

    FescoCommonResult<TaskCallback.SubTask> taskCallBack(String task);

    FescoCommonResult<CreteAsyncCallBack.TaskData> asyncCreateTask(String task);
}