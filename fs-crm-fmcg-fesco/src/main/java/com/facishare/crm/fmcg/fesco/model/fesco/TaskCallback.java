package com.facishare.crm.fmcg.fesco.model.fesco;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

public interface TaskCallback {

    @Data
    @ToString
    final class Arg implements Serializable {

        private Integer code;

        private SubTask data;

        private Boolean success;

        private String message;

    }

    @Data
    @ToString
    final class SubTask implements Serializable {

        private int subTaskId;

        private String idCard;

        private String phone;

        private String name;

        private String cardNo;

        private BigDecimal money;

        private BigDecimal subServiceMoney;

        private String thirdSubId;

        private String state;

        private String message;

        private String paymentTime;

        private String bankStreamNo;

        private String bankName;
    }

    @Data
    @Builder
    @ToString
    final class Result implements Serializable {

        private String code;
    }
}
