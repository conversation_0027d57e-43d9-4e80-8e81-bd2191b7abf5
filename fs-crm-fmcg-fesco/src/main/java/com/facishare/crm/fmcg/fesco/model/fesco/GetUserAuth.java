package com.facishare.crm.fmcg.fesco.model.fesco;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/27 11:07
 */
public interface GetUserAuth {

    @Data
    @ToString
    final class Arg implements Serializable {

        private String name;

        private String phone;

        private String idCard;

        private String idCode;
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    final class Result extends FescoCommonResult<UserAuthInfo> {

    }

    @Data
    @ToString
    final class UserAuthInfo implements Serializable {

        private String name;

        private String idCard;

        private String phone;

        private String bankCardNo;

        private Integer bankId;

        private String bankName;

        private String address;

        private String idCardFrontFile;
        
        private String idCardBackFile;
        
        private String idCardStartDate;
        
        private String idCardEndDate;

        private Integer auth;
        
        private Integer agreement;
        
        private Integer idCode;
    }
}
