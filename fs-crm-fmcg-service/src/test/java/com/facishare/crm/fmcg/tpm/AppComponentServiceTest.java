package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.ActivitySimpleList;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTypeReport;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IAppComponentService;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/22 16:29
 */
public class AppComponentServiceTest extends BaseTest {

    @Resource
    private IAppComponentService appComponentService;

    @Test
    public void activityTypeReportTest() {
        ApiContext context = ApiContextManager.getContext();
        context.setTenantId("84931");

        ActivityTypeReport.Arg arg = new ActivityTypeReport.Arg();
        arg.setId("63edcfc4ff48280001912992");
        arg.setNeedReturnActivityReport(true);
        ActivityTypeReport.Result result = appComponentService.activityTypeReport(arg);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.getReportData().size() >= 1);
    }

    @Test
    public void activityTypeSimpleListTest() {
        ActivitySimpleList.Arg arg = new ActivitySimpleList.Arg();

        arg.setNeedReturnFirstReport(true);
        arg.setNeedReturnActivityPlanReport(true);

        ActivitySimpleList.Result result = appComponentService.activityTypeSimpleList(arg);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.getReportData().size() >= 1);
    }
}
