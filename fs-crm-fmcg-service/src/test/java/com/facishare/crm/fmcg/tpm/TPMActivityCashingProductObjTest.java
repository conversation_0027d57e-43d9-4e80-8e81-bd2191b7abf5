package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.controller.TPMActivityCashingProductObjRelatedListController;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Map;


public class TPMActivityCashingProductObjTest extends BaseTest {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private InfraServiceFacade infraServiceFacade;

    @Test
    public void RelatedListTest() {
        TPMActivityCashingProductObjRelatedListController controller = new TPMActivityCashingProductObjRelatedListController();
        controller.setServiceFacade(serviceFacade);
        controller.setInfraServiceFacade(infraServiceFacade);


        String aa = "{\"serializeEmpty\":false,\"extractExtendInfo\":true,\"associated_object_describe_api_name\":\"ProductObj\",\"associated_object_field_related_list_name\":\"target_related_list_TPMActivityAgreementCashingProduct_ProductObj__c\",\"search_query_info\":\"{\\\"limit\\\":20,\\\"offset\\\":0,\\\"filters\\\":[],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}],\\\"wheres\\\":[]}\",\"search_template_id\":\"5d6e7189946ff719b4040132\",\"search_template_type\":\"default\",\"object_data\":{\"activity_id\":\"66068bc4984c81000181cc92\",\"activity_id__r\":\"没有方案-全部兑付产品\",\"dealer_id\":\"64b7a1c7ff8e0100013d08d8\",\"dealer_id__r\":\"海淀经销商\",\"store_id\":\"64b7a1c7ff8e0100013d08d8\",\"store_id__r\":\"海淀经销商\",\"record_type\":\"default__c\",\"object_describe_api_name\":\"TPMActivityAgreementCashingProductObj\",\"rowId\":\"17117071947141224\",\"object_describe_id\":\"63f57829b9367d0001767c76\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\"},\"ignore_scene_record_type\":false,\"trigger_info\":{\"trigger_page\":\"Add\"},\"include_describe\":false,\"master_data\":{\"activity_id\":\"66068bc4984c81000181cc92\",\"activity_id__r\":\"没有方案-全部兑付产品\",\"record_type\":\"default__c\",\"activity_type\":\"66066b8d8d15880001cba14c\",\"dealer_cashing_type\":\"goods\",\"object_describe_api_name\":\"TPMActivityAgreementObj\",\"created_by\":[\"1001\"],\"owner\":[\"1001\"],\"data_own_department\":[\"1157\"],\"data_own_department__r\":\"北京大区\",\"agreement_cashing_type\":\"goods\",\"field_pmgpj__c\":\"1\",\"audit_status__c\":\"schedule\",\"total\":\"0.00\",\"end_date\":1711814400000,\"actual_total_amount\":\"0.00\",\"begin_date\":1711641600000,\"field_jUkZ4__c__r\":\"#%$\",\"activity_type__c\":\"\",\"field_jUkZ4__c__v\":\"66066b8d8d15880001cba14c\",\"activity_type__c__r\":\"#%$\",\"field_jUkZ4__c\":\"\",\"activity_type__c__v\":\"66066b8d8d15880001cba14c\",\"store_id__r\":\"海淀经销商\",\"store_id\":\"64b7a1c7ff8e0100013d08d8\",\"dealer_id__r\":\"海淀经销商\",\"dealer_id\":\"64b7a1c7ff8e0100013d08d8\"},\"details\":{\"TPMActivityAgreementDetailObj\":[],\"TPMActivityAgreementCashingProductObj\":[{\"activity_id\":\"66068bc4984c81000181cc92\",\"activity_id__r\":\"没有方案-全部兑付产品\",\"dealer_id\":\"64b7a1c7ff8e0100013d08d8\",\"dealer_id__r\":\"海淀经销商\",\"store_id\":\"64b7a1c7ff8e0100013d08d8\",\"store_id__r\":\"海淀经销商\",\"record_type\":\"default__c\",\"object_describe_api_name\":\"TPMActivityAgreementCashingProductObj\",\"rowId\":\"17117071947141224\",\"object_describe_id\":\"63f57829b9367d0001767c76\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\"}]},\"maskFieldApiNames\":{\"TPMActivityAgreementObj\":[]}}";
        StandardRelatedListController.Arg arg = JSON.parseObject(aa, TPMActivityCashingProductObjRelatedListController.Arg.class);
        String master = "{\"activity_id\":\"660696f9984c8100018273a2\",\"activity_id__r\":\"没有方案-全部\",\"record_type\":\"default__c\",\"activity_type\":\"66066b8d8d15880001cba14c\",\"dealer_cashing_type\":\"goods\",\"object_describe_api_name\":\"TPMActivityAgreementObj\",\"created_by\":[\"1001\"],\"owner\":[\"1001\"],\"data_own_department\":[\"1157\"],\"data_own_department__r\":\"北京大区\",\"agreement_cashing_type\":\"goods\",\"field_pmgpj__c\":\"1\",\"audit_status__c\":\"schedule\",\"total\":\"0.00\",\"end_date\":1711814400000,\"actual_total_amount\":\"0.00\",\"begin_date\":1711641600000,\"field_jUkZ4__c__r\":\"#%$\",\"activity_type__c\":\"\",\"field_jUkZ4__c__v\":\"66066b8d8d15880001cba14c\",\"activity_type__c__r\":\"#%$\",\"field_jUkZ4__c\":\"\",\"activity_type__c__v\":\"66066b8d8d15880001cba14c\",\"store_id__r\":\"海淀经销商\",\"store_id\":\"64b7a1c7ff8e0100013d08d8\",\"dealer_id__r\":\"海淀经销商\",\"dealer_id\":\"64b7a1c7ff8e0100013d08d8\"}";

        String obj = "{\"activity_id\":\"660696f9984c8100018273a2\",\"activity_id__r\":\"没有方案-全部\",\"dealer_id\":\"64b7a1c7ff8e0100013d08d8\",\"dealer_id__r\":\"海淀经销商\",\"store_id\":\"64b7a1c7ff8e0100013d08d8\",\"store_id__r\":\"海淀经销商\",\"record_type\":\"default__c\",\"object_describe_api_name\":\"TPMActivityAgreementCashingProductObj\",\"rowId\":\"17117123105252423\",\"object_describe_id\":\"63f57829b9367d0001767c76\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\"}";

        ObjectDataDocument objectDataDocument = new ObjectDataDocument();
        objectDataDocument.putAll(JSON.parseObject(master, Map.class));
        arg.setMasterData(objectDataDocument);

        ObjectDataDocument objectDataDocument2 = new ObjectDataDocument();
        objectDataDocument2.putAll(JSON.parseObject(obj, Map.class));
        arg.setObjectData(objectDataDocument2);

        arg.setSearchQueryInfo("{\"limit\":20,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}],\"wheres\":[]}");

        controller.setArg(arg);

        RequestContext requestContext = RequestContext.builder()
                .tenantId("84931")
                .ea("84931")
                .user(User.systemUser("84931"))
                .build();
        controller.setControllerContext(new ControllerContext(requestContext, "TPMActivityCashingProductObj", "RelatedList"));

        RequestContextManager.setContext(requestContext);

        TPMActivityCashingProductObjRelatedListController.Result service = controller.service(arg);
        assert service != null;
    }


}
