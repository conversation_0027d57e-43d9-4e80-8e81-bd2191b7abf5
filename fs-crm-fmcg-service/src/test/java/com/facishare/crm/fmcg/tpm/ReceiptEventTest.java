package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.mengniu.handler.SalesEventDistributor;
//import com.facishare.crm.fmcg.mengniu.service.ConsumerScanCodeRewardService;
import com.facishare.crm.fmcg.tpm.reward.dto.ConsumerReward;
import org.junit.Test;

import javax.annotation.Resource;

public class ReceiptEventTest extends BaseTest {

    @Resource
    private SalesEventDistributor salesEventDistributor;

//    @Resource
//    private ConsumerScanCodeRewardService consumerScanCodeRewardService;

    @Test
    public void consumerScanCodeRewardTest() {
        ConsumerReward.Arg arg = new ConsumerReward.Arg();
        arg.setToken("local");
        arg.setCode("133000000000000090");
        arg.setAppId("wxf70ac4798732b66f");

//        ConsumerReward.Result result = consumerScanCodeRewardService.reward(arg);

//        assert result != null;
    }

    @Test
    public void invokeTest() {
        JSONObject event = new JSONObject();
        event.put("event_id", "SIGN_IN_GOODS.64d1f9e8537bf6000111fa9c");
        event.put("event_time", System.currentTimeMillis());
        event.put("event_type", "SIGN_IN_GOODS");
        event.put("tenant_id", "89150");
        event.put("user_id", "-10000");

        JSONObject data = new JSONObject();
        data.put("delivery_note_id", "64d1f9e8537bf6000111fa9c");

        event.put("data", data);

        salesEventDistributor.process(event);
    }
}
