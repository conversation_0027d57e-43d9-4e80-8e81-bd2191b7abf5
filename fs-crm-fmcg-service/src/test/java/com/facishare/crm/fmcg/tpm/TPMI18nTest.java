package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.service.TPMI18nService;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/7/11 下午5:37
 */
public class TPMI18nTest extends BaseTest {

    @Resource
    private TPMI18nService tpmi18nService;

    private static final String[] API_NAMES = new String[39];

    static {
        API_NAMES[0] = "TPMBudgetBusinessSubjectObj";
        API_NAMES[1] = "TPMBudgetStatisticTableObj";
        API_NAMES[2] = "TPMBudgetAccountObj";
        API_NAMES[3] = "TPMBudgetAccountDetailObj";
        API_NAMES[4] = "TPMBudgetTransferDetailObj";
        API_NAMES[5] = "TPMBudgetOccupationDetailObj";
        API_NAMES[6] = "TPMBudgetCarryForwardObj";
        API_NAMES[7] = "TPMBudgetCarryForwardDetailObj";
        API_NAMES[8] = "TPMBudgetDisassemblyObj";
        API_NAMES[9] = "TPMBudgetDisassemblyNewDetailObj";
        API_NAMES[10] = "TPMBudgetDisassemblyExistsDetailObj";
        API_NAMES[11] = "TPMBudgetAccrualObj";
        API_NAMES[12] = "TPMBudgetAccrualDetailObj";

        API_NAMES[13] = "TPMActivityItemObj";
        API_NAMES[14] = "TPMActivityItemCostStandardObj";
        API_NAMES[15] = "TPMActivityUnifiedCaseObj";
        API_NAMES[16] = "TPMActivityDealerScopeObj";
        API_NAMES[17] = "TPMActivityCashingProductScopeObj";
        API_NAMES[18] = "TPMActivityObj";
        API_NAMES[19] = "TPMActivityDetailObj";
        API_NAMES[20] = "TPMActivityStoreObj";
        API_NAMES[21] = "TPMActivityAgreementObj";
        API_NAMES[22] = "TPMActivityAgreementDetailObj";
        API_NAMES[23] = "TPMDealerActivityCostObj";
        API_NAMES[24] = "TPMStoreWriteOffObj";
        API_NAMES[25] = "TPMActivityProofObj";
        API_NAMES[26] = "TPMActivityProofDetailObj";
        API_NAMES[27] = "TPMActivityProofAuditObj";
        API_NAMES[28] = "TPMActivityProofAuditDetailObj";
        API_NAMES[29] = "TPMActivityCashingProductObj";
        API_NAMES[30] = "TPMActivityAgreementCashingProductObj";
        API_NAMES[31] = "TPMStoreWriteOffCashingProductObj";
        API_NAMES[32] = "TPMDealerActivityCashingProductObj";
        API_NAMES[33] = "TPMActivityUnifiedCaseProductRangeObj";
        API_NAMES[34] = "TPMActivityProductRangeObj";
        API_NAMES[35] = "TPMActivityVenueObj";
        API_NAMES[36] = "TPMActivityMaterialObj";
        API_NAMES[37] = "ExpenseClaimFormObj";
        API_NAMES[38] = "ExpenseClaimFormDetailObj";
    }

    @Test
    public void testSaveOfApiName() {
        tpmi18nService.saveOfApiName(84931L, API_NAMES);
    }

    @Test
    public void testExImportReferenceByApiNames() {
        tpmi18nService.exImportReferenceByApiNames(84931L, API_NAMES);
    }

    @Test
    public void testExImportLayoutByApiNames() {
        tpmi18nService.exImportLayoutByApiNames(84931L, API_NAMES);
    }
}
