package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;

/**
 * author: wuyx
 * description:
 * createTime: 2022/7/5 19:16
 */

public class AutomationTest extends BaseTest {

    @Test
    public  void getCurlTest(){
        String curl = "curl \"https://crm.ceshi112.com/FHH/EM1HNCRM/API/v1/object/TPMActivityItemObj/action/Add?_postid=O-E.84846.1000-1657016348855&_fs_token=CMPaPJGrEJ8jCJamPYqqCs5YBJXaOcOjE3aoPcCmCZaqDJ0t&traceId=E-E.84846.1000-1657016416366\" -X POST -H \"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:102.0) Gecko/20100101 Firefox/102.0\" -H \"Accept: application/json, text/javascript, */*; q=0.01\" -H \"Accept-Language: zh-CN,zh-TW;0.9,en;0.8\" -H \"Accept-Encoding: gzip, deflate, br\" -H \"Content-Module: application/json; charset=utf-8\" -H \"x-trace-id: 84846_1000_1657002490797:1077\" -H \"X-Requested-With: XMLHttpRequest\" -H \"Origin: https://crm.ceshi112.com\" -H \"Connection: keep-alive\" -H \"Referer: https://crm.ceshi112.com/XV/UI/Home\" -H \"Cookie: guid=e6a5d43e-e109-f18a-7b10-417ba4e2360d; fs_token=CMPaPJGrEJ8jCJamPYqqCs5YBJXaOcOjE3aoPcCmCZaqDJ0t; FSAuthXC=0G60i5q5y840001JOparCbL2Gns380wzuSotfDR2RC1NdrJzzIabu0mnYlKHGsECmmwY4rB8d6Kd16d4MmWE194OLumwEFOVfTuzWz2zKxOpZk5r7zSLhlmgiW2Jl9O0ZkskvPkxkpYwlPHddHSXcsg0kjPmha7WhOFi5DTHF7jknjAphbmDJ1mutwOf3VoDkJyE1K8N9p2hlSZ3uMhwFZyTumtKOZ6wj1w5zO; sso_token=6ae7cd33-dbe2-4a2c-889a-e61ef037bab9; sensorsdata2015jssdkcross=\"%\"7B\"%\"22distinct_id\"%\"22\"%\"3A\"%\"2217bc33f93f666e-0e7ba1a854a1b3-4c3e247b-1327104-17bc33f93f78c9\"%\"22\"%\"2C\"%\"22\"%\"24device_id\"%\"22\"%\"3A\"%\"2217bc33f93f666e-0e7ba1a854a1b3-4c3e247b-1327104-17bc33f93f78c9\"%\"22\"%\"2C\"%\"22props\"%\"22\"%\"3A\"%\"7B\"%\"22\"%\"24latest_traffic_source_type\"%\"22\"%\"3A\"%\"22\"%\"E7\"%\"9B\"%\"B4\"%\"E6\"%\"8E\"%\"A5\"%\"E6\"%\"B5\"%\"81\"%\"E9\"%\"87\"%\"8F\"%\"22\"%\"2C\"%\"22\"%\"24latest_referrer\"%\"22\"%\"3A\"%\"22\"%\"22\"%\"2C\"%\"22\"%\"24latest_referrer_host\"%\"22\"%\"3A\"%\"22\"%\"22\"%\"2C\"%\"22\"%\"24latest_search_keyword\"%\"22\"%\"3A\"%\"22\"%\"E6\"%\"9C\"%\"AA\"%\"E5\"%\"8F\"%\"96\"%\"E5\"%\"88\"%\"B0\"%\"E5\"%\"80\"%\"BC_\"%\"E7\"%\"9B\"%\"B4\"%\"E6\"%\"8E\"%\"A5\"%\"E6\"%\"89\"%\"93\"%\"E5\"%\"BC\"%\"80\"%\"22\"%\"7D\"%\"7D; Hm_lvt_06d5233541e92feb3cc8980700b1efa6=1636600072,1637897264; hy_data_2020_id=17d0cf74a2e89-04c51dd6978262-4c3e217e-1327104-17d0cf74a2f428; hy_data_2020_js_sdk=\"%\"7B\"%\"22distinct_id\"%\"22\"%\"3A\"%\"2217d0cf74a2e89-04c51dd6978262-4c3e217e-1327104-17d0cf74a2f428\"%\"22\"%\"2C\"%\"22site_id\"%\"22\"%\"3A478\"%\"2C\"%\"22user_company\"%\"22\"%\"3A409\"%\"2C\"%\"22props\"%\"22\"%\"3A\"%\"7B\"%\"7D\"%\"2C\"%\"22device_id\"%\"22\"%\"3A\"%\"2217d0cf74a2e89-04c51dd6978262-4c3e217e-1327104-17d0cf74a2f428\"%\"22\"%\"7D; FSAuthX=0G60i5q5y840001JOparCbL2Gns380wzuSotfDR2RC1NdrJzzIabu0mnYlKHGsECmmwY4rB8d6Kd16d4MmWE194OLumwEFOVfTuzWz2zKxOpZk5r7zSLhlmgiW2Jl9O0ZkskvPkxkpYwlPHddHSXcsg0kjPmha7WhOFi5DTHF7jknjAphbmDJ1mutwOf3VoDkJyE1K8N9p2hlSZ3uMhwFZyTumtKOZ6wj1w5zO; mirrorId=0000; EPXId=4fcf47fc066943a094efd87daa541740; enterprise=84846; JSESSIONID=5B1E1A3C9FCBB3F20778892CE74A8BA0; lang=zh-CN; originRefer=\" -H \"Sec-Fetch-Dest: empty\" -H \"Sec-Fetch-Mode: cors\" -H \"Sec-Fetch-Site: same-origin\" -H \"TE: trailers\" --data-raw \"{\"\"object_data\"\":{\"\"object_describe_api_name\"\":\"\"TPMActivityItemObj\"\",\"\"record_type\"\":\"\"default__c\"\",\"\"created_by\"\":[\"\"1000\"\"],\"\"owner\"\":[\"\"1000\"\"],\"\"data_own_department\"\":[\"\"1000\"\"],\"\"data_own_department__r\"\":\"\"待分配\"\",\"\"calculate_pattern\"\":\"\"1\"\",\"\"amount_standard_check\"\":false,\"\"unit\"\":\"\"box\"\",\"\"is_activated\"\":true,\"\"cost_standard\"\":\"\"10\"\",\"\"calculate_pattern__o\"\":\"\"\"\",\"\"unit__o\"\":\"\"\"\",\"\"object_describe_id\"\":\"\"628eef49399c4f00012c53ba\"\",\"\"name\"\":\"\"wuyx测试002\"\",\"\"amount_standard\"\":\"\"10\"\",\"\"remark\"\":\"\"\"\",\"\"requestId\"\":\"\"e65054f06a5240fc99c039f335c753e0\"\"}}\"";

        int startIndex = curl.indexOf("crm.ceshi112.com");
        int endIndex = curl.indexOf("_postid");
        String url = curl.substring(startIndex + "crm.ceshi112.com".length(), endIndex).trim();

        String method = curl.contains("POST") ? "POST" :"GET";

        int bodyIndex = curl.indexOf("--data-raw") + "--data-raw".length();
        String body = curl.substring(bodyIndex).trim();


        System.out.println(url);
        System.out.println(method);
        System.out.println(JSON.parseObject(body));

        JSONObject json = new JSONObject();
        json.put("url",url+"_fs_token=");



        StringBuffer stringBuffer = new StringBuffer();

        stringBuffer.append("var api = { \"url\":\"").append(url);
    }

}
