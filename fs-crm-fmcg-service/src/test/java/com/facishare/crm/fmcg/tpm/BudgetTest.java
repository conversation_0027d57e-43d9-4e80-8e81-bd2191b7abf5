package com.facishare.crm.fmcg.tpm;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetOccupationDetailFields;
import com.facishare.crm.fmcg.tpm.business.BudgetAccountService;
import com.facishare.crm.fmcg.tpm.business.BudgetCalculateService;
import com.facishare.crm.fmcg.tpm.business.RedisLockService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetActivitySubjectService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetConsumeService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.paas.BudgetAccountDetailMapper;
import com.facishare.crm.fmcg.tpm.dao.paas.BudgetOccupationDetailMapper;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IBudgetPresetDataService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/11 下午5:37
 */
public class BudgetTest extends BaseTest {


    @Resource
    private BudgetOccupationDetailMapper budgetOccupationDetailMapper;

    @Resource
    private BudgetAccountDetailMapper budgetAccountDetailMapper;

    @Resource
    private IBudgetActivitySubjectService budgetActivitySubjectService;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private BudgetCalculateService budgetCalculateService;

    @Resource
    private IBudgetConsumeService budgetConsumeService;

    @Resource
    private IBudgetAccountDetailService budgetAccountDetailService;

    @Resource
    private BudgetTypeDAO budgetTypeDAO;

    @Resource
    private BudgetAccountService budgetAccountService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private IBudgetPresetDataService budgetPresetDataService;

    @Test
    public void testMapper() {
        System.out.println(budgetOccupationDetailMapper.setTenantId("84931").statisticOccupiedMoney("84931", "123"));
    }

    @Test
    public void testMappe2r() {
        List<Map<String, Object>> data = new ArrayList<>();
        Map<String, Object> tmp = new HashMap<>();
        tmp.put("_id", "62f9f1183c633b00018f10d9");
        tmp.put("last_modified_time", System.currentTimeMillis());
        tmp.put("tenant_id", "84931");
        tmp.put(TPMBudgetOccupationDetailFields.OCCUPIED_STATUS, TPMBudgetOccupationDetailFields.OCCUPIED_STATUS_RELEASE);
        data.add(tmp);
        Map<String, Object> tmp2 = new HashMap<>();
        tmp2.put("_id", "62f9f1153c633b00018f1059");
        tmp2.put("last_modified_time", System.currentTimeMillis());
        tmp2.put("tenant_id", "84931");
        tmp2.put(TPMBudgetOccupationDetailFields.OCCUPIED_STATUS, TPMBudgetOccupationDetailFields.OCCUPIED_STATUS_RELEASE);
        data.add(tmp2);
        budgetOccupationDetailMapper.setTenantId("84931").updateOccupationStatus("84931", data);
    }

    @Test
    public void testStatisticMoney() {

        System.out.println(JSON.toJSONString(budgetAccountDetailMapper.setTenantId("84931").statisticMoney("84931", "1")));
    }

    @Test
    public void testStatisticAvailableMoney() {

        System.out.println(JSON.toJSONString(budgetAccountDetailMapper.setTenantId("84931").batchGetBudgetAvailableAmount("84931", Lists.newArrayList("62fef9c23ece5e000185afa8", "62fe04478f56ca0001ee46bb"))));
    }

    @Test
    public void testLevel() {
        System.out.println(budgetActivitySubjectService.getSubjectLevel(User.systemUser("84931"), "62d4f960c487ab00010379b2"));
    }

    @Test
    public void testRedis() {
        redisLockService.unLock("CONSUME:LOCK:PREFIX:BUDGET CONSUME RULE:6541bdbede61310001d60554:b967bdf4-6d", "a7a339a6-6e0c-4438-a523-52305357676c");
    }

    @Test
    public void testCalculate() {
        budgetCalculateService.recalculateBudgetAmount(User.systemUser("84931"), "62d62bf80f2537000111f603");
    }

    @Test
    public void testStatictis() {
        JSON.toJSONString(budgetAccountDetailMapper.staticDetailAmountByRelatedData("84931", "master__c", "62f4ca3dc9f0b20001e9a280"));
    }

    @Test
    public void testAddField() {
        budgetConsumeService.addBudgetChangeDetailToBudget(User.systemUser("84931"), "master__c");
    }

    @Test
    public void testQueryFrozen() {
        System.out.println(JSON.toJSONString(budgetAccountDetailService.queryFrozenDetailByRelateObjectWithoutUnfreeze(User.systemUser("84931"), "object_R49Vf__c", "63047e5a1677d500012f05ef")));
    }

    @Test
    public void testNode() {
        System.out.println(JSON.toJSONString(budgetTypeDAO.getNodeById("84931", "63075484066d800001b021b2")));
    }

    @Test
    public void testAddAccount() {
        IObjectData account = new ObjectData(JSON.parseObject("{\"object_describe_api_name\":\"TPMBudgetAccountObj\",\"record_type\":\"default__c\",\"created_by\":[\"1003\"],\"owner\":[\"1003\"],\"data_own_department\":[\"1004\"],\"data_own_department__r\":\"消费规则部门\",\"transfer_in_amount\":\"0.00\",\"base_amount\":\"100\",\"transfer_out_amount\":\"0.00\",\"budget_status\":\"enable\",\"frozen_amount\":\"0.00\",\"used_amount\":\"0.00\",\"statistic_department_amount\":\"0.00\",\"calculate_apply_amount\":\"0.00\",\"total_amount\":\"100.00\",\"available_amount\":\"100.00\",\"budget_type_id__o\":\"\",\"budget_node_id__o\":\"\",\"budget_template_id__o\":\"\",\"budget_status__o\":\"\",\"object_describe_id\":\"62fcc4a2516b8d000159b548\",\"name\":\"消费22\",\"budget_department\":[\"1005\"],\"parent_id\":\"630de745a910780001ed6a9d\",\"product_category_id\":\"62a0168d25b0460001549f41\",\"budget_subject_id\":\"6307140cba20b40001e33327\",\"budget_period_month\":*************,\"budget_type_id\":\"630ca933142d080627d4532b\",\"budget_node_id\":\"630ca9333f2d8700012fdb5e\",\"budget_template_id\":\"630ca902142d080627d4532a\",\"requestId\":\"aedc7e61d4344443837521e9144ea942\"}"));
        System.out.println(JSON.toJSONString(budgetAccountService.createBudgetAccount(User.systemUser("84931"), account, false, false, true,false)));
    }

    @Test
    public void testCo() {
        IObjectData budget = serviceFacade.findObjectData(User.systemUser("84931"), "630de745a910780001ed6a9d", ApiNames.TPM_BUDGET_ACCOUNT);
        budgetAccountService.queryRelatedBudgetByControlDimension(User.systemUser("84931"), budget.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class), budget);
    }


    @Test
    public void testAccount() {
        String tenantId = "84931";
        IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), "6311c78937b86f00016db592", ApiNames.TPM_BUDGET_ACCOUNT);
        budgetAccountService.preValidateAccount(User.systemUser(tenantId), budget, false);
    }

    @Test
    public void testPresetTPMBudgetBusinessSubjectData() {
        budgetPresetDataService.presetTPMBudgetBusinessSubjectData();
    }

}
