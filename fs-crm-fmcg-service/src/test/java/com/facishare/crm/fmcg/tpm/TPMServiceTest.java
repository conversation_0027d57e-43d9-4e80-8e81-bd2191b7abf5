package com.facishare.crm.fmcg.tpm;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.common.apiname.TPMStoreWriteOffFields;
import com.facishare.crm.fmcg.mengniu.api.FillActivityItem;
import com.facishare.crm.fmcg.mengniu.api.FillProductRangeData;
import com.facishare.crm.fmcg.mengniu.handler.NewRewardService;
import com.facishare.crm.fmcg.mengniu.service.IMengNiuRefreshDataService;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.CrmAuditLogService;
import com.facishare.crm.fmcg.tpm.business.PresetRedPacketService;
import com.facishare.crm.fmcg.tpm.business.StoreCostService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetProvisionService;
import com.facishare.crm.fmcg.tpm.business.dto.CrmAuditLogDTO;
import com.facishare.crm.fmcg.tpm.business.enums.ActivityTemplateI18nEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityStoreWriteOffSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.reward.dto.CustomerPublish;
import com.facishare.crm.fmcg.tpm.service.abstraction.BudgetConsumeFlowService;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.service.abstraction.PluginInstanceService;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.*;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.BudgetAccrualRuleManager;
import com.facishare.crm.fmcg.tpm.web.service.ActivityUnifiedCaseService;
import com.facishare.crm.fmcg.tpm.web.service.BudgetAccrualRuleService;
import com.facishare.crm.fmcg.tpm.web.service.BudgetNewConsumeRuleService;
import com.facishare.crm.fmcg.tpm.web.tools.TPMDescribeService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IStoreWriteOffService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IStoreWriteOffV2Service;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ValidateRuleService;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.text.StringSubstitutor;
import org.apache.curator.shaded.com.google.common.collect.ImmutableMap;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/28 上午11:51
 */
public class TPMServiceTest extends BaseTest {

    @Autowired
    private ServiceFacade serviceFacade;

    @Resource
    private SpecialTableMapper specialTableMapper;

    @Resource
    private BudgetService budgetService;

    @Resource
    private StoreCostService storeCostService;

    @Resource
    private IStoreWriteOffService storeWriteOffService;

    @Resource
    private IStoreWriteOffV2Service storeWriteOffV2Service;

    @Resource
    private ActivityTypeManager activityTypeManager;


//    @Resource
//    private BudgetOldConsumeRuleManager budgetConsumeRuleManager;

    @Resource
    private BudgetAccrualRuleManager budgetAccrualRuleManager;


    @Resource
    private BudgetNewConsumeRuleService budgetNewConsumeRuleService;

    @Resource
    private BudgetAccrualRuleService budgetAccrualRuleService;

    @Resource
    private BudgetConsumeFlowService budgetConsumeFlowService;

    @Resource
    private CrmAuditLogService crmAuditLogService;

    @Resource(name = "describeLogicService")
    private DescribeLogicService describeService;

    @Resource
    private TPMDescribeService tpmDescribeService;

    @Resource
    private ActivityUnifiedCaseService activityUnifiedCaseService;

    @Resource
    private PluginInstanceService pluginInstanceService;

    @Resource
    private IMengNiuRefreshDataService mengNiuRefreshDataService;

    @Resource
    private PresetRedPacketService presetRedPacketService;
    @Resource
    protected ValidateRuleService validateRuleService;
    @Resource
    private IBudgetProvisionService budgetProvisionService;

    @Resource
    private NewRewardService<Serializable> newRewardService;


    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

    @Test
    public void testDescribe(){
        User user = User.systemUser("82958");
        RequestContext requestContext = RequestContext.builder().tenantId("82958").user(user).lang(Lang.en).build();
        RequestContextManager.setContext(requestContext);

        IObjectDescribe object = serviceFacade.findObject("82958", ApiNames.TPM_ACTIVITY_OBJ);
        System.out.println(object);
    }


    @Test
    public void timeTest(){
        long start = 1709136000000L;
        long end = 1740758399000L;

        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime startDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(start), zoneId);
        LocalDateTime endDateLimit = startDate.plusYears(1);
        boolean result = endDateLimit.atZone(zoneId).toInstant().toEpochMilli() >= end;
        System.out.println(result);

        boolean b = LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault()).plusYears(1).toInstant(ZoneOffset.of("+8")).toEpochMilli() >= end;
        System.out.println(b);

        LocalDateTime startDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault());
        LocalDateTime endLimit = startDateTime.plusYears(1).plusDays(1).minusNanos(1000000);
        boolean b1 = endLimit.toInstant(ZoneOffset.of("+8")).toEpochMilli() >= end;
        System.out.println(b1);
    }

    @Test
    public void presetRedPacketTest(){
        presetRedPacketService.presetRedPacket("89150", "65fd70a03980c5000184089b,65fbd2ee4d0a6000010bc4a9,65fbd2ed4d0a6000010bc3ef", "all");
    }

    @Test
    public void customRedPacket(){
        CustomerPublish.Arg arg = new CustomerPublish.Arg();
        arg.setTenantId("82958");
        arg.setApiName("red_packet_distribution__c");
        arg.setDataId("67bc5adcbad05500075c01c1");

        newRewardService.customerPublishRedPacket(arg);
    }

    @Test
    public void testGetDelete() {
        System.out.println(JSON.toJSONString(serviceFacade.findObjectDataIncludeDeleted(User.systemUser("84931"), "64745b21de0ff2000198288d", ApiNames.TPM_DEALER_ACTIVITY_COST)));
    }

    @Test
    public void testI18n() throws IOException {
        String json = "";
        File file = ResourceUtils.getFile("classpath:tpm/module_activity_type_template/activity_type_template.json");
        json = new String(Files.readAllBytes(file.toPath()));

        StringSubstitutor sub = new StringSubstitutor(ActivityTemplateI18nEnum.getInnerMap());
        json = sub.replace(json);

        System.out.println(json);
    }

    @Test
    public void testGetDeleted() {
/*        List<Map> list = (specialTableMapper.setTenantId("78582")).findBySql("select id,is_deleted from fmcg_tpm_activity where id = '613acff2e991c10001e37892'");
        System.out.println(JSON.toJSONString(list));
        if (!CollectionUtils.isEmpty(list)) {
            for (Map m : list) {
                System.out.println(m.toString());
            }
        }*/
    }

    @Test
    public void testCalculate() {
        budgetService.calculateBudget("83150", "61cab4512bd15900018a95e1");
    }

    @Test
    public void describe() {
//        tpmDescribeService.updateObjectDesc("85494", ApiNames.TPM_ACTIVITY_OBJ, "");
        //tpmDescribeService.updateUnifiedCaseObjectDesc(tenantIds, "");
        //      pluginService.addPluginUnit(Integer.valueOf(tenantId), -10000, apiName, PLUGIN_NAME);

        Map<String, List<String>> account = new HashMap<>();
        account.put(ApiNames.ACCOUNT_OBJ, Lists.newArrayList("6536274264d249000118693d"));

        Map<String, Map<String, List<RelevantTeam>>> stringMapMap = serviceFacade.batchFindTeamMember("89646", account);
        List<String> reversedList = stringMapMap.get(ApiNames.ACCOUNT_OBJ).get("6536274264d249000118693d").stream()
                .filter(v -> Objects.isNull(v.getSourceType()))
                .sorted(Comparator.comparing(RelevantTeam::getRoleType).reversed())
                .map(RelevantTeam::getMemberId)
                .collect(Collectors.toList());
        System.out.println(reversedList);

        List<String> list = stringMapMap.get(ApiNames.ACCOUNT_OBJ).get("6536274264d249000118693d").stream()
                .filter(v -> Objects.isNull(v.getSourceType()))
                .map(RelevantTeam::getMemberId)
                .collect(Collectors.toList());
        System.out.println(list);
    }

    @Test
    public void unifiedCaseTest() {
        ApiContext context = ApiContextManager.getContext();
        context.setTenantId("85494");

        ActivityUnifiedCaseData.Arg arg = new ActivityUnifiedCaseData.Arg();
        arg.setActivityUnifiedCaseId("6406a59a6de18e00012d8608");

        ActivityUnifiedCaseData.Result result = activityUnifiedCaseService.queryData(arg);
        System.out.println(result);
    }

    @Test
    public void sendLog() {
        ActivityTypeVO activityTypeVO = new ActivityTypeVO();
        activityTypeVO.setApiName(ApiNames.TPM_ACTIVITY_OBJ);
        activityTypeVO.setDescription("112--测试日志数据-1213");

        crmAuditLogService.sendLog(CrmAuditLogDTO.builder()
                .tenantId("84931")
                .userId(String.valueOf(1000))
                .action("Edit")
                .message("编辑活动类型")
                .extra(JSONObject.toJSONString(activityTypeVO))
                .parameters(JSONObject.toJSONString(activityTypeVO))
                .build());
    }

    @Test
    public void batchUpdateTest() {
//        ApiContext context = ApiContextManager.getContext();
//        context.setTenantId("80063");
//
//        StoreWriteOffBatchData.Arg arg = new StoreWriteOffBatchData.Arg();
//        String s = "{\"store_standard\":true,\"confirmed_amount\":\"12\",\"remark\":\"12\",\"audited_amount\":null}";
//        JSONObject object = JSONObject.parseObject(s);
//        arg.setData(object);
//        arg.setIdList(Lists.newArrayList("63282dc601b54200013f7ddd", "6343dbe26128de00010b5761", "6343dbbe6128de00010b495c", "6343dba46128de00010b4236", "6343db846128de00010b3ac0", "6343dbd46128de00010b5090", "6343e5446128de00010c59e7", "6343e5506128de00010c6133", "6343e55b6128de00010c6812", "6343e5636128de00010c6fa3", "6343e56c6128de00010c76e1", "6343e5746128de00010c7e3a", "6343e57d6128de00010c855e", "6343e5856128de00010c8c9d", "6343e58d6128de00010c93e7", "6343e5966128de00010c9a9c"));
//        arg.setWriteOffOwner(Lists.newArrayList("1000"));
//        StoreWriteOffBatchData.Result result = storeWriteOffService.batchUpdate(arg);
//        System.out.println(result);


        User user = new User("80063", "-10000");
        List<String> idList = Lists.newArrayList("6343e5506128de00010c6133", "6343e57d6128de00010c855e", "6343e5746128de00010c7e3a");
        IObjectDescribe detailDescribe = describeService.findObject("80063", ApiNames.TPM_STORE_WRITE_OFF_OBJ);
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds("80063", idList, ApiNames.TPM_STORE_WRITE_OFF_OBJ);
        for (IObjectData iObjectData : objectDataList) {
            iObjectData.set("confirmed_amount", "25");
        }
        RuleResult validateRule = validateRuleService.validateRule(user, "update", detailDescribe, objectDataList);

        if (validateRule != null) {
            if (!org.springframework.util.StringUtils.isEmpty(validateRule.getFailMessage())) {
                System.out.println(validateRule.getFailMessage());
            }
        }
    }

    @Test
    public void datraListV2Test() {
        StoreWriteOffDataListV2.Arg arg = new StoreWriteOffDataListV2.Arg();
        // id 62d6538d0a551700010a0911  ObjectId("6323f09da658a86deb5b1669")
        arg.setId("62d6538d0a551700010a0911");

        StoreWriteOffDataListV2.Result result = storeWriteOffV2Service.dataList(arg);
        System.out.println(result);
    }

    @Test
    public void test111() {
        IObjectData oldData = serviceFacade.findObjectData(User.systemUser("84931"), "63f4890ca3af6700012110b8", ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        ObjectDataDocument document = ObjectDataDocument.of(oldData);
        String field = (String) document.getOrDefault("activity_department_range", "");
        System.out.println(field);

    }

    @Test
    public void refreshData(){

//        FillActivityItem.Arg arg = new FillActivityItem.Arg();
//        arg.setUpTenantId("89150");
//        arg.setTenantIds(Lists.newArrayList());
//        arg.setActivityIdMap(ImmutableMap.of("84931", "65d8525bd038ac00014604d2"));
//        mengNiuRefreshDataService.fillActivityItem(arg);

        Map<String,String> fieldMap = new HashMap<String, String>();
        fieldMap.put("product_range_fresh_standard__c", "product_range_fresh_standard");
        FillProductRangeData.Arg arg = new FillProductRangeData.Arg();
        arg.setTenantId("82958");
        arg.setApiName("TPMActivityObj");
        arg.setFields(fieldMap);

        mengNiuRefreshDataService.fillProductRangeData(arg);
    }


    @Test
    public void pluginTest() {


        Map<String, IObjectDescribe> tpmActivityProofObj = serviceFacade.findObjects("80063", Lists.newArrayList("TPMActivityProofObj"));
        IFieldDescribe fieldDescribe = tpmActivityProofObj.get("TPMActivityProofObj").getFieldDescribe("record_type");
        List<Map<String, String>> options = (List<Map<String, String>>) fieldDescribe.get("options");
        String label = "";
        if (!CollectionUtils.isEmpty(options)) {
            label = options.stream()
                    .filter(v -> v.get("api_name").equals("default__c"))
                    .map(v -> v.get("label"))
                    .findFirst().orElse("");
        }
        System.out.println(label);


    }

    @Test
    public void consumeFlowTest() {


        Boolean flag = budgetConsumeFlowService.isExistedBudgetConsumeFlowData("84931", "62f5f5a15c173d519ebbe9ea");
        System.out.println(flag);


    }

    @Test
    public void provisionTest() {

        IObjectData data = serviceFacade.findObjectData(User.systemUser("84931"), "6780c803e3f385000140ce92", ApiNames.TPM_BUDGET_PROVISION_OBJ);
        budgetProvisionService.reuseOccupyBudget(data);

    }

    @Test
    public void newConsumeRuleTest() {
        ListBusinessObject.Arg arg = new ListBusinessObject.Arg();

        IObjectDescribe describe = serviceFacade.findObject("85494", ApiNames.TPM_ACTIVITY_OBJ);
        IFieldDescribe activityType = describe.getFieldDescribe("activity_type");
        List<Map<String, String>> options = (List<Map<String, String>>) activityType.get("options");

        ApiContextManager.setContext(ApiContext.builder().tenantId("85494").build());

//        ListBusinessObject.Result result = budgetNewConsumeRuleService.listBusinessObject(arg);

//        System.out.println(result);

    }


    @Test
    public void consumeRuleAdd() {

        BudgetRuleTypeNodeVO entity = new BudgetRuleTypeNodeVO();
        entity.setTriggerTime("add");
        entity.setType("deduction");
        entity.setSourceField("amount");

        BudgetWhereConditionVO where = new BudgetWhereConditionVO();
        where.setConnector("or");

        BudgetTriggerConditionVO conditionEntity = new BudgetTriggerConditionVO();
        conditionEntity.setValueType(0);
        conditionEntity.setFieldName("create_time");
        conditionEntity.setFieldValues(Lists.newArrayList("23123232132132"));
        conditionEntity.setOperator("==");

        where.setTriggerConditions(Lists.newArrayList(conditionEntity));

        BudgetWhereConditionVO where2 = new BudgetWhereConditionVO();
        where2.setConnector("or");

        BudgetTriggerConditionVO conditionEntity2 = new BudgetTriggerConditionVO();
        conditionEntity2.setValueType(0);
        conditionEntity2.setFieldName("last_modified_time");
        conditionEntity.setFieldValues(Lists.newArrayList("23123232132132"));
        conditionEntity2.setOperator("==");

        where2.setTriggerConditions(Lists.newArrayList(conditionEntity2));

        entity.setWhereConditions(Lists.newArrayList(where, where2));

        // ----------

        BudgetTableNodeVO templateNodeVO = new BudgetTableNodeVO();
        templateNodeVO.setRatio("50");

        BudgetFieldRelationVO relationVO = new BudgetFieldRelationVO();
        relationVO.setOperator("EQ");
        relationVO.setSourceField("base_amount");
        relationVO.setTargetField("create_time");

        BudgetTableNodeVO templateNodeVO2 = new BudgetTableNodeVO();
        templateNodeVO2.setRatio("50");

        BudgetFieldRelationVO relationVO2 = new BudgetFieldRelationVO();
        relationVO2.setOperator("EQ");
        relationVO2.setSourceField("base_amount");
        relationVO2.setTargetField("last_time");


        templateNodeVO.setFieldRelation(Lists.newArrayList(relationVO));

        templateNodeVO2.setFieldRelation(Lists.newArrayList(relationVO2));


        AddBudgetConsumeRule.Arg arg = new AddBudgetConsumeRule.Arg();
        BudgetConsumeRuleVO vo = new BudgetConsumeRuleVO();
        vo.setApiName("TPMActivityAgreementObj");
        vo.setName("规则15");
        vo.setRuleType("freeze_deduction");
        vo.setRuleDescription("123");
        vo.setRuleStatus("enable");
        vo.setRecordType("default__c");
        vo.setRuleTypeNodes(Lists.newArrayList(entity));
        vo.setBudgetTableNodes(Lists.newArrayList(templateNodeVO, templateNodeVO2));

        arg.setBudgetConsumeRuleVO(vo);


    }

    @Test
    public void budgetConsumeRuleList() {
        ApiContext context = ApiContextManager.getContext();
        context.setTenantId("82958");

//        ListBudgetConsumeRule.Arg arg = new ListBudgetConsumeRule.Arg();
//        arg.setKeyword("");
//        arg.setLimit(20);
//        arg.setObjectApiName("");
//        arg.setOffset(0);
//
//        budgetConsumeRuleService.list(arg);

        Map<String, String> objectsByTenantId = budgetAccrualRuleManager.findObjectsByTenantId(context.getTenantId());
        System.out.println(objectsByTenantId);

    }


    @Test
    public void consumeRuleTest() {

        BudgetConsumeRuleVO budgetConsumeRuleVO = new BudgetConsumeRuleVO();
        budgetConsumeRuleVO.setApiName("TPMActivityAgreementObj");

        BudgetRuleTypeNodeVO entity = new BudgetRuleTypeNodeVO();
        entity.setTriggerTime("add");
        entity.setType("deduction");
        entity.setSourceField("amount");

        BudgetWhereConditionVO where = new BudgetWhereConditionVO();
        where.setConnector("or");

        BudgetTriggerConditionVO conditionEntity = new BudgetTriggerConditionVO();
        conditionEntity.setValueType(0);
        conditionEntity.setFieldName("create_time");
        conditionEntity.setFieldValues(Lists.newArrayList("23123232132132"));
        conditionEntity.setOperator("==");

        where.setTriggerConditions(Lists.newArrayList(conditionEntity));

        BudgetWhereConditionVO where2 = new BudgetWhereConditionVO();
        where2.setConnector("or");

        BudgetTriggerConditionVO conditionEntity2 = new BudgetTriggerConditionVO();
        conditionEntity2.setValueType(0);
        conditionEntity2.setFieldName("last_modified_time");
        conditionEntity.setFieldValues(Lists.newArrayList("23123232132132"));
        conditionEntity2.setOperator("==");

        where2.setTriggerConditions(Lists.newArrayList(conditionEntity));

        entity.setWhereConditions(Lists.newArrayList(where, where2));

        budgetConsumeRuleVO.setRuleTypeNodes(Lists.newArrayList(entity));
        //规则条件选择配置
//        budgetConsumeRuleManager.enclosureConditionFilter("84931", 1000, budgetConsumeRuleVO);


    }


    private BigDecimal calculateStoreAmount(String tenantId,
                                            String objectId,
                                            String apiName,
                                            String costFieldApiName,
                                            String storeWriteOffFieldApiName,
                                            String type) {
        //求和
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(200);
        query.setOffset(0);

        //关联门店费用核销的 id
        Filter storeFilter = new Filter();
        storeFilter.setFieldName(storeWriteOffFieldApiName);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(objectId));
        query.setFilters(Lists.newArrayList(storeFilter));

        //如果依据对象是 活动举证检核的，，条件上加 检核结果 = pass 通过
        if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName)) {
            Filter statusFilter = new Filter();
            statusFilter.setFieldName(TPMActivityProofAuditFields.AUDIT_STATUS);
            statusFilter.setOperator(Operator.EQ);
            statusFilter.setFieldValues(Lists.newArrayList("pass"));
            query.getFilters().add(statusFilter);
        }

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                apiName,
                Lists.newArrayList(storeWriteOffFieldApiName),
                type,
                costFieldApiName);

        BigDecimal amount;
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(data)) {
            String key = String.format("%s_%s", type, costFieldApiName);
            amount = data.get(0).get(key, BigDecimal.class);
        } else {
            amount = new BigDecimal("0");
        }
        return amount;
    }


    private String getYears(Object createTime) {
        //根据创建时间获取 年月
        String parseTime = "";
        try {
            Date date = new Date(Long.parseLong(createTime.toString()));
            String format = sdf.format(date);
            Date parse = sdf.parse(format);
            parseTime = String.valueOf(parse.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return parseTime;
    }


    private List<IObjectData> getStoreWriteOffObjectData(String tenantId,
                                                         IObjectData dataDocument,
                                                         String years,
                                                         String accountFieldApiName,
                                                         String agreementFieldApiName) {

        if (StringUtils.isEmpty(years)) {
            return Collections.emptyList();
        }

        String accountField = (String) dataDocument.get(accountFieldApiName);
        String agreementField = (String) dataDocument.get(agreementFieldApiName);
        String activityId = (String) dataDocument.get(TPMStoreWriteOffFields.ACTIVITY_ID);
        //根据 以「门店」 + 「活动」 +「协议」 +「年月」为唯一属性，查询是否已有。

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        Filter yearsFilter = new Filter();
        yearsFilter.setFieldName(TPMStoreWriteOffFields.YEARS);
        yearsFilter.setOperator(Operator.EQ);
        yearsFilter.setFieldValues(Lists.newArrayList(years));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMStoreWriteOffFields.STORE);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(accountField));

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMStoreWriteOffFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter costFilter = new Filter();
        costFilter.setFieldName(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION);
        costFilter.setOperator(Operator.EQ);
        costFilter.setFieldValues(Lists.newArrayList(agreementField));
        query.setFilters(Lists.newArrayList(yearsFilter, storeFilter, activityFilter, costFilter));

        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_STORE_WRITE_OFF_OBJ, query).getData();
    }

    private void updateNoteObject(String tenantId, String dataId, String objectId, String apiName, String storeWriteOffFieldApiName) {
        try {
            IObjectData data = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, apiName);
            Map<String, Object> map = new HashMap<>();
            map.put(storeWriteOffFieldApiName, objectId);
            serviceFacade.updateWithMap(User.systemUser(tenantId), data, map);
        } catch (Exception e) {

        }
    }

    @Test
    public void dataListTest() {
        StoreWriteOffDataList.Arg arg = new StoreWriteOffDataList.Arg();
        arg.setSourceId("62bc05e57b182200015fa1bc");
        arg.setField("field_wmqkC__c");
        arg.setApiName("TPMActivityProofObj");

//        StoreWriteOffDataList.Result result = storeWriteOffService.dataList(arg);

//        System.out.println(result);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        Filter ownerFilter = new Filter();
        ownerFilter.setFieldName(CommonFields.NAME);
        ownerFilter.setOperator(Operator.EQ);
        ownerFilter.setFieldValues(Lists.newArrayList("AP-2022-06-30-00000003"));

        query.setFilters(Lists.newArrayList(ownerFilter));

        List<IObjectData> objectDataList = serviceFacade.findBySearchQuery(User.systemUser("80063"), "TPMActivityProofObj", query).getData();
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(objectDataList)) {
            IObjectData iObjectData = objectDataList.get(0);
            Object activityId = iObjectData.get("activity_id");
            activityId = activityId == null ? "" : activityId;
            System.out.println(activityId);
            addStoreWriteOff("80063", activityId.toString(), "TPMActivityProofObj", iObjectData);
        }


    }

    public void addStoreWriteOff(String tenantId, String activityId, String sourceApiName, IObjectData iObjectData) {


        ActivityTypeExt activityType = activityTypeManager.findByActivityId(tenantId, activityId);
        if (activityType == null) {
            return;
        }
        String activityTypeId = activityType.get().getId().toString();

        ActivityStoreWriteOffSourceConfigEntity configEntity = activityType.storeWriteOffSourceConfig();

        //  configEntity = getConfigEntity();
        //  fix

        if (configEntity == null || !sourceApiName.equals(configEntity.getApiName()) || !"80063".equals(tenantId)) {
            return;
        }
        String apiName = configEntity.getApiName();
        String storeWriteOffFieldApiName = configEntity.getReferenceStoreWriteOffFieldApiName();
        String dealerFieldApiName = configEntity.getDealerFieldApiName();
        String accountFieldApiName = configEntity.getAccountFieldApiName();
        String agreementFieldApiName = configEntity.getAgreementFieldApiName();
        String costFieldApiName = configEntity.getCostFieldApiName();

        //如果依据节点是活动检核
        if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName)) {

            storeWriteOffFieldApiName = TPMActivityProofAuditFields.STORE_WRITE_OFF_ID;
            costFieldApiName = TPMActivityProofAuditFields.AUDIT_TOTAL;
            dealerFieldApiName = TPMActivityProofAuditFields.DEALER_ID;
            accountFieldApiName = TPMActivityProofAuditFields.STORE_ID;
            agreementFieldApiName = TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID;
        }
        String calculateType = configEntity.getCalculateType();

        String dataId = iObjectData.getId();
        //通过创建时间去获取 年月
        String years = getYears(iObjectData.get(TPMStoreWriteOffFields.CREATE_TIME));

        if (StringUtils.isEmpty(years)) {
            return;
        }
        List<IObjectData> storeObjectData = getStoreWriteOffObjectData(tenantId, iObjectData, years, accountFieldApiName, agreementFieldApiName);
        //如果已存在核销
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(storeObjectData)) {
            IObjectData objectData = storeObjectData.get(0);
            // 获取当前依据的对象，并去更新 关联 门店费用核销的 字段
            updateNoteObject(tenantId, dataId, objectData.getId(), apiName, storeWriteOffFieldApiName);

            BigDecimal amount;
            //更新 根据 节点配置项，更新费用核销
            switch (calculateType) {
                case "store_cost_total":
                    amount = calculateStoreAmount(tenantId, objectData.getId(), apiName, costFieldApiName, storeWriteOffFieldApiName, "sum");
                    break;
                case "store_cost_average":
                    amount = calculateStoreAmount(tenantId, objectData.getId(), apiName, costFieldApiName, storeWriteOffFieldApiName, "avg");
                    break;
                default:
                    amount = new BigDecimal("0");
                    break;
            }
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMStoreWriteOffFields.AUDITED_AMOUNT, amount);
            updateMap.put(TPMStoreWriteOffFields.REF_AUDIT_COST, amount);

            serviceFacade.updateWithMap(User.systemUser(tenantId), objectData, updateMap);
        } else {
            //创建 门店费用核销
            IObjectData storeCostData = new ObjectData();
            storeCostData.setDescribeApiName(ApiNames.TPM_STORE_WRITE_OFF_OBJ);
            storeCostData.setTenantId(tenantId);
            storeCostData.setOwner(Lists.newArrayList("-10000"));
            storeCostData.set(TPMStoreWriteOffFields.ACTIVITY_ID, iObjectData.get(TPMStoreWriteOffFields.ACTIVITY_ID));

            //活动类型字段 取之 活动方案的 活动类型字段
            storeCostData.set(TPMStoreWriteOffFields.ACTIVITY_TYPE, activityTypeId);

            //依据字段
            storeCostData.set(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, iObjectData.get(agreementFieldApiName));
            storeCostData.set(TPMStoreWriteOffFields.DEALER_ID, iObjectData.get(dealerFieldApiName));
            storeCostData.set(TPMStoreWriteOffFields.STORE, iObjectData.get(accountFieldApiName));
            //核销费用 字段
            storeCostData.set(TPMStoreWriteOffFields.CONFIRMED_AMOUNT, "");
            storeCostData.set(TPMStoreWriteOffFields.YEARS, years);
            storeCostData.set(TPMStoreWriteOffFields.CREATE_TIME, System.currentTimeMillis());
            storeCostData.set(TPMStoreWriteOffFields.STORE_STANDARD, true);
            storeCostData.set(TPMStoreWriteOffFields.WRITE_OFF_STATUS, TPMStoreWriteOffFields.WriteOffStatus.AUTOMATIC_WRITE_OFF);
            storeCostData.set(TPMStoreWriteOffFields.WRITE_OFF_VALUE, TPMStoreWriteOffFields.WriteOffValue.AGREE);
            storeCostData.set(TPMStoreWriteOffFields.WRITE_OFF_OWNER, Lists.newArrayList("-10000"));
            //如果检核对象是不合格的，取0
            Object amount = iObjectData.get(costFieldApiName);
            if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName)
                    && !"pass".equals(iObjectData.get(TPMActivityProofAuditFields.AUDIT_STATUS))) {
                amount = 0;
            }
            storeCostData.set(TPMStoreWriteOffFields.AUDITED_AMOUNT, amount);
            storeCostData.set(TPMStoreWriteOffFields.REF_AUDIT_COST, amount);
            storeCostData.set(TPMStoreWriteOffFields.ENTER_INTO_ACCOUNT, false);


            IObjectData saveObjectData = serviceFacade.saveObjectData(User.systemUser(tenantId), storeCostData);
            // 获取当前依据的对象，并去更新 关联 门店费用核销的 字段
            updateNoteObject(tenantId, dataId, saveObjectData.getId(), apiName, storeWriteOffFieldApiName);
        }


    }

    @Test
    public void gray815Test() {
        String companyTenantIdsStr = "735029,739241,735463,747978,700528,685453,747878,590065,663073,590225,590064,590061,590161,663116,723437,590056,738294,719424,590062,752234,708376,708249,746351,663123,589987,752241,590118,747582,712528,718299,662514,679449,732368,737312,746617,720772,744776,590124,590135,743395,726597,713391,744777,590268,730068,715682,731936,590116,712632,679456,733938,590103,663002,713401,745011,726884,722652,590057,683722,736925,663001,662991,747438,746570,723622,590109,743158,744365,736542,742890,747736,663027,662957,724519,723428,590209,752056,712664,735652,590063,665485,715148,725731,720157,726886,590126,709122,736840,736209,683675,753579,753360,707281,683731,679488,727343,590027,590188,725721,747483,746377,750744,679469,747053,663125,738490,590275,746350,748666,663068,679427,590243,683661,747123,713359,724276,726389,663105,744997,590213,590115,590021,726785,679403,734277,590091,743368,752527,732889,734676,724481,751779,590172,747051,726820,749433,744199,708801,590125,751555,731670,590069,748098,590058,590264,737078,726889,715669,748664,739331,729499,748683,726958,729843,733210,709361,711681,716722,745216,589994,590261,726894,734663,747003,738453,744570,663024,727364,742922,589984,683750,663095,739500,726885,679402,748965,662947,747952,590217,663045,743164,735008,732136,744569,742812,590274,733027,735044,718964,662999,744492,746347,663044,723943,721302,663512,729908,720136,739322,744450,716474,750797,735210,739657,663032,663124,744276,724352,750746,743393,743880,735248,683709,683712,748411,743466,731960,744930,590077,739900,727415,744520,746860,662987,747946,748644,744745,590270,733723,737183,663046,683643,708777,708904,590257,590251,712011,731999,663484,752211,751053,748820,723559,743324,751287,736356,590140,716789,736199,589997,748199,683751,590231,743788,753590,747500,744890,679437,753633,590015,747058,720284,753136,753137,727865,733682,663053,732941,679440,679467,730982,712277,679498,748662,590130,720724,736622,730410,744717,727664,590216,709412,733119,737092,743516,663052,734733,752519,590165,736908,723541,679428,753641,721533,721343,713211,732915,747793,753589,683634,745267,753209,749070,718037,737219,721104,744797,679451,744970,726945,752360,736137,753711,730384,589986,727797,734249,753778,590241,746827,751244,753001,751934,719254,733702,712842,708906,723797,748260,735057,748950,590042,725960,752586,724636,590234,744775,747116,718397,738454,746353,663051,683665,590259,753636,718297,733316,731310,749715,724776,716348,732859,753639,663025,736560,724128,747987,753464,590052,733321,748127,753632,590121,710485,590258,734201,723343,744317,752904,744219,744332,721357,721557,749204,709356,590093,679441,735448,748339,734374,709730,683702,744495,749457,717371,753364,734499,753434,713215,751838,663501,723033,742964,663020,748315,708641,714492,747472,750796,749615,747475,590120,590239,590012,753004,662505,718302,753649,751820,735304,706242,716493,744833,590153,752893,752766,730606,590193,752822,746373,679472,738617,590227,746670,743145,749050,590048,723916,747777,679417,722079,708642,752859,708901,735104,745271,590249,747697,748137,683635,750758,749246,747094,723422,720156,750303,662520,746619,736896,752993,663091,752469,662990,753316,753068,731280,753181,744804,662986,745215,739656,725310,750899,715381,714403,738589,753262,663031,670800,662994,744434,720569,726946,725725,663074,683629,736149,746868,720158,753090,748471,739239,721307,745290,750981,738869,753657,726161,751646,733681,721794,739845,753612,739586,663042,590179,731672,753152,679446,752854,713213,753003,747484,707403,753002,720250,724792,726163,663079,746831,734928,743098,736897,735395,719655,679435,738405,725546,746375,590010,746641,53395,679404,748151,747814,731600,735294,720366,662981,744828,662989,738349,679457,683650,752598,717712,590228,663098,752958,662503,753469,679493,590146,747954,726944,750906,746507,733695,679464,720375,728006,725276,749487,725179,743046,744437,752857,717554,663082,748131,733683,735208,738944,679461,748089,725726,705745,747124,748571,737369,679455,679434,752354,679487,752865,746409,736205,747119,663094,753941,731379,723479,683724,679468,670799,742965,744503,753208,753154,735681,747073,719584,663099,730385,750582,726943,590244,724456,728641,743508,742929,746379,716582,735302,679478,737066,724658,751855,590252,718693,726410,746577,752824,683669,747219,683704,748152,726136,747893,722739,753645,736595,732210,749013,713358,715100,683664,743456,748575,729267,723812,708516,752008,723421,679483,712633,739242,679491,747424,748738,662992,662502,750455,748148,752850,744946,743731,590245,753162,753605,752648,683689,683640,710101,734675,663023,679422,730983,749008,752359,744870,723551,749934,744072,590149,706492,753990,590000,748483,679413,746464,735283,720614,754006,727201,679447,663101,663458,662518,683633,753079,665482,683723,590001,751753,590055,754206,662985,720688,683639,663485,730872,683708,753335,747345,751922,727957,753151,589992,752016,732518,745953,679429,744141,683666,754288,747918,720137,746914,590254,706688,753148,754115,739412,742993,706407,730119,590238,731715,726170,730428,739880,749192,720160,750410,744996,707411,663089,679433,747874,727928,718968,709718,663117,724833,718300,746865,752856,683658,679462,714382,683671,749421,753265,733684,743130,727702,590163,739866,730324,753380,679463,662971,721492,742784,754169,738896,590081,683659,734922,752765,744667,719343,590160,749621,752746,590272,738372,746917,737466,747608,726344,718741,750816,732110,590051,749504,743378,715111,733827,719393,724119,754104,662964,724031,750586,724078,748479,752079,662951,706442,590090,719877,590111,720933,737415,743261,744591,725715,590086,590132,751856,663081,720292,663086,662983,751620,590005,711365,725544,736566,748194,679481,590080,683701,707694,663057,747923,738584,736801,753132,739581,721916,662509,746651,748149,663498,749778,729109,590273,679405,683647,715380,721803,662962,712202,725894,590047,729756,753761,753653,683710,747026,751634,726661,754016,736762,715112,748282,754231,747942,740916,721352,663036,735082,741814,720885,708131,712174,590145,731619,739611,663476,663075,720895,741080,663005,711677,731033,733996,754002,663026,736498,739472,589982,722724,748193,590059,753858,731178,735743,751971,753111,732665,752858,590084,739229,748219,679471,734630,708902,747824,679406,679436,748322,724562,743294,750687,663018,752504,705755,734678,731279,663007,738455,750461,716708,725545,751049,662973,679474,748073,749765,732367,736018,729902,738295,749967,735182,710604,734215,723430,748191,728036,589983,731567,731519,751651,752309,662978,737617,735209,736155,589990,749108,723732,754107,732933,750587,754143,711966,662982,729768,748935,725564,736159,590067,709682,751846,754142,746951,752761,665385,750822,718298,734039,727017,754106,752017,733317,679453,752764,754111,663061,589996,590040,683691,729638,754125,752763,750814,724342,590263,590174,679418,736014,753637,679489,590089,727231,679480,590206,741800,683729,683734,726666,710858,708041,754114,753591,679420,748228,679482,744149,590105,742995,735598,743767,662980,683681,747117,751754,751577,749790,748248,590016,590014,728185,737060,662510,590127,754120,752165,735396,725957,728357,750757,719261,731705,662977,753618,750566,721303,743390,736214,750063,714371,746997,748196,735684,723845,709490,709063,683695,750376,590070,752866,720135,729692,753180,744567,719618,754136,714538,735542,662968,733839,683696,748359,753130,750572,737058,735847,754086,754109,748344,729909,726922,713374,706374,748848,663064,739448,742792,712705,739678,740108,589999,748190,754167,750755,708042,747625,753336,663507,721588,750389,753472,590189,735979,739407,747702,729099,720066,726997,751598,748754,730761,752333,712937,663013,749938,753525,663003,736067,747633,750017,735301,717340,733645,589998,748212,734130,753654,750815,663113,739326,749839,719290,753118,748659,752344,752024,731188,752842,717080,739453,736276,706855,750460,750570,749111,719610,727927,754108,728525,679499,726512,739643,730518,737353,748220,749112,752878,751103,590148,712769,718729,735425,748147,590114,679460,718396,679411,663030,747926,683688,743134,752902,753855,748130,713086,751921,724480,663041,752625,752992,749468,751916,731290,747573,589991,736623,749193,662950,663065,737366,662959,663050,753658,706991,683668,663080,662966,738452,748197,739564,748734,590215,727206,728407,736284,727823,662507,748192,728032,736564,679466,590242,735599,712453,590196,679465,750145,754161,706228,724581,708025,683748,724424,719939,708780,721389,732415,734213,748146,707019,726194,730328,753054,663503,751291,752119,720498,590167,751917,732220,754141,590192,662522,717417,744139,754269,751554,735306,727701,679448,724012,744497,748254,705746,709287,749110,749422,750855,724054,708019,679407,753923,739447,663056,663049,744615,683641,590097,748356,589995,663118,722654,727929,754194,736015,716489,590044,683752,747114,747522,752609,749465,717127,735832,683740,751823,748733,737240,728491,590050,706553,754203,679485,683717,708776,735299,590094,665476,736085,683636,738416,753183,730187,727699,663006,590175,717338,747008,747031,753005,752762,662519,663000,663033,754220,711429,679479,723653,735846,753638,590131,663085,663106,733685,734559,736619,748271,744555,589979,683698,752481,749890,739642,709936,590112,723170,738907,721335,726987,748878,752955,749461,747440,749936,744587,683746,710580,750018,719792,754070,753045,749754,746348,734178,721103,710312,679416,590226,752195,751854,743162,735319,723362,718786,670801,662952,748225,590218,731396,748876,744894,745014,753627,753803,747115,744967,590202,679415,753160,730517,736158,748472,753120,590018,663038,706437,590220,724775,748476,737280,683642,663037,590078,663114,726564,744483,724485,741081,747113,720860,683753,679454,707961,709602,717173,734677,747118,751781,751290,748209,747854,728249,728237,722026,715681,663514,662965,683736,683755,590191,663077,723429,749476,751825,749848,747463,746744,749194,720133,709304,709293,662508,590210,663496,743161,744632,750567,752468,753838,754101,753724,749917,714757,683714,683674,753163,733054,728083,713549,712938,711679,590236,589993,713214,727063,730252,737585,751824,753334,748278,724890,711680,590003,749774,734925,731620,720199,663015,590011,729711,739414,707535,729364,735305,735601,736013,750433,753141,753523,750997,748085,735300,720634,716752,683678,590162,752529,728655,708130,706896,753102,751725,590204,707976,720633,741082,663482,665386,709687,735068,754083,752752,751024,749146,748920,747938,662993,590043,734673,663009,706065,719207,725077,726266,753801,751097,730714,722704,710303,709627,663063,735303,736215,745734,749753,754209,728352,727698,727697,726996,663483,748210,737230,733954,729585,727070,717202,710451,663078,590208,590104,590008,665483,683745,731618,737059,753601,748154,722741,590038,590041,679421,708580,590013,590083,590223,752235,754146,736571,736026,719299,683747,663087,662996,590176,754211,747540,718704,710859,679477,663059,590256,590068,589989,679409,727718,734410,749160";
        String tpmTenantIdsStr = "663116,590268,736925,746337,725894,743847,590042,742812,731672,747438,747573,725725,747615,747736,590245,709541,731178,747878,747520,747149,747942,743470,731936,726886,748187,748148,748193,748192,748191,748196,748194,748219,748199,748254,748262,748260,747954,748282,748285,748308,748309,748310,748344,723943,748409,748228,748453,748080,748471,748483,723033,748554,748565,748693,684088,720724,748876,748888,748893,748897,748902,743880,748927,748322,748950,748970,748983,735652,748999,749082,749367,749464,749619,710963,749886,749934,750281,739657,750326,750427,749468,750521,749621,750577,750694,750717,750718,750817,751020,750997,750998,751026,751041,750746,751024,751049,751239,751566,751586,751746,748411,751847,751881,751919,751994,752189,752243,752275,752281,744034,736720,743632,752561,752568,735703,736542,752849,752797,752955,752565,735395,753442,725845,753485,753489,753512,753525,753227,753138,754169,663085,575252";

        List<String> split = Lists.newArrayList(tpmTenantIdsStr.split(","));
        System.out.println(split.size());
        List<String> company = Lists.newArrayList(companyTenantIdsStr.split(","));

        split.retainAll(company);
        System.out.println(split);
        System.out.println(split.size());
        System.out.println(company.size());
    }
}
