package com.facishare.crm.fmcg.session;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.restful.client.FRestApiProxyFactory;
import com.fxiaoke.Utils.ReceiverChannelUtils;
import com.fxiaoke.api.MessageServiceV2;
import com.fxiaoke.constant.ReceiverChannelType;
import com.fxiaoke.model.*;
import com.fxiaoke.model.message.SendImageCardMessageArg;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.fxiaoke.model.message.SendTextLinkMessageArg;
import com.fxiaoke.model.message.SendTextMessageArg;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

/**
 * @author: wuyx
 * @description:
 * @createTime: 2022/1/7 10:27
 */
@Slf4j
public class PlateMessageTest extends BaseTest {

    private MessageServiceV2 messageServiceV2;

    @Before
    public void init() throws Exception {
        this.messageServiceV2 = FRestApiProxyFactory.getInstance().create(MessageServiceV2.class);
    }


    @Test
    public void sendTextMessage() throws Exception {
        ApiContext context = ApiContextManager.getContext();
        SendTextMessageArg arg = new SendTextMessageArg();
        {
            arg.setUuid("111");
            arg.setEi(80063);
            // 应用通知
            arg.setReceiverChannelType(ReceiverChannelType.NOTICE);
            arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData("TPM"));

            //服务号
//            arg.setReceiverChannelType(ReceiverChannelType.OPEN_APP);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildOpenAppChannelData(80063, 0, context.getAppId()));

/*
            //业务群
            arg.setReceiverChannelType(ReceiverChannelType.TRUST_GROUP);
            arg.setReceiverChannelData(ReceiverChannelUtils.buildTrustChannelData("NewOpportunityObj","5c04bc3d99ae20000177b606"));

            //外部平台
            arg.setReceiverChannelType(ReceiverChannelType.EXTERNAL_PLATFORM);
            arg.setReceiverChannelData(ReceiverChannelUtils.buildExternalChannelData(PlatformType.WeChat));
*/
            arg.setMessageContent("销售线索超时112 姓名：谷嘉欣.11123");
            arg.setReceiverIds(Lists.newArrayList(1000));
        }
        System.out.println(JSON.toJSONString(arg));
        MessageResponse result = messageServiceV2.sendTextMessage(arg);
        if (result.getCode() == MessageResponse.SUCCESS_CODE) {
            log.info("message send success");
        } else {
            log.error("message send failed");
        }
    }

    @Test
    public void sendTextLinkMessage() throws Exception {
        SendTextLinkMessageArg arg = new SendTextLinkMessageArg();
        {
           // arg.setUuid("110");
            arg.setEi(80063);
            //应用通知
            arg.setReceiverChannelType(ReceiverChannelType.NOTICE);
            arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData("TPM"));

//            //服务号
//            arg.setReceiverChannelType(ReceiverChannelType.OPEN_APP);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildOpenAppChannelData(55707,0,"FSAID_11490cb2"));
//
//            //业务群
//            arg.setReceiverChannelType(ReceiverChannelType.TRUST_GROUP);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildTrustChannelData("NewOpportunityObj","5c04bc3d99ae20000177b606"));
//
//            //外部平台
//            arg.setReceiverChannelType(ReceiverChannelType.EXTERNAL_PLATFORM);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildExternalChannelData(PlatformType.WeChat));
            arg.setMessageContent("销售线索超时 姓名：谷嘉欣.");
            arg.setTitle("销售线索超时");
            arg.setOutPlatformUrl("www.baidu.com");
            arg.setInnerPlatformMobileUrl("www.baidu.com");
            arg.setReceiverIds(Lists.newArrayList(1000));
        }
        MessageResponse result = messageServiceV2.sendTextLinkMessage(arg);
        if (result.getCode() == MessageResponse.SUCCESS_CODE) {
            log.info("message send success");
        } else {
            log.error("message send failed");
        }
    }

    @Test
    public void sendTextCardMessage() throws Exception {
        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        {
            arg.setUuid("110");
            arg.setEi(2);
            //应用通知
            arg.setReceiverChannelType(ReceiverChannelType.NOTICE);
            arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData("CRM"));

//            //服务号
//            arg.setReceiverChannelType(ReceiverChannelType.OPEN_APP);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildOpenAppChannelData(55707,0,"FSAID_11490cb2"));
//
//            //业务群
//            arg.setReceiverChannelType(ReceiverChannelType.TRUST_GROUP);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildTrustChannelData("NewOpportunityObj","5c04bc3d99ae20000177b606"));
//
//            //外部平台
//            arg.setReceiverChannelType(ReceiverChannelType.EXTERNAL_PLATFORM);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildExternalChannelData(PlatformType.WeChat));
            arg.setReceiverIds(Lists.newArrayList(1000));
            TextCardMessage textCardMessage = new TextCardMessage();
            {
                TextCardMessageHead head = new TextCardMessageHead();
                head.setTitleElement(new TextCardElement("报表订阅", "", ""));
                textCardMessage.setHead(head);
                TextCardMessageBody body = new TextCardMessageBody();
                body.setContentElement(new TextCardElement("报表名称", "", ""));
                List<KeyValueItem> keyValueItems = Lists.newArrayList();
                keyValueItems.add(new KeyValueItem(new TextCardElement("报表名称：", "", ""), new TextCardElement("时间段范围", "", "")));
                keyValueItems.add(new KeyValueItem(new TextCardElement("运行时间：", "", ""), new TextCardElement("2019-03-06 02:45:08", "", "")));
                body.setForm(keyValueItems);
                textCardMessage.setBody(body);
                TextCardMessageFoot foot = new TextCardMessageFoot();
                foot.setFootElement(new TextCardElement("点击查看详情", "", ""));
                textCardMessage.setFoot(foot);
                textCardMessage.setInnerPlatformMobileUrl("www.baidu.com");
                textCardMessage.setInnerPlatformWebUrl("www.baidu.com1111");
                textCardMessage.setOutPlatformUrl("www.baidu.com");
            }
            arg.setTextCardMessage(textCardMessage);
        }
        MessageResponse result = messageServiceV2.sendTextCardMessage(arg);
        if (result.getCode() == MessageResponse.SUCCESS_CODE) {
            log.info("message send success");
        } else {
            log.error("message send failed");
        }
    }


    @Test
    public void sendImageCardMessage() throws Exception {

        SendImageCardMessageArg arg = new SendImageCardMessageArg();
        {
            arg.setUuid("110");
            arg.setEi(55707);
            //应用通知
            arg.setReceiverChannelType(ReceiverChannelType.NOTICE);
            arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData("CRM"));

//            //服务号
//            arg.setReceiverChannelType(ReceiverChannelType.OPEN_APP);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildOpenAppChannelData(55707,0,"FSAID_11490cb2"));
//
//            //业务群
//            arg.setReceiverChannelType(ReceiverChannelType.TRUST_GROUP);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildTrustChannelData("NewOpportunityObj","5c04bc3d99ae20000177b606"));
//
//            //外部平台
//            arg.setReceiverChannelType(ReceiverChannelType.EXTERNAL_PLATFORM);
//            arg.setReceiverChannelData(ReceiverChannelUtils.buildExternalChannelData(PlatformType.WeChat));
            arg.setButtonText("ceshi");
            arg.setMessageContent("销售线索超时 姓名：谷嘉欣.");
            arg.setTitle("销售线索超时");
            arg.setOutPlatformUrl("www.baidu.com");
            arg.setInnerPlatformMobileUrl("www.baidu.com");
            arg.setReceiverIds(Lists.newArrayList(1000));
            arg.setPicName("2018-11-27_114147.txt");
            arg.setPicPath("N_201903_05_cf0495b82c8345bcb48e43d3c1428437.txt");
        }
        MessageResponse result = messageServiceV2.sendImageCardMessage(arg);
        if (result.getCode() == MessageResponse.SUCCESS_CODE) {
            log.info("message send success");
        } else {
            log.error("message send failed");
        }
    }
}
