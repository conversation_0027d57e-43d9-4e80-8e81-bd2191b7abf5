package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.facishare.crm.fmcg.tpm.action.TPMBudgetDisassemblyObjAddAction;
import com.facishare.crm.fmcg.tpm.action.TPMBudgetDisassemblyObjEditAction;
import com.facishare.crm.fmcg.tpm.controller.TPMBudgetDisassemblyObjDescribeLayoutController;
import com.facishare.crm.fmcg.tpm.controller.TPMBudgetDisassemblyObjWebDetailController;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutResourceController;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;


public class BudgetDisassemblyObjTest extends BaseTest {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private InfraServiceFacade infraServiceFacade;

    @Test
    public void buttonTest() {
        StandardDesignerLayoutResourceController controller = new StandardDesignerLayoutResourceController();
        controller.setServiceFacade(serviceFacade);

        StandardDesignerLayoutResourceController.Arg arg = new StandardDesignerLayoutResourceController.Arg();
        arg.setDescribeApiName("close_test__c");
        arg.setIncludeButtons(true);
        arg.setIncludeFieldTypes(true);
        arg.setLayoutType("detail");
        controller.setArg(arg);

        RequestContext requestContext = RequestContext.builder()
                .tenantId("84931")
                .ea("84931")
                .user(User.systemUser("84931"))
                .build();

        controller.setControllerContext(new ControllerContext(requestContext, "close_test__c", "DesignerLayoutResource"));

        StandardDesignerLayoutResourceController.Result result = controller.service(arg);

        assert result != null;
    }

    @Test
    public void describeLayoutTest() {
        TPMBudgetDisassemblyObjDescribeLayoutController controller = new TPMBudgetDisassemblyObjDescribeLayoutController();
        controller.setServiceFacade(serviceFacade);
        controller.setInfraServiceFacade(infraServiceFacade);

        TPMBudgetDisassemblyObjDescribeLayoutController.Arg arg = new TPMBudgetDisassemblyObjDescribeLayoutController.Arg();
//        arg.setTargetBudgetTemplateId("62fb368cd23e543af05bc85e");
        arg.setApiname("TPMBudgetDisassemblyObj");
        arg.setInclude_detail_describe(true);
        arg.setInclude_layout(true);
        arg.setLayout_type("add");
        arg.setLayoutAgentType("mobile");
        arg.setRecordType_apiName("default__c");
        arg.setSourceBudgetNodeId("67a9619cdab06000013d1207");
        arg.setTargetBudgetNodeId("67a9619cdab06000013d1208");
        arg.setBudgetTypeId("67a9619cdab06000013d120a");



        controller.setArg(arg);

        RequestContext requestContext = RequestContext.builder()
                .tenantId("85494")
                .ea("85494")
                .user(User.systemUser("85494"))
                .build();
        controller.setControllerContext(new ControllerContext(requestContext, "TPMBudgetDisassemblyObj", "DescribeLayout"));

        RequestContextManager.setContext(requestContext);

        StandardDescribeLayoutController.Result service = controller.service(arg);

        assert service != null;
    }

    @Test
    public void addActionTest() {
        TPMBudgetDisassemblyObjAddAction action = new TPMBudgetDisassemblyObjAddAction();
        action.setServiceFacade(serviceFacade);
        //action.setStopWatch(StopWatch.create("TPMBudgetDisassemblyObjAddAction"));

        RequestContext requestContext = RequestContext.builder()
                .tenantId("84931")
                .ea("84931")
                .user(User.systemUser("84931"))
                .build();
        action.setActionContext(new ActionContext(requestContext, "TPMBudgetDisassemblyObj", "Add"));

        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(new ObjectDataDocument());
        String objData = "{\"object_describe_api_name\":\"TPMBudgetDisassemblyObj\",\"record_type\":\"default__c\",\"created_by\":[\"1000\"],\"owner\":[\"1000\"],\"data_own_department\":[\"1000\"],\"data_own_department__r\":\"华东业务部\",\"new_detail_disassembly_amount\":\"50.00\",\"exists_detail_disassembly_amount\":\"0.00\",\"disassembly_amount\":\"50.00\",\"available_amount\":\"450.00\",\"source_budget_template_id__o\":\"\",\"budget_type_id__o\":\"\",\"source_budget_node_id__o\":\"\",\"target_budget_template_id__o\":\"\",\"target_budget_node_id__o\":\"\",\"object_describe_id\":\"63009679b2b0380001b12ce9\",\"source_budget_account_id\":\"630ed802d090d40001ef9325\",\"source_budget_template_id\":\"630ca8e8142d080627d45329\",\"budget_type_id\":\"630ca933142d080627d4532b\",\"source_budget_node_id\":\"630ca9333f2d8700012fdb5d\",\"source_budget_department\":\"消费规则部门\",\"target_budget_template_id\":\"630ca902142d080627d4532a\",\"target_budget_node_id\":\"630ca9333f2d8700012fdb5e\",\"requestId\":\"069ffa1d6216493384bb0913d8e2ff06\"}";

        arg.getObjectData().putAll(JSON.parseObject(objData));


        arg.setDetails(Maps.newHashMap());

//        String detail = "{\"record_type\":\"default__c\",\"object_describe_id\":\"6300967ab2b0380001b12d1e\",\"object_describe_api_name\":\"TPMBudgetDisassemblyNewDetailObj\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\",\"dealer_id\":\"62f5f51a2c477d0001a13f93\",\"dealer_id__r\":\"消费2\",\"take_apart_in_amount\":\"100.00\",\"name\":\"测试拆解04\",\"budget_department\":[\"1000\"],\"budget_period_year\":************0,\"__tbIndex\":0,\"amount\":\"100\"}";
//        List<ObjectDataDocument> details = Lists.newArrayList();
//        ObjectDataDocument objectDataDocument = new ObjectDataDocument();
//        objectDataDocument.putAll(JSON.parseObject(detail));
//        details.add(objectDataDocument);
//
//        arg.getDetails().put("TPMBudgetDisassemblyNewDetailObj", details);

        String detail = "{\"record_type\":\"default__c\",\"object_describe_id\":\"6300967ab2b0380001b12d1e\",\"object_describe_api_name\":\"TPMBudgetDisassemblyNewDetailObj\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\",\"take_apart_in_amount\":\"50.00\",\"name\":\"3-8\",\"budget_department\":[\"1005\"],\"budget_period_month\":1659283200000,\"budget_subject_id\":\"6307140cba20b40001e33327\",\"budget_subject_id__r\":\"lwh二级科目\",\"product_category_id\":\"62a0168d25b0460001549f40\",\"product_category_id__r\":\"家用电器\",\"amount\":\"50\"}";
        List<ObjectDataDocument> details = Lists.newArrayList();
        ObjectDataDocument objectDataDocument = new ObjectDataDocument();
        objectDataDocument.putAll(JSON.parseObject(detail));
        details.add(objectDataDocument);

        arg.getDetails().put("TPMBudgetDisassemblyNewDetailObj", details);

        action.setArg(arg);

        BaseObjectSaveAction.Result result = action.act(arg);

        assert result != null;
    }

    @Test
    public void editActionTest() {
        TPMBudgetDisassemblyObjEditAction action = new TPMBudgetDisassemblyObjEditAction();
        action.setServiceFacade(serviceFacade);
        action.setInfraServiceFacade(infraServiceFacade);

        RequestContext requestContext = RequestContext.builder()
                .tenantId("84966")
                .ea("84966")
                .user(User.systemUser("84966"))
                .build();
        action.setActionContext(new ActionContext(requestContext, "TPMBudgetDisassemblyObj", "Edit"));

        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(new ObjectDataDocument());
        String objData = "{\"record_type\":\"default__c\",\"object_describe_id\":\"6300967ab2b0380001b12d1e\",\"object_describe_api_name\":\"TPMBudgetDisassemblyNewDetailObj\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\",\"take_apart_in_amount\":\"50.00\",\"name\":\"3-8\",\"budget_department\":[\"1005\"],\"budget_period_month\":1659283200000,\"budget_subject_id\":\"6307140cba20b40001e33327\",\"budget_subject_id__r\":\"lwh二级科目\",\"product_category_id\":\"62a0168d25b0460001549f40\",\"product_category_id__r\":\"家用电器\",\"amount\":\"50\"}";

        arg.getObjectData().putAll(JSON.parseObject(objData));


        arg.setDetails(Maps.newHashMap());

//        String detail = "{\"record_type\":\"default__c\",\"object_describe_id\":\"6300967ab2b0380001b12d1e\",\"object_describe_api_name\":\"TPMBudgetDisassemblyNewDetailObj\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\",\"dealer_id\":\"62f5f51a2c477d0001a13f93\",\"dealer_id__r\":\"消费2\",\"take_apart_in_amount\":\"100.00\",\"name\":\"测试拆解04\",\"budget_department\":[\"1000\"],\"budget_period_year\":************0,\"__tbIndex\":0,\"amount\":\"100\"}";
//        List<ObjectDataDocument> details = Lists.newArrayList();
//        ObjectDataDocument objectDataDocument = new ObjectDataDocument();
//        objectDataDocument.putAll(JSON.parseObject(detail));
//        details.add(objectDataDocument);
//
//        arg.getDetails().put("TPMBudgetDisassemblyNewDetailObj", details);

        String detail = "{\"record_type\":\"default__c\",\"object_describe_id\":\"6300967ab2b0380001b12d1e\",\"object_describe_api_name\":\"TPMBudgetDisassemblyNewDetailObj\",\"lock_rule\":\"default_lock_rule\",\"lock_status\":\"0\",\"life_status\":\"normal\",\"take_apart_in_amount\":\"50.00\",\"name\":\"3-8\",\"budget_department\":[\"1005\"],\"budget_period_month\":1659283200000,\"budget_subject_id\":\"6307140cba20b40001e33327\",\"budget_subject_id__r\":\"lwh二级科目\",\"product_category_id\":\"62a0168d25b0460001549f40\",\"product_category_id__r\":\"家用电器\",\"amount\":\"50\"}";
        List<ObjectDataDocument> details = Lists.newArrayList();
        ObjectDataDocument objectDataDocument = new ObjectDataDocument();
        objectDataDocument.putAll(JSON.parseObject(detail));
        details.add(objectDataDocument);

        arg.getDetails().put("TPMBudgetDisassemblyExistsDetailObj", details);

        action.setArg(arg);

        RequestContextManager.setContext(requestContext);

        BaseObjectSaveAction.Result result = action.act(arg);

        assert result != null;
    }

    @Test
    public void initButton() {
//        budgetDisassemblyService.initFailedRetryButton("84931");

        String traceId = "6454beceff56840001fd293f".toUpperCase(Locale.ROOT);
        String detailId = "6454bed4ff56840001fd2aaf";
        IObjectData data = serviceFacade.findObjectData(User.systemUser("84931"), detailId, "TPMBudgetAccountDetailObj");
        Map<String, Object> map = new HashMap<>();
        map.put("approval_trace_id", traceId);
        map.put("biz_trace_id", traceId);
        serviceFacade.updateWithMap(User.systemUser("84931"), data, map);
    }

    @Test
    public void webDetailTest() {
        TPMBudgetDisassemblyObjWebDetailController controller = new TPMBudgetDisassemblyObjWebDetailController();
        controller.setServiceFacade(serviceFacade);
        controller.setInfraServiceFacade(infraServiceFacade);

        TPMBudgetDisassemblyObjWebDetailController.Arg arg = new TPMBudgetDisassemblyObjWebDetailController.Arg();
        arg.setObjectDataId("645e26a73b66e00001872e76");
        arg.setObjectDescribeApiName("TPMBudgetDisassemblyObj");
        arg.setSerializeEmpty(false);
        Map<String, Integer> a = new HashMap<>();
        a.put("TPMBudgetDisassemblyObj", 60);
        arg.setDescribeVersionMap(a);
        arg.setFormatData(false);
        arg.setLayoutVersion("V3");

        controller.setArg(arg);

        RequestContext requestContext = RequestContext.builder()
                .tenantId("84966")
                .ea("84966")
                .user(User.systemUser("84966"))
                .build();
        controller.setControllerContext(new ControllerContext(requestContext, "TPMBudgetDisassemblyObj", "WebDetail"));

        RequestContextManager.setContext(requestContext);

        TPMBudgetDisassemblyObjWebDetailController.Result service = controller.service(arg);
        assert service != null;
    }

    @Test
    public void updateTest() {
        List<IObjectData> objData = serviceFacade.findObjectDataByIdsIgnoreAll("84966", Lists.newArrayList("64632e0f33cf4500018f8853"), "TPMBudgetDisassemblyNewDetailObj");

        for (IObjectData objDatum : objData) {
            try {

                Map<String, Object> updater = new HashMap<>();
                String replace = objDatum.get("name", String.class).replace("error", "");
                updater.put("name", replace);

                serviceFacade.updateWithMap(User.systemUser("84966"), objDatum, updater);

            } catch (Exception e) {
            }
        }
    }

}
