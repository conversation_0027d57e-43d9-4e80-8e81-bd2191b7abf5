package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.mengniu.business.abstraction.InterconnectEnterprisesService;
import com.facishare.crm.fmcg.tpm.api.activity.EnableList;
import com.facishare.crm.fmcg.tpm.api.activity.TPMActivityScript;
import com.facishare.crm.fmcg.tpm.api.cost.CalculateAmount;
import com.facishare.crm.fmcg.tpm.api.proof.Enable;
import com.facishare.crm.fmcg.tpm.business.TPMTriggerActionService;
import com.facishare.crm.fmcg.tpm.web.contract.TriggerAction;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.FunctionPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.enterpriserelation2.arg.BatchGetOuterTenantIdByEaArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.result.BatchGetOuterTenantIdByEaResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2023/4/20 16:15
 */
public class TriggerControllerTest extends BaseTest {


    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private TPMTriggerActionService triggerActionService;

    @Resource
    private InterconnectEnterprisesService interconnectEnterprisesService;

    @Resource
    private PromotionPolicyService promotionPolicyService;

    @Autowired
    private PublicEmployeeService publicEmployeeService;

    @Autowired
    private MetadataActionService metadataActionService;

    @Autowired
    private FxiaokeAccountService fxiaokeAccountService;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private FuncClient funcClient;

    @Test
    public void testTriggerEnable() {
        Enable.Arg arg = new Enable.Arg();
        arg.setStoreId("644202cf8d392e00019e3a04");
        arg.setUseCache(false);

        ControllerContext context = new ControllerContext(RequestContext.builder().tenantId("88417").user(User.builder().tenantId("88417").userId("1000").build()).build(), ApiNames.TPM_ACTIVITY_PROOF_OBJ, "Enable");
        long time = System.currentTimeMillis();
        System.out.println(serviceFacade.triggerController(context, arg, Enable.Result.class));
        System.out.println(System.currentTimeMillis() - time);
    }

    @Test
    public void testTriggerAction() {

        IObjectData data = serviceFacade.findObjectData(User.systemUser("84931"), "67614a016ac8df00019e9e91", ApiNames.TPM_BUDGET_PROVISION_OBJ);
        IObjectData dataCopy = ObjectDataExt.of(data).copy();
        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put(TPMBudgetProvisionObjFields.PROVISION_ERROR_MESSAGE, "ce shi copy again");
        serviceFacade.updateWithMap(User.systemUser("84931"), data, updateMap);
        IObjectDescribe objectDescribe = serviceFacade.findObject("84931", ApiNames.TPM_BUDGET_PROVISION_OBJ);
        serviceFacade.log(User.systemUser("84931"), EventType.MODIFY, ActionType.Modify, objectDescribe, data, updateMap, dataCopy);
    }

    @Test
    public void testTriggerScript() {
        TPMActivityScript.Arg arg = new TPMActivityScript.Arg();
        arg.setTenantIds(Lists.newArrayList("85494", "90432"));
        arg.setModule("init_close_activity_agreement_button");
        RequestContext build = RequestContext.builder().tenantId("85494").user(User.builder().tenantId("85494").userId("1000").build()).build();
        ControllerContext context = new ControllerContext(build, ApiNames.TPM_ACTIVITY_OBJ, "Script");
        long time = System.currentTimeMillis();
        System.out.println(serviceFacade.triggerController(context, arg, TPMActivityScript.Result.class));
        System.out.println(System.currentTimeMillis() - time);
    }

    @Test
    public void testCalculateAmount() {
        CalculateAmount.Arg arg = new CalculateAmount.Arg();
//        arg.setEnd();
//        arg.setBegin();
        arg.setActivityId("64097ff58ebef10001737845");
        arg.setDealerId("63ad77a92752cb0001e98669");
        ControllerContext context = new ControllerContext(RequestContext.builder()
                .tenantId("85494").user(User.builder().tenantId("85494").userId("1000").build())
                .build(), ApiNames.TPM_DEALER_ACTIVITY_COST, "CalculateAmount");

        System.out.println(serviceFacade.triggerController(context, arg, CalculateAmount.Result.class));

    }

    @Test
    public void testTriggerActionV2() {
        EnableList.Arg arg = new EnableList.Arg();
        arg.setVisitId("67e666339e2d5e3d80a2eff9");
        arg.setStoreId("67e11be999437f000130ec62");
        arg.setActionId("67e666339e2d5e3d80a2effc");
        arg.setVisitStatus("3");
        arg.setActivityIds(Lists.newArrayList("63ae86dd1a9bb700018b09d2"));
        arg.setActivityTypeList(Lists.newArrayList());

        ControllerContext context = new ControllerContext(RequestContext.builder().tenantId("83150").user(User.builder().tenantId("83150").userId("1000").build()).build(), ApiNames.TPM_ACTIVITY_OBJ, "EnableList");
        long time = System.currentTimeMillis();
        System.out.println(serviceFacade.triggerController(context, arg, EnableList.Result.class));
        System.out.println(System.currentTimeMillis() - time);
    }

    @Test
    public void testAddActivity() {

//        IObjectData dataIgnoreAll = serviceFacade.findObjectDataIgnoreAll(User.systemUser("84931"), "64a51725c3395e000170002b", ApiNames.TPM_ACTIVITY_OBJ);
//
//        dataIgnoreAll.set("extend_obj_data_id", null);
//        dataIgnoreAll.set("id", null);
//        dataIgnoreAll.set("_id", null);
//        dataIgnoreAll.set("name", "测试test--38");
//        dataIgnoreAll.set("activity_type", "64a6379bc5d72b000159a342");
//        dataIgnoreAll.set("product_gift_data_json", "{\"ladder\":\"common\",\"cycle_status\":\"noCycle\",\"isAdd\":\"add\",\"quantityOrPrice\":\"price\",\"mode_type\":\"master_reduce\",\"amortize\":\"noAmortize\",\"groupings\":[{\"condition\":[{\"field_name\":\"order_time\",\"field_name__s\":\"下单日期\",\"operator\":\"EQ\",\"operator__s\":\"等于\",\"field_values\":[*************],\"field_values__s\":\"2023-07-05\",\"type\":\"date\",\"object_api_name\":\"SalesOrderObj\",\"object_api_name__s\":\"销售订单\",\"field_name_type\":\"field\",\"field_value_type\":\"value\",\"operator_name\":\"等于\"}],\"anyOrAll\":\"\",\"amountOrSubtotal\":\"\",\"groupIndex\":\"1\",\"ladders\":[{\"ladder_index\":\"1\",\"gift_total_num\":\"\",\"gift_kind_upper_limit\":\"\",\"gift_condition_unit_id\":\"\",\"gift_condition_unit__s\":\"\",\"quantity\":\"1\",\"priceOrDiscount\":\"1\",\"gift_type\":\"\",\"gift_lists\":[{\"category_name\":\"\",\"category_id\":\"\",\"is_multiple_unit\":\"\",\"product_name\":\"\",\"product_id\":\"\",\"max_quantity\":\"\",\"min_quantity\":\"\",\"required\":\"1\",\"unit_id\":\"\",\"unit__s\":\"\"}],\"gift_conditions\":[{\"category_name\":\"\",\"category_id\":\"\",\"is_multiple_unit\":\"\"}],\"price_policy_rule_id\":\"\"}],\"condition_category_product\":{\"categoryOrProduct\":\"product\",\"category_products\":[]}}],\"object_api_name\":\"SalesOrderObj\"}");
//        dataIgnoreAll.set(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, null);
//        dataIgnoreAll.set(TPMActivityFields.SOURCE_OBJECT_API_NAME, ApiNames.SALES_ORDER_OBJ);
//        dataIgnoreAll.set(TPMActivityFields.MODE_TYPE, "details_reduce");


        IObjectData objectData = new ObjectData();
        // 对象id
        objectData.setId("659ac28751e8500001995b57");
        objectData.setTenantId("84931");
        objectData.setDescribeApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        objectData.set(TPMActivityAgreementFields.DESCRIPTION, "789");


        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Edit")
                .apiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ)
                .objectData(objectData)
                .user(User.systemUser("84931"))
                .triggerWorkflow(false)
                .triggerFlow(false)
                .build();
        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(arg);

    }


    @Test
    public void createPrice() {

        IObjectData dataIgnoreAll = serviceFacade.findObjectDataIgnoreAll(User.systemUser("84931"), "64a51725c3395e000170002b", ApiNames.TPM_ACTIVITY_OBJ);


        String json = "{\"ladder\":\"common\",\"cycle_status\":\"noCycle\",\"isAdd\":\"add\",\"quantityOrPrice\":\"price\",\"mode_type\":\"master_reduce\",\"amortize\":\"noAmortize\",\"groupings\":[{\"condition\":[{\"field_name\":\"resource\",\"field_name__s\":\"来源\",\"operator\":\"EQ\",\"operator__s\":\"等于\",\"field_values\":[\"2\"],\"field_values__s\":\"快消订货\",\"type\":\"select_one\",\"object_api_name\":\"SalesOrderObj\",\"object_api_name__s\":\"销售订单\",\"field_name_type\":\"field\",\"field_value_type\":\"value\",\"operator_name\":\"等于\"}],\"anyOrAll\":\"\",\"amountOrSubtotal\":\"\",\"groupIndex\":\"1\",\"ladders\":[{\"ladder_index\":\"1\",\"gift_total_num\":\"\",\"gift_kind_upper_limit\":\"\",\"gift_condition_unit_id\":\"\",\"gift_condition_unit__s\":\"\",\"quantity\":\"12\",\"priceOrDiscount\":\"1\",\"gift_type\":\"\",\"gift_lists\":[{\"category_name\":\"\",\"category_id\":\"\",\"is_multiple_unit\":\"\",\"product_name\":\"\",\"product_id\":\"\",\"max_quantity\":\"\",\"min_quantity\":\"\",\"required\":\"1\",\"unit_id\":\"\",\"unit__s\":\"\"}],\"gift_conditions\":[{\"category_name\":\"\",\"category_id\":\"\",\"is_multiple_unit\":\"\"}],\"price_policy_rule_id\":\"\"}],\"condition_category_product\":{\"categoryOrProduct\":\"product\",\"category_products\":[]}}],\"object_api_name\":\"SalesOrderObj\"}";
        JSONObject.parse(json);

        dataIgnoreAll.setOwner(Lists.newArrayList("1000"));
        dataIgnoreAll.set(TPMActivityFields.SOURCE_OBJECT_API_NAME, "SalesOrderObj");
        dataIgnoreAll.set(PricePolicyFields.ACTIVITY_ID, "64a63e69e61e02269705de83");
        dataIgnoreAll.set(PricePolicyFields.START_DATE, "*************");
        dataIgnoreAll.set(PricePolicyFields.END_DATE, "*************");
        dataIgnoreAll.set(PricePolicyFields.NAME, "测试test--003");
        dataIgnoreAll.set("product_gift_data_json", JSONObject.parse(json));
        dataIgnoreAll.set(PricePolicyFields.ACCOUNT_RANGE, "{\"type\":\"ALL\",\"value\":\"ALL\"}");
        dataIgnoreAll.set(PricePolicyFields.PRIORITY, "2");
        dataIgnoreAll.set(PricePolicyFields.MODE_TYPE, "master_reduce");
        dataIgnoreAll.set(PricePolicyFields.ACTIVE_STATUS, "enable");
        dataIgnoreAll.set(PricePolicyFields.MODIFY_TYPE, "master");
        dataIgnoreAll.set(PricePolicyFields.CALCULATE_STATUS, "0");


        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Add")
                .apiName(ApiNames.PRICE_POLICY_OBJ)
                .objectData(dataIgnoreAll)
                .user(User.systemUser("84931"))
                .build();
        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(arg);
    }

    @Test
    public void createTest() {
//        IObjectData objectData = serviceFacade.findObjectDataIgnoreAll(User.systemUser("85494"), "64aff05198a0b50001c3ccf0", ApiNames.TPM_ACTIVITY_OBJ);
//
//        promotionPolicyService.createSFAPromotionPolicy("85494", "1000", objectData);

        HeaderObj header = HeaderObj.newInstance(Integer.parseInt("89712"));

        String toAccount = eieaConverter.enterpriseIdToAccount(Integer.parseInt("89712"));

        BatchGetOuterTenantIdByEaArg eaArg = new BatchGetOuterTenantIdByEaArg();
        eaArg.setEas(Lists.newArrayList(toAccount));
        BatchGetOuterTenantIdByEaResult eaResult = fxiaokeAccountService.batchGetOuterTenantIdByEa(header, eaArg).getData();
        Map<String, Long> outerTenantIdMap = eaResult.getEa2OuterTenantIdMap();
        String outTenantId = outerTenantIdMap.get(toAccount).toString();
        System.out.println(outTenantId);

        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj
                = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.parseInt("89646"), -10000);

        ActionAddArg arg = new ActionAddArg();
        Map<String, Object> map = new HashMap<>();
        map.put("outer_tenant_id", outTenantId);
        map.put("contract_id", "653627ad64d24900011872b8");
        map.put("outer_role_ids", Lists.newArrayList("00000000000000000000000000123456"));
        com.fxiaoke.crmrestapi.common.data.ObjectData convert = com.fxiaoke.crmrestapi.common.data.ObjectData.convert(map);
        arg.setObjectData(convert);
        Result<ActionAddResult> addResultResult = metadataActionService.add(headerObj, ApiNames.PUBLIC_EMPLOYEE_OBJ, false, arg);
        if (addResultResult.getCode() != 0) {
            throw new MetaDataBusinessException(addResultResult.getMessage(), addResultResult.getCode());
        }
    }

    @Test
    public void updateTest() {


//        interconnectEnterprisesService.triggerInterconnection("89724", "300110496", ApiNames.ENTERPRISE_RELATION_OBJ, "6537aaadf23cdb00012ac48b", "89730");

        // 创建联系人、并关联新建的客户
        // 创建互联用户的联系人必填字段  name mobile 客户id

        String MENG_NIU_TENANT_NAME = "蒙牛1端[假]5";

        System.out.println(Boolean.FALSE.equals(false));


    }

    @Test
    public void authCode() {
        AuthContext authContext = AuthContext.builder().userId("-10000").tenantId("84931").appId("CRM").build();
        List<FunctionPojo> functionPojos = funcClient.queryFuncAccessByRole(authContext, "00000000000000000000000000123456");
        System.out.println(functionPojos);
        Set<String> funcCode = functionPojos.stream().map(FunctionPojo::getFuncCode).collect(Collectors.toSet());
        funcClient.updateRoleFuncAccess(authContext, "00000000000000000000000000123456", funcCode);
    }

    @Test
    public void initChannelWebNavigationWithLinkAppOpenTest() {
        String linkAppId = ConfigFactory.getConfig("fs-appserver-openproxy-appinfo").get("tpm_link_app_id");
        interconnectEnterprisesService.initChannelWebNavigationWithLinkAppOpen(89724, linkAppId);
    }
}
