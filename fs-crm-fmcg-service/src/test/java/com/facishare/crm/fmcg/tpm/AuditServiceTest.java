package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IAuditDataService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IAuditService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/24 16:53
 */
public class AuditServiceTest extends BaseTest {


    @Resource
    private IAuditService auditService;

    @Resource
    private IAuditDataService auditDataService;

    @Resource
    private ServiceFacade serviceFacade;

    @Test
    public void activityTypeReportTest() {
        AuditActivityTypeList.Arg arg = new AuditActivityTypeList.Arg();
        AuditActivityTypeList.Result result = auditService.activityTypeList(arg);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.getActivityTypeList().size() >= 1);
    }

    @Test
    public void listHeaderTest() {
        AuditListHeader.Arg arg = new AuditListHeader.Arg();
        arg.setActivityTypeId("61e6613b079a1d137e12dcd8");
        AuditListHeader.Result result = auditService.listHeader(arg);

        Assert.assertNotNull(result);
    }

    @Test
    public void initFilterTest() {
        InitFilter.Arg arg = new InitFilter.Arg();
        arg.setActivityTypeId("61e6613b079a1d137e12dcd8");

        InitFilter.Result result = auditService.initFilter(arg);

        Assert.assertNotNull(result);
    }

    @Test
    public void listTest() {
        ApiContext context = ApiContextManager.getContext();
        context.setTenantId("89646");
        context.setTenantAccount("89646");

        AuditList.Arg arg = new AuditList.Arg();
        //643e494ef68bef0001b4e886
        //646585544bd6fc0001406e8c
        arg.setActivityTypeId("652cfd124990030001c05add");
        arg.setDealerId("all");
        arg.setActivityId("all");
        arg.setBeginDate(1666800000000L);
        arg.setEndDate(1698335999999L);
        arg.setActivityStatus("in_progress");
        arg.setActivityClosedStatus("unclosed");

        AuditList.NumberFilterVO f1 = new AuditList.NumberFilterVO();
        f1.setFieldKey("unaudited_proof_count");
        f1.setOperator("GT");
        f1.setFieldValues(Lists.newArrayList(0));
        f1.setIsAsc(false);

        AuditList.NumberFilterVO f2 = new AuditList.NumberFilterVO();
        f2.setFieldKey("unaudited_proof_count");
        f2.setOperator("GT");
        f2.setFieldValues(Lists.newArrayList(0));
        f2.setIsAsc(false);

        arg.setNumberFilterList(Lists.newArrayList(
                f1
        ));

        arg.setOffset(0L);
        arg.setLimit(20L);

        AuditList.Result result = auditService.list(arg);

        Assert.assertNotNull(result);
    }

    @Test
    public void auditSummaryListTest() {
        AuditSummaryList.Arg arg = new AuditSummaryList.Arg();
        arg.setActivityId("61e53f9189e7f70001c96cd3");
        arg.setDealerId("61e53804cc30640001afb97c");
        arg.setStoreId("");
        arg.setBeginDate(1616256000000L);
        arg.setEndDate(1647878399999L);
        arg.setMinimumProofCount(0L);
        arg.setViewMode("view");
        arg.setOffset(0L);
        arg.setLimit(20L);

        System.out.println(JSON.toJSONString(arg));

        AuditSummaryList.Result result = auditDataService.auditSummaryList(arg);

        assert result != null;
    }

    @Test
    public void dataListTest() {
        AuditDataList.Arg arg = new AuditDataList.Arg();
        arg.setActivityId("61e53f9189e7f70001c96cd3");
        arg.setDealerId("61e53804cc30640001afb97c");
        arg.setId("61e53804cc30640001afb97c");
        arg.setDataIdList(Lists.newArrayList("61e6342b89e7f70001cade2d", "61e6844e299f780001b688b3"));
        arg.setViewMode("audit");
        arg.setNeedReturnDescribeAndLayout(false);

        System.out.println(JSON.toJSONString(arg));

        AuditDataList.Result result = auditDataService.dataList(arg);

        assert result != null;
    }

    @Test
    public void aggTest() {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList("61cd80b863d4320001e5f973"));

        Filter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName("dealer_id__c");
        dealerIdFilter.setOperator(Operator.EQ);
        dealerIdFilter.setFieldValues(Lists.newArrayList("5fa645e2c256690001a0e387"));

        query.setFilters(Lists.newArrayList(activityIdFilter, dealerIdFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser("80063"),
                query,
                "udef_proof__c",
                Lists.newArrayList("activity_id__c", "dealer_id__c", "store_id__c"),
                "avg",
                "cost__c");
        BigDecimal value;
        if (!CollectionUtils.isEmpty(data)) {
            String key = String.format("avg_%s", "cost__c");
            value = data.get(0).get(key, BigDecimal.class);
        } else {
            value = new BigDecimal("0");
        }

        assert value != null;
    }
}

