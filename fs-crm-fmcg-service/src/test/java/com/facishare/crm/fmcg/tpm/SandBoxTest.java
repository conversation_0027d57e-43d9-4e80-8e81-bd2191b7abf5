package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.web.contract.ActivitySandBox;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTemplateCopy;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IActivitySandBoxService;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IActivityTemplateCopyService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * author: wuyx
 * description:
 * createTime: 2022/3/30 17:05
 */
public class SandBoxTest extends BaseTest {

    @Resource
    private IActivitySandBoxService activitySandBoxService;

    @Resource
    private IActivityTemplateCopyService activityTemplateCopyService;

    @Test
    public void sandBoxTest() {
        ActivitySandBox.Arg arg = new ActivitySandBox.Arg();
        arg.setSourceEA("80063");
        arg.setTargetEA("78612");

        activitySandBoxService.sandBoxCopy(arg);
    }

    @Test
    public void templateCopyTest() {
        ActivityTemplateCopy.Arg arg = new ActivityTemplateCopy.Arg();
        arg.setSourceEI(84274);
        arg.setTargetEI(78612);

        activityTemplateCopyService.copyTPMOfTemplate(arg);
    }

    @Test
    public void copyActivityCommonSetTest() {
        activitySandBoxService.copyConfig(80063, 84931);
    }

}
