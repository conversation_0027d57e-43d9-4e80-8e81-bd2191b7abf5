{"return_type": "currency", "describe_api_name": "ReturnedGoodsInvoiceObj", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "PaymentObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "returned_goods_invoice_id", "is_index": true, "default_result": "d_zero", "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "amount", "label": "换货结算金额", "count_to_zero": false, "api_name": "exchange_settlement_amount", "count_field_type": "currency", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}