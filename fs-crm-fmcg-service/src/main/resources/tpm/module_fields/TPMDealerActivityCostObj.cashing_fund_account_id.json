{"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "status", "field_values": ["true"]}]}], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "兑付入账账户", "target_api_name": "FundAccountObj", "target_related_list_name": "target_related_list_TPMDealerActivityCostObj_FundAccountObj__c", "target_related_list_label": "经销商费用核销入账", "action_on_target_delete": "set_null", "is_need_convert": false, "related_wheres": [], "api_name": "cashing_fund_account_id", "help_text": "", "status": "released"}