package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.CustomBudgetFreeze;
import com.facishare.crm.fmcg.tpm.web.custom.abstraction.ICustomBudgetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/9 16:12
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/CustomBudget", produces = "application/json")
public class CustomBudgetController {

    @Resource
    private ICustomBudgetService customBudgetService;

    @PostMapping(value = "Freeze")
    public CustomBudgetFreeze.Result freeze(@RequestBody CustomBudgetFreeze.Arg arg) {
        return customBudgetService.freeze(arg);
    }
}