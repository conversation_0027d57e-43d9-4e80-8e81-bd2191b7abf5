package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.PromotionPolicyEdit;
import com.facishare.crm.fmcg.tpm.web.contract.PromotionPolicyGet;
import com.facishare.crm.fmcg.tpm.web.contract.StatisticPolicyOccupy;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPromotionPolicyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/6/29 14:07
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/PromotionPolicy", produces = "application/json")
public class PromotionPolicyController {

    @Resource
    private IPromotionPolicyService promotionPolicyService;

    @PostMapping(value = "/Get")
    public PromotionPolicyGet.Result get(@RequestBody PromotionPolicyGet.Arg arg) {
        return promotionPolicyService.get(arg);
    }

    @PostMapping(value = "/EnableEditPromotionPolicy")
    public PromotionPolicyEdit.Result enableEditPromotionPolicy(@RequestBody PromotionPolicyEdit.Arg arg) {
        return promotionPolicyService.enableEditPromotionPolicy(arg);
    }

    @PostMapping(value = "/UpdateSFAPromotionPolicyTest")
    public void updateSFAPromotionPolicyTest(@RequestParam String id) {
        promotionPolicyService.updateSFAPromotionPolicyTest(id);
    }

    @PostMapping(value = "/CreateSFAPromotionPolicyTest")
    public void createSFAPromotionPolicyTest(@RequestParam String id) {
        promotionPolicyService.createSFAPromotionPolicyTest(id);
    }

    @PostMapping(value = "/StatisticPolicyOccupy")
    public StatisticPolicyOccupy.Result statisticPolicyOccupy(@RequestBody StatisticPolicyOccupy.Arg arg) {
        return promotionPolicyService.statisticPolicyOccupy(arg);
    }

}
