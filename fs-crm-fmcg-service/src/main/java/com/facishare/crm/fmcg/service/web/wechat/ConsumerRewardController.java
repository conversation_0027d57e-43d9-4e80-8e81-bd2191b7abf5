package com.facishare.crm.fmcg.service.web.wechat;

import com.facishare.crm.fmcg.tpm.api.scan.*;
import com.facishare.crm.fmcg.tpm.reward.dto.ConsumerReward;
import com.facishare.crm.fmcg.tpm.reward.handler.ConsumerRewardHandler;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPhysicalRewardService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IScanCodeService;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/ConsumerReward", produces = "application/json")
public class ConsumerRewardController {

    @Resource
    private IScanCodeService scanCodeService;
    @Resource
    private ConsumerRewardHandler consumerRewardHandler;
    @Resource
    private IPhysicalRewardService physicalRewardService;


    @PostMapping(value = "{environment}/Reward")
    public ConsumerReward.Result reward(@PathVariable String environment, @RequestBody ConsumerReward.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        log.info("reward arg: {}", arg);
        return consumerRewardHandler.handle(arg);
    }

    @PostMapping(value = "{environment}/CodeInformation")
    public ConsumerScanInnerCode.Result codeInformation(@PathVariable String environment, @RequestBody ConsumerScanInnerCode.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        if (Strings.isNullOrEmpty(arg.getSource())) {
            arg.setSource("plugin");
        }
        return scanCodeService.consumerScanCode(arg);
    }

    @PostMapping(value = "{environment}/CodeType")
    public ConsumerScanCodeType.Result codeType(@PathVariable String environment, @RequestBody ConsumerScanCodeType.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        return scanCodeService.codeType(arg);
    }

    @PostMapping(value = "{environment}/CodeRewarded")
    public CodeRewarded.Result codeRewarded(@PathVariable String environment, @RequestBody CodeRewarded.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        return scanCodeService.rewarded(arg);
    }

    @PostMapping(value = "{environment}/EnableReward")
    public ConsumerScanEnableReward.Result enableReward(@PathVariable String environment, @RequestBody ConsumerScanEnableReward.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        return scanCodeService.enableReward(arg);
    }

    @PostMapping(value = "{environment}/CheckLock")
    public ConsumerScanCheckLock.Result checkLock(@PathVariable String environment, @RequestBody ConsumerScanCheckLock.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        return scanCodeService.checkLock(arg);
    }

    @PostMapping(value = "{environment}/Unlock")
    public ConsumerScanUnlock.Result unlock(@PathVariable String environment, @RequestBody ConsumerScanUnlock.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        return scanCodeService.unlock(arg);
    }

    @PostMapping("/{environment}/consumer_reward_list")
    public ConsumerRewardList.Result consumerRewardList(@PathVariable String environment, @RequestBody ConsumerRewardList.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        if (Strings.isNullOrEmpty(arg.getWxToken())) {
            arg.setWxToken(arg.getToken());
        }
        return physicalRewardService.consumerRewardList(arg);
    }

    @PostMapping("/{environment}/get_physical_item_info")
    public GetPhysicalItemInfo.Result getPhysicalItemInfo(@PathVariable String environment, @RequestBody GetPhysicalItemInfo.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        if (Strings.isNullOrEmpty(arg.getWxToken())) {
            arg.setWxToken(arg.getToken());
        }
        return physicalRewardService.getPhysicalItemInfo(arg);
    }

    @RequestMapping("/{environment}/physical_item_write_off")
    public PhysicalItemWriteOff.Result physicalItemWriteOff(@PathVariable String environment, @RequestBody PhysicalItemWriteOff.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        if (Strings.isNullOrEmpty(arg.getWxToken())) {
            arg.setWxToken(arg.getToken());
        }
        return physicalRewardService.physicalItemWriteOff(arg);
    }

    @RequestMapping("/{environment}/fill_mail_info_for_physical_item")
    public FillMailInfoForPhysicalItem.Result fillMailInfoForPhysicalItem(@PathVariable String environment, @RequestBody FillMailInfoForPhysicalItem.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        if (Strings.isNullOrEmpty(arg.getWxToken())) {
            arg.setWxToken(arg.getToken());
        }
        return physicalRewardService.fillMailInfoForPhysicalItem(arg);
    }

    @RequestMapping("/{environment}/query_write_off_qr_code_status")
    public QueryWriteOffQrCodeStatus.Result queryWriteOffQrCodeStatus(@PathVariable String environment, @RequestBody QueryWriteOffQrCodeStatus.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        return physicalRewardService.queryWriteOffQrCodeStatus(arg);
    }
}