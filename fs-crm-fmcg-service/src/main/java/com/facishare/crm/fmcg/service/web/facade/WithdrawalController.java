package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.api.withdraw.*;
import com.facishare.crm.fmcg.tpm.web.service.WithdrawService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/15 17:12
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/Withdrawal", produces = "application/json")
public class WithdrawalController {

    @Resource
    private WithdrawService withdrawService;

    @RequestMapping(value = "Dashboard")
    public Dashboard.Result dashboard(@RequestBody Dashboard.Arg arg) {
        return withdrawService.dashboard(arg);
    }

    @RequestMapping(value = "QueryRedPacketRecords")
    public QueryRedPacketRecords.Result queryRedPacketRecords(@RequestBody QueryRedPacketRecords.Arg arg) {
        return withdrawService.queryRedPacketRecords(arg);
    }

    @RequestMapping(value = "QueryWithdrawRecords")
    public QueryWithdrawRecords.Result queryWithdrawRecords(@RequestBody QueryWithdrawRecords.Arg arg) {
        return withdrawService.queryWithdrawRecords(arg);
    }

    @RequestMapping(value = "PreviewWithdraw")
    public PreviewWithdraw.Result previewWithdraw(@RequestBody PreviewWithdraw.Arg arg) {
        return withdrawService.previewWithdraw(arg);
    }

    @RequestMapping(value = "SubmitWithdraw")
    public SubmitWithdraw.Result submitWithdraw(@RequestBody SubmitWithdraw.Arg arg) {
        return withdrawService.submitWithdraw(arg);
    }

    @RequestMapping(value = "PersonAuthIdCard")
    public PersonAuthIdCard.Result personAuthIdCard(@RequestBody PersonAuthIdCard.Arg arg) {
        return withdrawService.personAuthIdCard(arg);
    }

    @RequestMapping(value = "PersonAlertRecord")
    public PersonAlertRecord.Result personAlertRecord(@RequestBody PersonAlertRecord.Arg arg) {
        return withdrawService.personAlertRecord(arg);
    }
}
