package com.facishare.crm.fmcg.service.web.inner;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import com.facishare.crm.fmcg.tpm.business.dto.FilterDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.CommonDAO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/10/31 17:55
 */

@RequestMapping("mongo")
@RestController
public class CommonMongoController {

    @Resource
    private CommonDAO commonDAO;


    @RequestMapping("query")
    public List query(@RequestBody JSONObject arg) {
        return commonDAO.query(arg.getString("clazz_name"), arg.getJSONArray("filters").toJavaList(FilterDTO.class), arg.getInteger("limit"), arg.getInteger("offset"));
    }

    @RequestMapping("update")
    public InnerApiResult<String> update(@RequestBody JSONObject arg) {
        return InnerApiResult.apply((arg2) -> {
            commonDAO.update(arg.getString("tenant_id"), arg.getString("clazz_name"), arg.getString("unique_id"), arg.getJSONObject("update_map"));
            return "success";
        }, arg);
    }

    @RequestMapping("batchAdd")
    public List batchAdd(@RequestBody JSONObject arg) {
        return commonDAO.add(arg.getString("tenant_id"), arg.getString("clazz_name"), arg.getJSONArray("dataList"));
    }

    @RequestMapping("delete")
    public InnerApiResult<Integer> delete(@RequestBody JSONObject arg) {
        return InnerApiResult.apply((arg2) -> Integer.valueOf(commonDAO.delete(arg.getString("clazz_name"), arg.getJSONArray("filters").toJavaList(FilterDTO.class))), arg);
    }
}
