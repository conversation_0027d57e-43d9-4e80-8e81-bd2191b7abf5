package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.LicenseAccessServiceGet;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.ILicenseAccessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by wuyx
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/LicenseAccess", produces = "application/json")
public class LicenseAccessController {

    @Resource
    private ILicenseAccessService licenseAccessService;

    @PostMapping(value = "Get")
    public LicenseAccessServiceGet.Result get(@RequestBody LicenseAccessServiceGet.Arg arg) {
        return licenseAccessService.get(arg);
    }
}