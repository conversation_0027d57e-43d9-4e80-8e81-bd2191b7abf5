package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.api.scan.ConsumerScanCheckLock;
import com.facishare.crm.fmcg.tpm.api.scan.ConsumerScanEnableReward;
import com.facishare.crm.fmcg.tpm.api.scan.ConsumerScanInnerCode;
import com.facishare.crm.fmcg.tpm.api.scan.ConsumerScanUnlock;
import com.facishare.crm.fmcg.tpm.reward.dto.ConsumerReward;
import com.facishare.crm.fmcg.tpm.reward.handler.ConsumerRewardHandler;
import com.facishare.crm.fmcg.tpm.web.service.ScanCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/RewardPlugin", produces = "application/json")
public class MengNiuRewardPluginController {

    @Resource
    private ScanCodeService scanCodeService;
    @Resource
    private ConsumerRewardHandler consumerRewardHandler;

    /**
     * 开启红包接口
     */
    @PostMapping(value = "{environment}/Reward")
    public ConsumerReward.Result reward(@PathVariable String environment, @RequestBody ConsumerReward.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSkipWxValidate(true);
        return consumerRewardHandler.handle(arg);
    }

    /**
     * 获取码信息接口，该接口返回的信息将用来开启红包
     */
    @PostMapping(value = "{environment}/CodeInformation")
    public ConsumerScanInnerCode.Result codeInformation(@PathVariable String environment, @RequestBody ConsumerScanInnerCode.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSource("plugin1");
        arg.setSkipWxValidate(true);
        return scanCodeService.consumerScanCode(arg);
    }

    /**
     * 传入二维码扫码结果，返回是否有活动
     */
    @PostMapping(value = "{environment}/EnableReward")
    public ConsumerScanEnableReward.Result enableReward(@PathVariable String environment, @RequestBody ConsumerScanEnableReward.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSkipWxValidate(true);
        return scanCodeService.enableReward(arg);
    }

    /**
     * 锁判断
     */
    @PostMapping(value = "{environment}/CheckLock")
    public ConsumerScanCheckLock.Result checkLock(@PathVariable String environment, @RequestBody ConsumerScanCheckLock.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSkipWxValidate(true);
        return scanCodeService.checkLock(arg);
    }

    /**
     * 解锁
     */
    @PostMapping(value = "{environment}/Unlock")
    public ConsumerScanUnlock.Result unlock(@PathVariable String environment, @RequestBody ConsumerScanUnlock.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSkipWxValidate(true);
        return scanCodeService.unlock(arg);
    }
}