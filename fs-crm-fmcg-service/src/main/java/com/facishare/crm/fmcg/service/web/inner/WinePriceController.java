package com.facishare.crm.fmcg.service.web.inner;

import com.facishare.crm.fmcg.ocr.api.IWinePriceService;
import com.facishare.crm.fmcg.ocr.api.contract.WinePriceDetect;
import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/9 16:21
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/WinePrice", produces = "application/json")
public class WinePriceController {

    @Resource
    private IWinePriceService winePriceService;

    @PostMapping(value = "Detect")
    public InnerApiResult<WinePriceDetect.Result> detect(@RequestBody WinePriceDetect.Arg arg) {
        return InnerApiResult.apply(winePriceService::detect, arg);
    }
}