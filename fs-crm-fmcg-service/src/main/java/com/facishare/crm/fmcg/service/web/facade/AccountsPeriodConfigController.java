package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.dms.model.GetAccountsPeriodConfigObjectInformation;
import com.facishare.crm.fmcg.dms.model.SaveAccountsPeriodConfigArg;
import com.facishare.crm.fmcg.dms.model.SaveAccountsPeriodConfigResult;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsPeriodConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/DMS/AccountsPeriodConfig", produces = "application/json")
public class AccountsPeriodConfigController {
    @Resource
    private IAccountsPeriodConfigService accountsPeriodConfigService;


    @PostMapping("/GetObjectInitializationInformation")
    public GetAccountsPeriodConfigObjectInformation.Result getObjectInitializationInformation(@RequestBody GetAccountsPeriodConfigObjectInformation.Arg arg) {

        return accountsPeriodConfigService.getAccountsPeriodConfigInitInformation(arg);
    }

    @PostMapping("/SaveAccountsPeriodConfig")
    public SaveAccountsPeriodConfigResult saveAccountsPeriodConfig(@RequestBody SaveAccountsPeriodConfigArg arg) {

        return accountsPeriodConfigService.saveAccountsPeriodConfig(arg);
    }

    @PostMapping("/GetAccountsPeriodConfig")
    public SaveAccountsPeriodConfigResult getAccountsPeriodConfig(@RequestBody GetAccountsPeriodConfigObjectInformation.Arg arg) {

        return accountsPeriodConfigService.getAccountsPeriodConfig(arg);
    }
}
