package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.service.BudgetNewConsumeRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/11/14 下午6:38
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/BudgetConsumeRule", produces = "application/json")
public class BudgetNewConsumeRuleController {

    @Resource
    private BudgetNewConsumeRuleService budgetNewConsumeRuleService;

    @PostMapping(value = "/Add")
    public AddBudgetConsumeRule.Result add(@RequestBody AddBudgetConsumeRule.Arg arg) {
        return budgetNewConsumeRuleService.add(arg);
    }

    @PostMapping(value = "/Get")
    public GetBudgetConsumeRule.Result get(@RequestBody GetBudgetConsumeRule.Arg arg) {
        return budgetNewConsumeRuleService.get(arg);
    }

    @PostMapping(value = "/Edit")
    public EditBudgetConsumeRule.Result edit(@RequestBody EditBudgetConsumeRule.Arg arg) {
        return budgetNewConsumeRuleService.edit(arg);
    }

    @PostMapping(value = "/List")
    public ListBudgetConsumeRule.Result List(@RequestBody ListBudgetConsumeRule.Arg arg) {
        return budgetNewConsumeRuleService.list(arg);
    }

    @PostMapping(value = "/SetRuleStatus")
    public SetBudgetConsumeRuleStatus.Result setRuleStatus(@RequestBody SetBudgetConsumeRuleStatus.Arg arg) {
        return budgetNewConsumeRuleService.setRuleStatus(arg);
    }

    @PostMapping(value = "/Delete")
    public DeleteBudgetConsumeRule.Result delete(@RequestBody DeleteBudgetConsumeRule.Arg arg) {
        return budgetNewConsumeRuleService.delete(arg);
    }

    @PostMapping(value = "/ListBusinessObject")
    public ListBusinessObject.Result ListBusinessObject(@RequestBody ListBusinessObject.Arg arg) {
        return budgetNewConsumeRuleService.listBusinessObject(arg);
    }

    @PostMapping(value = "/getConsumeObjects")
    public GetConsumeObject.Result getConsumeObjects(@RequestBody GetConsumeObject.Arg arg) {
        return budgetNewConsumeRuleService.getConsumeObjects(arg);
    }

    @PostMapping(value = "/DeletePlugin")
    public DeletePluginObject.Result deletePlugin(@RequestBody DeletePluginObject.Arg arg){
        return budgetNewConsumeRuleService.deletePlugin(arg);
    }
}
