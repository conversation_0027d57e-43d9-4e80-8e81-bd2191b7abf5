package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tongfu.AuthService;
import com.facishare.crm.fmcg.tongfu.model.Auth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:57
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/TongFu", produces = "application/json")
public class TongFuAuthController {

    @Resource
    private AuthService authService;

    @PostMapping(value = "Auth")
    public Auth.Result auth(@RequestBody Auth.Arg arg) {
        return authService.auth(arg);
    }
}