package com.facishare.crm.fmcg.service.web.task;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.task.ActivityNodeTemplateValidationTaskService;
import com.facishare.crm.fmcg.tpm.task.ActivityTypeValidationTaskService;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTask;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

@JobHander(value = "ActivityTaskController")
@Slf4j
@Component
public class ActivityTaskController extends IJobHandler {

    public static final String ACTIVITY_VALIDATION_TASK = "ACTIVITY_VALIDATION_TASK-%s";
    @Resource
    private ActivityNodeTemplateValidationTaskService activityNodeTemplateValidationTaskService;

    @Resource
    private ActivityTypeValidationTaskService activityTypeValidationTaskService;

    @Override
    public ReturnT<?> execute(TriggerParam params) {
        log.info("activity config validation task start : {}", JSON.toJSONString(params.getExecutorParams()));

        ActivityTask.Arg arg = JSON.parseObject(params.getExecutorParams(), ActivityTask.Arg.class);

        String taskTraceId = TraceContext.get().getTraceId();
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {

            TraceContext.get().setTraceId(StringUtils.isBlank(taskTraceId) ? String.format(ACTIVITY_VALIDATION_TASK, UUID.randomUUID()) : taskTraceId);
            try {
                activityNodeTemplateValidationTaskService.activityNodeTemplateListValidation(arg);
            } catch (Exception ex) {
                log.error("activity node template validation failed : ", ex);
            }

            try {
                activityTypeValidationTaskService.activityTypeValidation(arg);
            } catch (Exception ex) {
                log.error("activity type validation failed : ", ex);
            }
        })).run();

        return ReturnT.success("success");
    }
}
