package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.mengniu.api.RewardBizDescribe;
import com.facishare.crm.fmcg.mengniu.service.IObjectActionService;
import com.facishare.crm.fmcg.tpm.api.scan.ConsumerScanCheckLock;
import com.facishare.crm.fmcg.tpm.api.scan.ConsumerScanCodeType;
import com.facishare.crm.fmcg.tpm.api.scan.ConsumerScanInnerCode;
import com.facishare.crm.fmcg.tpm.api.scan.ConsumerScanUnlock;
import com.facishare.crm.fmcg.tpm.reward.dto.ConsumerReward;
import com.facishare.crm.fmcg.tpm.reward.handler.ConsumerRewardHandler;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IScanCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/TPM/MengNiu/Reward", produces = "application/json")
public class MengNiuRewardController {

    @Resource
    private IScanCodeService scanCodeService;
    @Resource
    private ConsumerRewardHandler consumerRewardHandler;
    @Resource
    private IObjectActionService objectActionService;

    @PostMapping(value = "{environment}/ConsumerScanCodeReward")
    public ConsumerReward.Result consumerScanCodeReward(@PathVariable String environment, @RequestBody ConsumerReward.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSkipWxValidate(true);
        return consumerRewardHandler.handle(arg);
    }

    @PostMapping(value = "{environment}/CodeInformation")
    public ConsumerScanInnerCode.Result codeInformation(@PathVariable String environment, @RequestBody ConsumerScanInnerCode.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSkipWxValidate(true);
        return scanCodeService.consumerScanCode(arg);
    }

    @PostMapping(value = "{environment}/CodeType")
    public ConsumerScanCodeType.Result codeType(@PathVariable String environment, @RequestBody ConsumerScanCodeType.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSkipWxValidate(true);
        return scanCodeService.codeType(arg);
    }

    @PostMapping(value = "{environment}/CheckLock")
    public ConsumerScanCheckLock.Result checkLock(@PathVariable String environment, @RequestBody ConsumerScanCheckLock.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSkipWxValidate(true);
        return scanCodeService.checkLock(arg);
    }

    @PostMapping(value = "{environment}/Unlock")
    public ConsumerScanUnlock.Result unlock(@PathVariable String environment, @RequestBody ConsumerScanUnlock.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setSkipWxValidate(true);
        return scanCodeService.unlock(arg);
    }

    @PostMapping(value = "BizDescribe")
    public RewardBizDescribe.Result bizDescribe(@RequestBody RewardBizDescribe.Arg arg) {
        return objectActionService.bizDescribe(arg);
    }

}
