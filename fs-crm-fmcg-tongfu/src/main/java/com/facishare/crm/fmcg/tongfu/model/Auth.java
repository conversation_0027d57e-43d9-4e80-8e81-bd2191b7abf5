package com.facishare.crm.fmcg.tongfu.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface Auth {

    @Data
    @ToString
    final class Arg implements Serializable {
    }

    @Data
    @ToString
    @Builder
    final class Result implements Serializable {

        private String token;

        @JSONField(name = "redirect_url")
        @JsonProperty(value = "redirect_url")
        @SerializedName("redirect_url")
        private String redirectUrl;
    }
}
